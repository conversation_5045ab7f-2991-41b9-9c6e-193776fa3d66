# -*- coding: utf-8 -*-
"""
积分变化图表生成器
负责生成积分获取和消耗的可视化图表
"""

import datetime
import logging
from typing import Dict, Any, List, Tuple
from pathlib import Path

# 导入matplotlib相关库并设置后端
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import numpy as np

# 导入查询模块
from api.query.Credit.CreditCountSql import CreditCountQueries

# 导入智能标签模块
from ..SmartLabels import add_smart_bar_labels

# 导入数据收集装饰器
from ...excel import collect_chart_data

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class CreditChangePicGenerator:
    """积分变化图表生成器"""

    def __init__(self, bid: str, image_manager):
        """
        初始化图表生成器

        Args:
            bid: 品牌ID
            image_manager: 图片管理器实例
        """
        self.bid = bid
        self.image_manager = image_manager

    def _extract_param(self, query_params, key, default=None):
        """
        从查询参数中提取值

        Args:
            query_params: 查询参数对象或字典
            key: 参数键
            default: 默认值

        Returns:
            参数值
        """
        if hasattr(query_params, key):
            return getattr(query_params, key)
        elif isinstance(query_params, dict):
            return query_params.get(key, default)
        else:
            return default

    async def generate_credit_change_charts(self, query_params) -> Dict[str, str]:
        """
        生成积分变化图表

        Args:
            query_params: 查询参数对象或字典

        Returns:
            Dict: 包含图片路径和AI分析的字典
        """
        try:
            logger.info(f"开始生成积分变化图表 - bid: {self.bid}")
            logger.info(f"查询参数类型: {type(query_params)}")
            logger.info(f"图片管理器会话目录: {self.image_manager.session_dir}")

            # 获取日期参数
            current_date = datetime.datetime.now()
            end_date = self._extract_param(query_params, 'end_date', '2025-06-30')
            query_end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d")

            logger.info(f"日期参数 - 当前日期: {current_date}, 查询结束日期: {query_end_date}")

            # 计算去年和今年的年份
            last_year = query_end_date.year - 1
            this_year = query_end_date.year

            # 去年全年12个月
            last_year_ranges = self._generate_monthly_ranges(last_year, 1, 12)

            # 今年从1月到查询结束月份（如果是当前年份则到昨天所在月份）
            if this_year == current_date.year:
                # 如果是当前年份，数据到昨天为止
                yesterday = current_date - datetime.timedelta(days=1)
                end_month = yesterday.month
            else:
                # 如果不是当前年份，使用查询结束月份
                end_month = query_end_date.month

            this_year_ranges = self._generate_monthly_ranges(this_year, 1, end_month)

            # 获取数据
            logger.info(f"开始获取数据 - 去年月份: {len(last_year_ranges)}个, 今年月份: {len(this_year_ranges)}个")
            last_year_data = await self._fetch_monthly_data(last_year_ranges, query_params)
            this_year_data = await self._fetch_monthly_data(this_year_ranges, query_params)

            logger.info(f"数据获取完成 - 去年: {len(last_year_data)}条, 今年: {len(this_year_data)}条")

            # 结果字典
            result = {}

            # 检查数据是否为空并生成错误图片
            if not last_year_data:
                logger.warning("去年数据为空，生成错误图片")
                error_path = self._generate_error_image("credit_change_last_year", "去年数据暂无")
                if error_path:
                    result["credit_change_last_year"] = error_path

            if not this_year_data:
                logger.warning("今年数据为空，生成错误图片")
                error_path = self._generate_error_image("credit_change_this_year", "今年数据暂无")
                if error_path:
                    result["credit_change_this_year"] = error_path

            logger.info(f"开始生成图表 - 去年: {len(last_year_data)}条, 今年: {len(this_year_data)}条")

            # 如果有数据则生成图表
            if last_year_data:
                last_year_path = await self._generate_chart(
                    last_year_data,
                    f"{last_year}年积分变化趋势",
                    "credit_change_last_year"
                )
                if last_year_path:
                    result["credit_change_last_year"] = last_year_path

            if this_year_data:
                this_year_path = await self._generate_chart(
                    this_year_data,
                    f"{this_year}年积分变化趋势",
                    "credit_change_this_year"
                )
                if this_year_path:
                    result["credit_change_this_year"] = this_year_path

            # AI分析
            try:
                from .CreditChangeAi import CreditChangeAiAnalyzer
                ai_analyzer = CreditChangeAiAnalyzer()

                logger.info("开始生成AI分析...")

                # 根据数据情况生成分析
                if last_year_data and this_year_data:
                    # 两年都有数据
                    logger.info("两年都有数据，生成完整AI分析")
                    ai_analysis = await ai_analyzer.generate_all_credit_change_analysis(this_year_data, last_year_data)
                    result.update(ai_analysis)
                elif this_year_data:
                    # 只有今年数据
                    logger.info("只有今年数据，生成今年AI分析")
                    this_year_analysis = await ai_analyzer.analyze_credit_change_this_year_data(this_year_data, [])
                    from .CreditChangePromt import get_default_report
                    result.update({
                        "credit_change_last_year_analysis_report": get_default_report("this_year_only", "last_year"),
                        "credit_change_this_year_analysis_report": this_year_analysis
                    })
                elif last_year_data:
                    # 只有去年数据
                    logger.info("只有去年数据，生成去年AI分析")
                    last_year_analysis = await ai_analyzer.analyze_credit_change_last_year_data(last_year_data)
                    from .CreditChangePromt import get_default_report
                    result.update({
                        "credit_change_last_year_analysis_report": last_year_analysis,
                        "credit_change_this_year_analysis_report": get_default_report("last_year_only", "this_year")
                    })
                else:
                    # 两年都没有数据
                    logger.warning("两年都没有数据，使用默认AI分析")
                    from .CreditChangePromt import get_default_report
                    result.update({
                        "credit_change_last_year_analysis_report": get_default_report("no_data", "last_year"),
                        "credit_change_this_year_analysis_report": get_default_report("no_data", "this_year")
                    })

                logger.info("AI分析生成完成")

            except Exception as ai_error:
                logger.error(f"AI分析失败: {ai_error}")
                import traceback
                traceback.print_exc()
                from .CreditChangePromt import get_default_report
                result.update({
                    "credit_change_last_year_analysis_report": get_default_report("ai_error", "last_year"),
                    "credit_change_this_year_analysis_report": get_default_report("ai_error", "this_year")
                })

            logger.info(f"积分变化图表生成完成，共 {len(result)} 个结果")
            return result

        except Exception as e:
            logger.error(f"生成积分变化图表失败: {e}")
            return {}

    def _generate_error_image(self, image_type: str, error_message: str) -> str:
        """
        生成错误提示图片

        Args:
            image_type: 图片类型
            error_message: 错误信息

        Returns:
            str: 图片文件路径
        """
        try:
            # 创建图表
            fig, ax = plt.subplots(figsize=(12, 8))
            ax.text(0.5, 0.5, f'数据获取失败\n{error_message}',
                   horizontalalignment='center', verticalalignment='center',
                   fontsize=20, color='red', weight='bold',
                   transform=ax.transAxes)
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')

            # 保存图片
            file_path = self.image_manager.get_image_path(image_type)
            plt.savefig(file_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close(fig)

            logger.info(f"错误图片已生成: {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"生成错误图片失败: {e}")
            return ""

    def _generate_monthly_ranges(self, year: int, start_month: int, end_month: int) -> List[Tuple[str, str, str]]:
        """
        生成月份日期范围

        Args:
            year: 年份
            start_month: 开始月份
            end_month: 结束月份

        Returns:
            List: [(月份标签, 开始日期, 结束日期), ...]
        """
        ranges = []

        for month in range(start_month, end_month + 1):
            # 月份第一天
            first_day = datetime.date(year, month, 1)

            # 月份最后一天（下个月第一天减一天）
            if month == 12:
                next_month_first = datetime.date(year + 1, 1, 1)
            else:
                next_month_first = datetime.date(year, month + 1, 1)

            last_day = next_month_first - datetime.timedelta(days=1)

            # 如果是当前年份的当前月份，最后一天设为昨天
            if year == datetime.datetime.now().year and month == datetime.datetime.now().month:
                yesterday = datetime.datetime.now().date() - datetime.timedelta(days=1)
                if yesterday < last_day:
                    last_day = yesterday

            month_label = f"{year}年{month}月"
            start_date = first_day.strftime("%Y-%m-%d")
            end_date = last_day.strftime("%Y-%m-%d")

            ranges.append((month_label, start_date, end_date))

        return ranges

    async def _fetch_monthly_data(self, time_ranges: List[Tuple[str, str, str]], query_params) -> List[Dict[str, Any]]:
        """
        获取月度积分数据

        Args:
            time_ranges: 时间范围列表
            query_params: 查询参数

        Returns:
            List: 月度数据列表
        """
        try:
            monthly_data = []
            bid = self._extract_param(query_params, 'bid')
            sid = self._extract_param(query_params, 'sid', None)

            for month_label, start_date, end_date in time_ranges:
                logger.info(f"获取 {month_label} 数据: {start_date} 到 {end_date}")

                # 转换日期格式为数据库格式（YYYYMMDD）
                start_date_db = start_date.replace('-', '')
                end_date_db = end_date.replace('-', '')

                try:
                    # 1. 获取积分奖励总数
                    credit_reward = await CreditCountQueries.get_total_credit_reward(
                        start_date_db, end_date_db, bid, sid
                    )
                    credit_reward = int(credit_reward) if credit_reward else 0
                    # 过滤负值
                    credit_reward = max(0, credit_reward)

                    # 2. 获取积分消耗总数
                    credit_consume = await CreditCountQueries.get_total_credit_consume(
                        start_date_db, end_date_db, bid, sid
                    )
                    credit_consume = int(credit_consume) if credit_consume else 0
                    # 过滤负值
                    credit_consume = max(0, credit_consume)

                    logger.info(f"{month_label} 数据: 奖励{credit_reward}, 消耗{credit_consume}")

                    monthly_data.append({
                        'month': month_label,
                        'credit_reward': credit_reward,
                        'credit_consume': credit_consume
                    })

                except Exception as month_error:
                    logger.error(f"获取 {month_label} 数据失败: {month_error}")
                    # 失败时添加零数据保持连续性
                    monthly_data.append({
                        'month': month_label,
                        'credit_reward': 0,
                        'credit_consume': 0
                    })

            return monthly_data

        except Exception as e:
            logger.error(f"获取月度数据失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    @collect_chart_data("credit_change", include_comparison=True)
    async def _generate_chart(self, data: List[Dict[str, Any]], title: str, image_type: str) -> str:
        """
        生成积分变化图表

        Args:
            data: 月度数据列表
            title: 图表标题
            image_type: 图片类型

        Returns:
            str: 图片文件路径
        """
        try:
            if not data:
                logger.warning(f"数据为空，无法生成图表: {image_type}")
                return ""

            # 创建图表
            fig, ax = plt.subplots(figsize=(14, 8))

            # 准备数据（确保非负值）
            months = [item['month'] for item in data]
            credit_rewards = [max(0, item['credit_reward']) for item in data]
            credit_consumes = [max(0, item['credit_consume']) for item in data]

            # 设置X轴位置
            x_pos = range(len(months))

            # 绘制双柱状图
            bar_width = 0.35
            bars1 = ax.bar([x - bar_width/2 for x in x_pos], credit_rewards,
                          bar_width, label='获取积分', color='#E74C3C', alpha=0.8)
            bars2 = ax.bar([x + bar_width/2 for x in x_pos], credit_consumes,
                          bar_width, label='消耗积分', color='#4472C4', alpha=0.8)

            # 设置图表标签（不显示标题，图注会在上方）
            ax.set_xlabel('月份', fontsize=12)
            ax.set_ylabel('积分数', fontsize=12)

            ax.set_xticks(x_pos)
            # 简化月份标签，只显示月份数字
            month_labels = [month.split('年')[-1] if '年' in month else month for month in months]
            ax.set_xticklabels(month_labels, rotation=0, ha='center')
            ax.grid(True, alpha=0.3, axis='y')

            # 计算最大值用于智能标签
            all_values = credit_rewards + credit_consumes
            max_value = max(all_values) if all_values and max(all_values) > 0 else 10

            # 收集所有需要标签的柱形数据
            bar_data = []
            for bar in bars1:
                height = bar.get_height()
                if height > 0:
                    bar_data.append((bar.get_x() + bar.get_width()/2., height))

            for bar in bars2:
                height = bar.get_height()
                if height > 0:
                    bar_data.append((bar.get_x() + bar.get_width()/2., height))

            # 使用智能标签函数
            if bar_data:
                x_positions = [x for x, y in bar_data]
                values = [y for x, y in bar_data]
                add_smart_bar_labels(ax, values, max_value, x_positions, fontsize=10, decimal_places=0)

            # 添加图例 - 放在图表外部顶部
            ax.legend(bbox_to_anchor=(0.5, 1.02), loc='lower center',
                     ncol=2, fontsize=11)

            # 添加数据表格
            self._add_data_table(data, ax)

            # 调整布局
            plt.tight_layout()

            # 保存图片
            save_path = self.image_manager.get_image_path(image_type)
            logger.info(f"准备保存图片到: {save_path}")

            # 确保保存目录存在
            save_dir = Path(save_path).parent
            save_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"保存目录: {save_dir}, 存在: {save_dir.exists()}")

            # 保存图片
            try:
                plt.savefig(save_path, dpi=300, bbox_inches='tight',
                           facecolor='white', edgecolor='none')
                logger.info(f"matplotlib保存成功")

                # 验证文件是否生成
                if Path(save_path).exists():
                    file_size = Path(save_path).stat().st_size
                    logger.info(f"图表已生成: {save_path}, 文件大小: {file_size} 字节")
                else:
                    logger.error(f"图片保存失败: 文件不存在 {save_path}")
                    return ""

            except Exception as save_error:
                logger.error(f"matplotlib保存失败: {save_error}")
                return ""
            finally:
                plt.close()

            return save_path

        except Exception as e:
            logger.error(f"生成图表失败 {image_type}: {e}")
            return ""

    def _add_data_table(self, data: List[Dict[str, Any]], ax):
        """
        在图表下方添加数据表格（横向展示）

        Args:
            data: 数据列表
            ax: 坐标轴对象
        """
        try:
            # 准备表格数据
            months = [item['month'] for item in data]
            credit_rewards = [f"{item['credit_reward']:,}" for item in data]
            credit_consumes = [f"{item['credit_consume']:,}" for item in data]

            # 构建表格数据
            table_data = [
                months,  # 第一行：月份
                credit_rewards,  # 第二行：获取
                credit_consumes  # 第三行：消耗
            ]

            # 行标签
            row_labels = ['月份', '获取', '消耗']

            # 创建表格
            table = ax.table(
                cellText=table_data,
                rowLabels=row_labels,
                cellLoc='center',
                loc='bottom',
                bbox=[0, -0.45, 1, 0.35]  # 位置和大小
            )

            # 设置表格样式
            table.auto_set_font_size(False)
            table.set_fontsize(9)
            table.scale(1, 1.3)

            # 设置行标签样式
            for i in range(len(row_labels)):
                table[(i, -1)].set_facecolor('#4472C4')
                table[(i, -1)].set_text_props(weight='bold', color='white')

            # 设置数据单元格样式
            for i in range(len(row_labels)):
                for j in range(len(months)):
                    if i == 0:  # 月份行（蓝色背景）
                        table[(i, j)].set_facecolor('#E7F3FF')
                        table[(i, j)].set_text_props(weight='bold')
                    elif i % 2 == 1:  # 奇数行（浅灰背景）
                        table[(i, j)].set_facecolor('#F8F9FA')
                    else:  # 偶数行（白色背景）
                        table[(i, j)].set_facecolor('white')

            logger.info(f"已添加数据表格，包含 {len(months)} 个月份数据")

        except Exception as e:
            logger.error(f"添加数据表格失败: {e}")


# 工厂函数
def create_credit_change_pic_generator(bid: str, image_manager) -> CreditChangePicGenerator:
    """
    创建积分变化图表生成器

    Args:
        bid: 品牌ID
        image_manager: 图片管理器实例

    Returns:
        CreditChangePicGenerator: 图表生成器实例
    """
    return CreditChangePicGenerator(bid, image_manager)