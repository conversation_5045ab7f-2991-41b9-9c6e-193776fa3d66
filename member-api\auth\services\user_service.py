"""
用户管理服务
处理用户的CRUD操作、权限管理、配额管理等
"""
import json
import logging
from typing import Optional, List, Dict, Any
from datetime import datetime
import aiomysql

from ..schemas.user import (
    UserCreate, UserUpdate, UserResponse, UserList,
    UserPasswordUpdate, UserPermission, UserQuota, UserRole, UserStatus
)
from ..utils.password import hash_password, verify_password

logger = logging.getLogger(__name__)


class UserService:
    """用户管理服务类"""
    
    def __init__(self, db_pool):
        """
        初始化用户服务
        
        Args:
            db_pool: 数据库连接池
        """
        self.db_pool = db_pool
    
    async def get_user_list(
        self, 
        page: int = 1, 
        page_size: int = 20,
        role: Optional[str] = None,
        status: Optional[str] = None,
        search: Optional[str] = None
    ) -> UserList:
        """
        获取用户列表
        
        Args:
            page: 页码
            page_size: 每页大小
            role: 角色筛选
            status: 状态筛选
            search: 搜索关键词
            
        Returns:
            UserList: 用户列表响应
        """
        try:
            offset = (page - 1) * page_size
            
            # 构建查询条件
            where_conditions = ["deleted_at IS NULL"]
            params = []
            
            if role:
                where_conditions.append("role = %s")
                params.append(role)
            
            if status:
                where_conditions.append("status = %s")
                params.append(status)
            
            if search:
                where_conditions.append("(username LIKE %s OR email LIKE %s OR real_name LIKE %s)")
                search_pattern = f"%{search}%"
                params.extend([search_pattern, search_pattern, search_pattern])
            
            where_clause = " AND ".join(where_conditions)
            
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 获取总数
                    count_sql = f"SELECT COUNT(*) as total FROM member_report_users WHERE {where_clause}"
                    await cursor.execute(count_sql, params)
                    total = (await cursor.fetchone())['total']
                    
                    # 获取列表
                    list_sql = f"""
                    SELECT * FROM member_report_users 
                    WHERE {where_clause}
                    ORDER BY created_at ASC
                    LIMIT %s OFFSET %s
                    """
                    params.extend([page_size, offset])
                    await cursor.execute(list_sql, params)
                    users = await cursor.fetchall()
                    
                    # 转换为响应模型
                    items = []
                    for user in users:
                        # 获取用户的BID权限
                        user_bid_perms = await self._get_user_bid_permissions(user['username'])
                        
                        items.append(UserResponse(
                            id=user['id'],
                            username=user['username'],
                            email=user['email'],
                            real_name=user['real_name'],
                            phone=user['phone'],
                            department=user['department'],
                            role=user['role'],
                            account_type=user.get('account_type', 'merchant_user'),
                            expire_time=user['expire_time'].strftime("%Y-%m-%d %H:%M:%S") if user.get('expire_time') else None,
                            status=user['status'],
                            user_bid_permissions=user_bid_perms,  # 使用新的权限格式
                            quotas=user['quotas'] if user['quotas'] else 0,
                            use_quotas=user['use_quotas'] if user['use_quotas'] else 0,
                            avatar_url=user['avatar_url'],
                            last_login_time=user['last_login_time'].strftime("%Y-%m-%d %H:%M:%S") if user['last_login_time'] else None,
                            login_count=user['login_count'],
                            created_at=user['created_at'].strftime("%Y-%m-%d %H:%M:%S"),
                            updated_at=user['updated_at'].strftime("%Y-%m-%d %H:%M:%S")
                        ))
                    
                    return UserList(
                        total=total,
                        items=items,
                        page=page,
                        page_size=page_size
                    )
                    
        except Exception as e:
            logger.error(f"获取用户列表失败: {e}")
            return UserList(total=0, items=[], page=page, page_size=page_size)
    
    async def get_user_by_id(self, user_id: int) -> Optional[UserResponse]:
        """
        根据ID获取用户
        
        Args:
            user_id: 用户ID
            
        Returns:
            UserResponse: 用户信息
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    sql = """
                    SELECT * FROM member_report_users 
                    WHERE id = %s AND deleted_at IS NULL
                    """
                    await cursor.execute(sql, (user_id,))
                    user = await cursor.fetchone()
                    
                    if not user:
                        return None
                    
                    # 获取用户的BID权限
                    user_bid_perms = await self._get_user_bid_permissions(user['username'])
                    
                    return UserResponse(
                        id=user['id'],
                        username=user['username'],
                        email=user['email'],
                        real_name=user['real_name'],
                        phone=user['phone'],
                        department=user['department'],
                        role=user['role'],
                        account_type=user.get('account_type', 'merchant_user'),
                        expire_time=user['expire_time'].strftime("%Y-%m-%d %H:%M:%S") if user.get('expire_time') else None,
                        status=user['status'],
                        user_bid_permissions=user_bid_perms,  # 使用新的权限格式
                        quotas=user['quotas'] if user['quotas'] else 0,
                        use_quotas=user['use_quotas'] if user['use_quotas'] else 0,
                        avatar_url=user['avatar_url'],
                        last_login_time=user['last_login_time'].strftime("%Y-%m-%d %H:%M:%S") if user['last_login_time'] else None,
                        login_count=user['login_count'],
                        created_at=user['created_at'].strftime("%Y-%m-%d %H:%M:%S"),
                        updated_at=user['updated_at'].strftime("%Y-%m-%d %H:%M:%S")
                    )
                    
        except Exception as e:
            logger.error(f"获取用户失败: {e}")
            return None
    
    async def create_user(self, user_data: UserCreate, created_by: Optional[str] = None) -> Optional[UserResponse]:
        """
        创建用户
        
        Args:
            user_data: 用户创建数据
            
        Returns:
            UserResponse: 创建的用户信息
        """
        try:
            # 检查用户名是否已存在
            if await self._check_username_exists(user_data.username):
                raise ValueError("用户名已存在")
            
            # 检查邮箱是否已存在
            if user_data.email and await self._check_email_exists(user_data.email):
                raise ValueError("邮箱已被使用")
            
            # 加密密码
            password = hash_password(user_data.password)
            
            async with self.db_pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    # 处理账户到期时间
                    expire_time = None
                    if hasattr(user_data, 'duration_days') and user_data.duration_days:
                        from datetime import datetime, timedelta
                        expire_time = datetime.now() + timedelta(days=user_data.duration_days)
                    elif hasattr(user_data, 'expire_time') and user_data.expire_time:
                        expire_time = user_data.expire_time
                    
                    # 创建用户基本信息 (移除了remark字段，因为用户表中没有这个字段)
                    sql = """
                    INSERT INTO member_report_users 
                    (username, email, password, real_name, phone, department,
                     role, account_type, expire_time, status, quotas,
                     created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                    """
                    
                    await cursor.execute(sql, (
                        user_data.username,
                        user_data.email,
                        password,
                        user_data.real_name,
                        user_data.phone,
                        user_data.department,
                        user_data.role.value if user_data.role else UserRole.USER.value,
                        user_data.account_type.value if hasattr(user_data, 'account_type') and user_data.account_type else 'merchant_user',
                        expire_time,
                        UserStatus.ACTIVE.value,
                        user_data.quotas if user_data.quotas else 0
                    ))
                    
                    user_id = cursor.lastrowid
                    await conn.commit()
                    
                    # 如果有BID权限配置，添加到新的权限表
                    if user_data.bid_permissions:
                        from .bid_permission_service import BidPermissionService
                        permission_service = BidPermissionService(self.db_pool)
                        
                        for bid_perm in user_data.bid_permissions:
                            bid = bid_perm.get('bid')
                            permissions = bid_perm.get('permissions', ['read'])
                            sid_filter = None
                            
                            # 如果有SID过滤配置
                            if user_data.sid_filter and bid in user_data.sid_filter:
                                sid_filter = user_data.sid_filter[bid]
                            
                            # 授予权限
                            await permission_service.grant_user_bid_permission(
                                username=user_data.username,
                                bid=bid,
                                permissions=permissions,
                                sid_filter=sid_filter,
                                granted_by=created_by if created_by else 'system',
                                grant_reason='用户创建时设置初始权限'
                            )
                    
                    # 返回创建的用户信息
                    return await self.get_user_by_id(user_id)
                    
        except ValueError as e:
            logger.error(f"创建用户失败: {e}")
            raise
        except Exception as e:
            logger.error(f"创建用户失败: {e}")
            return None
    
    async def update_user(self, user_id: int, user_data: UserUpdate, updated_by: Optional[str] = None) -> Optional[UserResponse]:
        """
        更新用户信息
        
        Args:
            user_id: 用户ID
            user_data: 更新数据
            
        Returns:
            UserResponse: 更新后的用户信息
        """
        try:
            # 构建更新字段
            update_fields = []
            params = []
            
            if user_data.email is not None:
                # 检查邮箱是否被其他用户使用
                if await self._check_email_exists(user_data.email, exclude_user_id=user_id):
                    raise ValueError("邮箱已被使用")
                update_fields.append("email = %s")
                params.append(user_data.email)
            
            if user_data.real_name is not None:
                update_fields.append("real_name = %s")
                params.append(user_data.real_name)
            
            if user_data.phone is not None:
                update_fields.append("phone = %s")
                params.append(user_data.phone)
            
            if user_data.department is not None:
                update_fields.append("department = %s")
                params.append(user_data.department)
            
            if user_data.role is not None:
                update_fields.append("role = %s")
                params.append(user_data.role.value)
            
            if user_data.status is not None:
                update_fields.append("status = %s")
                params.append(user_data.status.value)
            
            if hasattr(user_data, 'account_type') and user_data.account_type is not None:
                update_fields.append("account_type = %s")
                params.append(user_data.account_type.value)
            
            if hasattr(user_data, 'expire_time') and user_data.expire_time is not None:
                update_fields.append("expire_time = %s")
                params.append(user_data.expire_time)
            
            if hasattr(user_data, 'duration_days') and user_data.duration_days is not None:
                # 如果提供了duration_days，则延长账户时间
                from datetime import datetime, timedelta
                new_expire_time = datetime.now() + timedelta(days=user_data.duration_days)
                update_fields.append("expire_time = %s")
                params.append(new_expire_time)
            
            if user_data.quotas is not None:
                update_fields.append("quotas = %s")
                params.append(user_data.quotas)
            
            if not update_fields:
                return await self.get_user_by_id(user_id)
            
            # 添加更新时间
            update_fields.append("updated_at = NOW()")
            
            async with self.db_pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    sql = f"""
                    UPDATE member_report_users 
                    SET {', '.join(update_fields)}
                    WHERE id = %s AND deleted_at IS NULL
                    """
                    params.append(user_id)
                    
                    await cursor.execute(sql, params)
                    await conn.commit()
                    
                    # 如果有BID权限配置，更新权限表
                    if user_data.bid_permissions is not None:
                        # 获取用户名
                        user = await self.get_user_by_id(user_id)
                        if user:
                            from .bid_permission_service import BidPermissionService
                            permission_service = BidPermissionService(self.db_pool)
                            
                            # 先删除旧的权限
                            await permission_service.revoke_all_user_permissions(user.username)
                            
                            # 添加新的权限
                            for bid_perm in user_data.bid_permissions:
                                bid = bid_perm.get('bid')
                                permissions = bid_perm.get('permissions', ['read'])
                                sid_filter = None
                                
                                # 如果有SID过滤配置
                                if user_data.sid_filter and bid in user_data.sid_filter:
                                    sid_filter = user_data.sid_filter[bid]
                                
                                # 授予权限
                                await permission_service.grant_user_bid_permission(
                                    username=user.username,
                                    bid=bid,
                                    permissions=permissions,
                                    sid_filter=sid_filter,
                                    granted_by=updated_by if updated_by else 'system',
                                    grant_reason='用户权限更新'
                                )
                    
                    return await self.get_user_by_id(user_id)
                    
        except ValueError as e:
            logger.error(f"更新用户失败: {e}")
            raise
        except Exception as e:
            logger.error(f"更新用户失败: {e}")
            return None
    
    async def delete_user(self, user_id: int, hard_delete: bool = False) -> bool:
        """
        删除用户
        
        Args:
            user_id: 用户ID
            hard_delete: 是否硬删除
            
        Returns:
            bool: 是否成功
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    if hard_delete:
                        # 硬删除
                        sql = "DELETE FROM member_report_users WHERE id = %s"
                    else:
                        # 软删除
                        sql = """
                        UPDATE member_report_users 
                        SET deleted_at = NOW(), status = 'inactive'
                        WHERE id = %s AND deleted_at IS NULL
                        """
                    
                    await cursor.execute(sql, (user_id,))
                    await conn.commit()
                    
                    return cursor.rowcount > 0
                    
        except Exception as e:
            logger.error(f"删除用户失败: {e}")
            return False
    
    async def update_password(self, user_id: int, password_data: UserPasswordUpdate) -> bool:
        """
        更新用户密码
        
        Args:
            user_id: 用户ID
            password_data: 密码更新数据
            
        Returns:
            bool: 是否成功
        """
        try:
            # 获取当前用户
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 验证旧密码
                    sql = "SELECT password FROM member_report_users WHERE id = %s"
                    await cursor.execute(sql, (user_id,))
                    user = await cursor.fetchone()
                    
                    if not user:
                        raise ValueError("用户不存在")
                    
                    if not verify_password(password_data.old_password, user['password']):
                        raise ValueError("原密码错误")
                    
                    # 更新密码
                    new_password = hash_password(password_data.new_password)
                    sql = """
                    UPDATE member_report_users 
                    SET password = %s, updated_at = NOW()
                    WHERE id = %s
                    """
                    
                    await cursor.execute(sql, (new_password, user_id))
                    await conn.commit()
                    
                    return True
                    
        except ValueError as e:
            logger.error(f"更新密码失败: {e}")
            raise
        except Exception as e:
            logger.error(f"更新密码失败: {e}")
            return False
    
    async def reset_password(self, user_id: int, new_password: str) -> bool:
        """
        重置用户密码（管理员操作）
        
        Args:
            user_id: 用户ID
            new_password: 新密码
            
        Returns:
            bool: 是否成功
        """
        try:
            password = hash_password(new_password)
            
            async with self.db_pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    sql = """
                    UPDATE member_report_users 
                    SET password = %s, updated_at = NOW()
                    WHERE id = %s AND deleted_at IS NULL
                    """
                    
                    await cursor.execute(sql, (password, user_id))
                    await conn.commit()
                    
                    return cursor.rowcount > 0
                    
        except Exception as e:
            logger.error(f"重置密码失败: {e}")
            return False
    
    async def update_user_quotas(self, user_id: int, quotas: Dict[str, int]) -> bool:
        """
        更新用户配额
        
        Args:
            user_id: 用户ID
            quotas: 配额数据
            
        Returns:
            bool: 是否成功
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    sql = """
                    UPDATE member_report_users 
                    SET quotas = %s, updated_at = NOW()
                    WHERE id = %s AND deleted_at IS NULL
                    """
                    
                    await cursor.execute(sql, (json.dumps(quotas), user_id))
                    await conn.commit()
                    
                    return cursor.rowcount > 0
                    
        except Exception as e:
            logger.error(f"更新用户配额失败: {e}")
            return False
    
    async def get_user_statistics(self) -> Dict[str, Any]:
        """
        获取用户统计信息
        
        Returns:
            Dict: 统计信息
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    sql = """
                    SELECT 
                        COUNT(*) as total_users,
                        SUM(CASE WHEN role = 'super_admin' THEN 1 ELSE 0 END) as super_admin_count,
                        SUM(CASE WHEN role = 'admin' THEN 1 ELSE 0 END) as admin_count,
                        SUM(CASE WHEN role = 'user' THEN 1 ELSE 0 END) as user_count,
                        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_count,
                        SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_count,
                        SUM(CASE WHEN DATE(last_login_time) = CURDATE() THEN 1 ELSE 0 END) as today_login_count
                    FROM member_report_users
                    WHERE deleted_at IS NULL
                    """
                    
                    await cursor.execute(sql)
                    stats = await cursor.fetchone()
                    
                    return {
                        "total_users": stats['total_users'] or 0,
                        "role_distribution": {
                            "super_admin": stats['super_admin_count'] or 0,
                            "admin": stats['admin_count'] or 0,
                            "user": stats['user_count'] or 0
                        },
                        "status_distribution": {
                            "active": stats['active_count'] or 0,
                            "inactive": stats['inactive_count'] or 0
                        },
                        "today_login_count": stats['today_login_count'] or 0
                    }
                    
        except Exception as e:
            logger.error(f"获取用户统计失败: {e}")
            return {
                "total_users": 0,
                "role_distribution": {},
                "status_distribution": {},
                "today_login_count": 0
            }
    
    # ========== 私有方法 ==========
    
    async def _check_username_exists(self, username: str, exclude_user_id: Optional[int] = None) -> bool:
        """检查用户名是否存在"""
        async with self.db_pool.acquire() as conn:
            async with conn.cursor() as cursor:
                sql = "SELECT COUNT(*) FROM member_report_users WHERE username = %s AND deleted_at IS NULL"
                params = [username]
                
                if exclude_user_id:
                    sql += " AND id != %s"
                    params.append(exclude_user_id)
                
                await cursor.execute(sql, params)
                count = (await cursor.fetchone())[0]
                return count > 0
    
    async def _check_email_exists(self, email: str, exclude_user_id: Optional[int] = None) -> bool:
        """检查邮箱是否存在"""
        async with self.db_pool.acquire() as conn:
            async with conn.cursor() as cursor:
                sql = "SELECT COUNT(*) FROM member_report_users WHERE email = %s AND deleted_at IS NULL"
                params = [email]
                
                if exclude_user_id:
                    sql += " AND id != %s"
                    params.append(exclude_user_id)
                
                await cursor.execute(sql, params)
                count = (await cursor.fetchone())[0]
                return count > 0
    
    async def _get_user_bid_permissions(self, username: str) -> Optional[List[Dict[str, Any]]]:
        """获取用户的BID权限"""
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    sql = """
                    SELECT 
                        p.bid,
                        p.permissions,
                        p.sid_filter,
                        p.status,
                        p.expire_at,
                        b.bid_name,
                        b.description
                    FROM member_report_user_bid_permissions p
                    JOIN member_report_bid_configs b ON p.bid = b.bid
                    WHERE p.username = %s AND p.status = 'active'
                    AND (p.expire_at IS NULL OR p.expire_at > NOW())
                    """
                    await cursor.execute(sql, (username,))
                    permissions = await cursor.fetchall()
                    
                    if not permissions:
                        return None
                    
                    result = []
                    for perm in permissions:
                        result.append({
                            'bid': perm['bid'],
                            'bid_name': perm['bid_name'],
                            'permissions': json.loads(perm['permissions']) if perm['permissions'] else [],
                            'sid_filter': json.loads(perm['sid_filter']) if perm['sid_filter'] else None,
                            'expire_at': perm['expire_at'].strftime("%Y-%m-%d %H:%M:%S") if perm['expire_at'] else None
                        })
                    
                    return result
        except Exception as e:
            logger.error(f"获取用户BID权限失败: {e}")
            return None