# -*- coding: utf-8 -*-
"""
会员等级消费分析AI分析提示词模板
提供会员等级消费数据的智能分析提示词生成功能
"""

from typing import Dict, Any, List
import random

class LevelConsumptionAnalysisPrompts:
    """会员等级消费分析AI分析提示词类"""

    @staticmethod
    def get_level_consumption_analysis_prompt(level_data: List[Dict[str, Any]], start_date: str = None, end_date: str = None) -> str:
        """
        生成会员等级消费分析提示词

        Args:
            level_data: 会员等级消费数据列表
            start_date: 查询开始日期
            end_date: 查询结束日期

        Returns:
            str: 分析提示词
        """
        if not level_data:
            return "会员等级消费数据不足，无法进行有效分析。建议完善数据收集机制，确保各等级会员消费数据的完整性和准确性。"

        try:
            # 提取关键数据
            level_names = [item.get('ccName', '未知等级') for item in level_data]
            per_capita_values = [item.get('perCapitaConsumption', 0) for item in level_data]
            unit_price_values = [item.get('customerUnitPrice', 0) for item in level_data]
            frequency_values = [item.get('avgConsumFrequency', 0) for item in level_data]

            # 计算基础统计
            total_levels = len(level_data)
            avg_per_capita = sum(per_capita_values) / total_levels if total_levels > 0 else 0
            avg_unit_price = sum(unit_price_values) / total_levels if total_levels > 0 else 0
            avg_frequency = sum(frequency_values) / total_levels if total_levels > 0 else 0

            # 找出关键等级
            max_per_capita_idx = per_capita_values.index(max(per_capita_values)) if per_capita_values else 0
            max_unit_price_idx = unit_price_values.index(max(unit_price_values)) if unit_price_values else 0
            max_frequency_idx = frequency_values.index(max(frequency_values)) if frequency_values else 0

            highest_per_capita_level = level_names[max_per_capita_idx]
            highest_unit_price_level = level_names[max_unit_price_idx]
            highest_frequency_level = level_names[max_frequency_idx]

            # 计算等级差异
            per_capita_range = max(per_capita_values) - min(per_capita_values) if per_capita_values else 0
            unit_price_range = max(unit_price_values) - min(unit_price_values) if unit_price_values else 0
            frequency_range = max(frequency_values) - min(frequency_values) if frequency_values else 0

            # 生成多样化的分析角度
            analysis_angles = [
                f"从会员等级消费结构来看，{highest_per_capita_level}在人均消费额方面表现最优，达到{max(per_capita_values):.2f}元，体现了高等级会员的消费价值。",
                f"各等级会员的客单价差异明显，{highest_unit_price_level}的客单价最高为{max(unit_price_values):.2f}元，显示出等级体系的有效性。",
                f"消费频次数据显示，{highest_frequency_level}的平均消费频次达到{max(frequency_values):.2f}次，反映了会员粘性的等级差异。",
                f"整体来看，{total_levels}个会员等级的人均消费额平均为{avg_per_capita:.2f}元，客单价平均{avg_unit_price:.2f}元。"
            ]

            # 生成运营建议
            operation_suggestions = [
                "建议针对高价值等级制定专属服务策略，进一步提升其消费体验和忠诚度。",
                "可考虑优化等级晋升机制，引导低等级会员向高等级转化，提升整体消费水平。",
                "应重点关注消费频次较低的等级，通过个性化营销活动激发其消费潜力。",
                "建议建立等级间的差异化权益体系，强化等级价值感知，促进会员升级。",
                "可通过数据分析识别各等级的消费偏好，实施精准化的产品推荐和营销策略。"
            ]

            # 随机选择分析角度和建议
            selected_analysis = random.choice(analysis_angles)
            selected_suggestion = random.choice(operation_suggestions)

            # 构建时间范围描述
            time_desc = ""
            if start_date and end_date:
                time_desc = f"在{start_date}至{end_date}期间，"
            elif start_date:
                time_desc = f"自{start_date}以来，"
            elif end_date:
                time_desc = f"截至{end_date}，"

            # 构建完整的分析提示词
            analysis_prompt = f"""
{time_desc}{selected_analysis}从等级差异化角度分析，人均消费额的等级差距为{per_capita_range:.2f}元，客单价差距为{unit_price_range:.2f}元，消费频次差距为{frequency_range:.2f}次，这些差异反映了会员分层策略的有效性。{selected_suggestion}同时，应持续监控各等级的消费行为变化，及时调整等级权益和服务标准，确保会员价值最大化。
""".strip()

            return analysis_prompt

        except Exception as e:
            # 返回默认分析
            return f"会员等级消费数据分析遇到技术问题，建议检查数据完整性。从现有{len(level_data)}个等级的基础数据来看，各等级间存在明显的消费差异，建议优化等级体系设计，通过差异化服务和权益配置，提升会员整体价值和消费活跃度，同时加强数据质量管控。"

    @staticmethod
    def get_fallback_analysis() -> str:
        """
        获取备用分析内容

        Returns:
            str: 备用分析内容
        """
        return "会员等级消费数据暂时无法获取，建议检查数据源连接和查询逻辑。从会员运营角度来看，建立完善的等级消费分析体系对于精准营销和会员价值提升至关重要。建议优先完善数据收集机制，确保各等级会员的消费行为数据能够准确记录和分析，为后续的等级优化和权益设计提供可靠的数据支撑。"
