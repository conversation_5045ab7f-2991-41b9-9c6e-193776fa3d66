# -*- coding: utf-8 -*-
"""
会员数据报告配置文件
这个文件现在主要用于导入和重新导出 constants.py 中的配置
如需修改数据，请编辑 constants.py 文件

使用方法:
1. 编辑 constants.py 文件中的数据
2. 运行 generate_member_report.py 生成报告
"""

# 从 constants.py 导入所有配置
from .constants import (
    # 基础配置
    TIME_FRAME,
    REPORT_DATE,
    TEMPLATE_PATH,
    OUTPUT_DIR,
    OUTPUT_PREFIX,
    TIMESTAMP_FORMAT,

    # 会员数据
    TOTAL_MEMBERS,
    NEW_MEMBERS,
    HISTORICAL_MEMBERS,

    # 完整数据字典
    MEMBER_DATA,

    # 辅助函数
    get_output_filename,
    get_full_output_path,
    print_data_summary as print_current_config,
    print_validation_report,
)

if __name__ == "__main__":
    # 直接运行此文件可以查看当前配置
    print_current_config()
    print_validation_report()
