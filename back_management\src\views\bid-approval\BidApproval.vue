<template>
  <div class="bid-approval-container">
    <!-- 标签页 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="待审批" name="pending">
        <!-- 搜索栏 -->
        <div class="search-bar">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索BID代码、名称或公司"
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
            style="width: 300px; margin-right: 10px"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleRefresh">刷新</el-button>
        </div>

        <!-- 待审批列表 -->
        <el-table
          :data="pendingList"
          v-loading="loading"
          stripe
          style="width: 100%; margin-top: 20px"
        >
          <el-table-column prop="bid" label="BID代码" width="120" />
          <el-table-column prop="bid_name" label="BID名称" min-width="150" />
          <el-table-column prop="company_name" label="公司名称" min-width="150" />
          <el-table-column prop="owner_username" label="申请人" width="120" />
          <el-table-column prop="contact_name" label="联系人" width="100" />
          <el-table-column prop="contact_phone" label="联系电话" width="120" />
          <el-table-column label="申请时间" width="160">
            <template #default="scope">
              {{ formatDate(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="280" fixed="right">
            <template #default="scope">
              <div style="display: flex; flex-wrap: nowrap; gap: 5px;">
                <el-button type="primary" size="small" @click="handleView(scope.row)">
                  查看详情
                </el-button>
                <el-button type="success" size="small" @click="handleApprove(scope.row)">
                  批准
                </el-button>
                <el-button type="danger" size="small" @click="handleReject(scope.row)">
                  拒绝
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          style="margin-top: 20px"
        />
      </el-tab-pane>

      <el-tab-pane label="审批历史" name="history">
        <!-- 筛选栏 -->
        <div class="filter-bar">
          <el-select
            v-model="historyStatus"
            placeholder="选择状态"
            clearable
            @change="fetchHistoryList"
            style="width: 150px; margin-right: 10px"
          >
            <el-option label="全部" value="" />
            <el-option label="已批准" value="approved" />
            <el-option label="已拒绝" value="rejected" />
            <el-option label="已激活" value="active" />
            <el-option label="已停用" value="inactive" />
          </el-select>
          <el-button @click="fetchHistoryList">刷新</el-button>
        </div>

        <!-- 历史列表 -->
        <el-table
          :data="historyList"
          v-loading="historyLoading"
          stripe
          style="width: 100%; margin-top: 20px"
        >
          <el-table-column prop="bid" label="BID代码" width="120" />
          <el-table-column prop="bid_name" label="BID名称" min-width="150" />
          <el-table-column prop="company_name" label="公司名称" min-width="150" />
          <el-table-column prop="owner_username" label="申请人" width="120" />
          <el-table-column label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="approved_by" label="审批人" width="120" />
          <el-table-column label="审批时间" width="160">
            <template #default="scope">
              {{ formatDate(scope.row.approved_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button type="primary" size="small" @click="handleView(scope.row)">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <el-pagination
          v-model:current-page="historyPage"
          v-model:page-size="historyPageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="historyTotal"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleHistorySizeChange"
          @current-change="handleHistoryPageChange"
          style="margin-top: 20px"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="BID申请详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-descriptions :column="2" border v-if="currentDetail">
        <el-descriptions-item label="BID代码">{{ currentDetail.bid }}</el-descriptions-item>
        <el-descriptions-item label="BID名称">{{ currentDetail.bid_name }}</el-descriptions-item>
        <el-descriptions-item label="申请人">{{ currentDetail.owner_username }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(currentDetail.status)">
            {{ getStatusText(currentDetail.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="公司名称" :span="2">{{ currentDetail.company_name }}</el-descriptions-item>
        <el-descriptions-item label="行业">{{ currentDetail.industry || '-' }}</el-descriptions-item>
        <el-descriptions-item label="联系人">{{ currentDetail.contact_name }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ currentDetail.contact_phone }}</el-descriptions-item>
        <el-descriptions-item label="联系邮箱">{{ currentDetail.contact_email || '-' }}</el-descriptions-item>
        <el-descriptions-item label="申请理由" :span="2">
          {{ currentDetail.apply_reason }}
        </el-descriptions-item>
        <el-descriptions-item label="业务描述" :span="2">
          {{ currentDetail.description || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="SID列表" :span="2">
          <el-tag v-for="sid in currentDetail.sid_list" :key="sid" style="margin-right: 5px">
            {{ sid }}
          </el-tag>
          <span v-if="!currentDetail.sid_list || currentDetail.sid_list.length === 0">-</span>
        </el-descriptions-item>
        <el-descriptions-item label="申请时间" :span="2">
          {{ formatDate(currentDetail.created_at) }}
        </el-descriptions-item>
        <el-descriptions-item v-if="currentDetail.approved_by" label="审批人">
          {{ currentDetail.approved_by }}
        </el-descriptions-item>
        <el-descriptions-item v-if="currentDetail.approved_at" label="审批时间">
          {{ formatDate(currentDetail.approved_at) }}
        </el-descriptions-item>
        <el-descriptions-item v-if="currentDetail.reject_reason" label="拒绝理由" :span="2">
          {{ currentDetail.reject_reason }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 批准对话框 -->
    <el-dialog
      v-model="approveDialogVisible"
      title="批准申请"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="approveForm" label-width="100px">
        <el-form-item label="BID代码">
          {{ approveForm.bid }}
        </el-form-item>
        <el-form-item label="BID名称">
          {{ approveForm.bid_name }}
        </el-form-item>
        <el-form-item label="自动激活">
          <el-switch v-model="approveForm.auto_activate" />
          <span style="margin-left: 10px; color: #909399">
            批准后自动激活并授予权限
          </span>
        </el-form-item>
        <el-form-item label="审批意见">
          <el-input
            v-model="approveForm.comment"
            type="textarea"
            :rows="3"
            placeholder="请输入审批意见（选填）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="approveDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmApprove" :loading="submitLoading">
          确认批准
        </el-button>
      </template>
    </el-dialog>

    <!-- 拒绝对话框 -->
    <el-dialog
      v-model="rejectDialogVisible"
      title="拒绝申请"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="rejectForm" label-width="100px">
        <el-form-item label="BID代码">
          {{ rejectForm.bid }}
        </el-form-item>
        <el-form-item label="BID名称">
          {{ rejectForm.bid_name }}
        </el-form-item>
        <el-form-item label="拒绝理由" required>
          <el-input
            v-model="rejectForm.reject_reason"
            type="textarea"
            :rows="4"
            placeholder="请输入拒绝理由（必填）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="rejectDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="confirmReject" :loading="submitLoading">
          确认拒绝
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import request from '@/api/request'

// 标签页
const activeTab = ref('pending')

// 待审批列表相关
const loading = ref(false)
const pendingList = ref([])
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 历史列表相关
const historyLoading = ref(false)
const historyList = ref([])
const historyStatus = ref('')
const historyPage = ref(1)
const historyPageSize = ref(20)
const historyTotal = ref(0)

// 对话框相关
const detailDialogVisible = ref(false)
const currentDetail = ref(null)
const approveDialogVisible = ref(false)
const rejectDialogVisible = ref(false)
const submitLoading = ref(false)

// 表单数据
const approveForm = reactive({
  bid: '',
  bid_name: '',
  auto_activate: true,
  comment: ''
})

const rejectForm = reactive({
  bid: '',
  bid_name: '',
  reject_reason: ''
})

// 获取待审批列表
const fetchPendingList = async () => {
  loading.value = true
  try {
    const response = await request.get('/admin/bid-approvals/pending', {
      params: {
        page: currentPage.value,
        page_size: pageSize.value,
        search: searchKeyword.value || undefined
      }
    })
    
    if (response.code === 200 || response.items) {
      pendingList.value = response.items || response.data?.items || []
      total.value = response.total || response.data?.total || 0
    }
  } catch (error) {
    console.error('获取待审批列表失败:', error)
    ElMessage.error('获取待审批列表失败')
  } finally {
    loading.value = false
  }
}

// 获取历史列表
const fetchHistoryList = async () => {
  historyLoading.value = true
  try {
    const response = await request.get('/admin/bid-approvals/history', {
      params: {
        page: historyPage.value,
        page_size: historyPageSize.value,
        status: historyStatus.value || undefined
      }
    })
    
    if (response.code === 200 || response.items) {
      historyList.value = response.items || response.data?.items || []
      historyTotal.value = response.total || response.data?.total || 0
    }
  } catch (error) {
    console.error('获取审批历史失败:', error)
    ElMessage.error('获取审批历史失败')
  } finally {
    historyLoading.value = false
  }
}

// 查看详情
const handleView = (row) => {
  currentDetail.value = row
  detailDialogVisible.value = true
}

// 批准申请
const handleApprove = (row) => {
  approveForm.bid = row.bid
  approveForm.bid_name = row.bid_name
  approveForm.auto_activate = true
  approveForm.comment = ''
  approveDialogVisible.value = true
}

// 确认批准
const confirmApprove = async () => {
  if (!approveForm.bid) return
  
  submitLoading.value = true
  try {
    const response = await request.post(`/admin/bid-approvals/${approveForm.bid}/approve`, {
      auto_activate: approveForm.auto_activate,
      comment: approveForm.comment
    })
    
    if (response.success || response.code === 200) {
      ElMessage.success(response.message || '批准成功')
      approveDialogVisible.value = false
      fetchPendingList()
    } else {
      ElMessage.error(response.message || '批准失败')
    }
  } catch (error) {
    console.error('批准申请失败:', error)
    ElMessage.error('批准申请失败')
  } finally {
    submitLoading.value = false
  }
}

// 拒绝申请
const handleReject = (row) => {
  rejectForm.bid = row.bid
  rejectForm.bid_name = row.bid_name
  rejectForm.reject_reason = ''
  rejectDialogVisible.value = true
}

// 确认拒绝
const confirmReject = async () => {
  if (!rejectForm.bid || !rejectForm.reject_reason) {
    ElMessage.warning('请输入拒绝理由')
    return
  }
  
  submitLoading.value = true
  try {
    const response = await request.post(`/admin/bid-approvals/${rejectForm.bid}/reject`, {
      reject_reason: rejectForm.reject_reason
    })
    
    if (response.success || response.code === 200) {
      ElMessage.success(response.message || '拒绝成功')
      rejectDialogVisible.value = false
      fetchPendingList()
    } else {
      ElMessage.error(response.message || '拒绝失败')
    }
  } catch (error) {
    console.error('拒绝申请失败:', error)
    ElMessage.error('拒绝申请失败')
  } finally {
    submitLoading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchPendingList()
}

// 刷新
const handleRefresh = () => {
  if (activeTab.value === 'pending') {
    fetchPendingList()
  } else {
    fetchHistoryList()
  }
}

// 切换标签页
const handleTabClick = (tab) => {
  if (tab.props.name === 'history' && historyList.value.length === 0) {
    fetchHistoryList()
  }
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchPendingList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchPendingList()
}

const handleHistorySizeChange = (val) => {
  historyPageSize.value = val
  fetchHistoryList()
}

const handleHistoryPageChange = (val) => {
  historyPage.value = val
  fetchHistoryList()
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  return dateStr
}

// 获取状态类型
const getStatusType = (status) => {
  const map = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    active: 'success',
    inactive: 'info'
  }
  return map[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const map = {
    pending: '待审批',
    approved: '已批准',
    rejected: '已拒绝',
    active: '已激活',
    inactive: '已停用'
  }
  return map[status] || status
}

// 初始化
onMounted(() => {
  fetchPendingList()
})
</script>

<style scoped>
.bid-approval-container {
  padding: 20px;
}

.search-bar,
.filter-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.el-descriptions {
  margin-top: 20px;
}
</style>