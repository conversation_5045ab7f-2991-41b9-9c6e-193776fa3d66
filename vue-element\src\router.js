import { createRouter, createWebHistory } from 'vue-router'
import MemberDataQuery from './views/query/MemberDataQuery.vue'
import PPTReportGenerator from './views/PPTreport/PPTReportGenerator.vue'
import Login from './views/auth/Login.vue'
import NotFound from './views/404.vue'
import BidApplication from './views/setting/BidApplication.vue'

const routes = [
  {
    path: '/',
    redirect: '/query'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/query',
    name: 'MemberDataQuery',
    component: MemberDataQuery,
    meta: {
      title: '数据查询',
      requiresAuth: true
    }
  },
  {
    path: '/ppt-report',
    name: 'PPTReportGenerator',
    component: PPTReportGenerator,
    meta: {
      title: 'PPT报告生成',
      requiresAuth: true
    }
  },
  {
    path: '/setting/bid-application',
    name: 'BidApplication',
    component: BidApplication,
    meta: {
      title: 'BID配置申请',
      requiresAuth: true
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: NotFound,
    meta: {
      requiresAuth: false
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 动态导入，确保Pinia已经初始化
  const { useAuthStore } = await import('@/stores/auth')
  const authStore = useAuthStore()
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 会员报表系统`
  }
  
  // 检查路由是否需要认证
  if (to.meta.requiresAuth !== false) {
    // 默认需要认证
    if (!authStore.isLoggedIn) {
      // 未登录，跳转到登录页
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
    } else {
      // 已登录，允许访问
      next()
    }
  } else {
    // 不需要认证的页面
    if (to.path === '/login' && authStore.isLoggedIn) {
      // 已登录用户访问登录页，重定向到首页
      next('/')
    } else {
      next()
    }
  }
})

export default router
