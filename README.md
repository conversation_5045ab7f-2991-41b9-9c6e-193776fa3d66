<<<<<<< HEAD
# 会员报表系统 (Member Report System)

## 项目概述

这是一个基于前后端分离架构的会员报表系统，专门用于品牌商户的会员数据分析和报表生成。系统支持多时间维度（周/月/季/半年）的数据对比分析，包括环比和同比功能。

## 技术架构

### 后端技术栈
- **框架**: FastAPI (Python 3.8+)
- **数据库**: MySQL/PostgreSQL
- **异步处理**: asyncio + aiomysql/asyncpg
- **认证**: JWT Token
- **API文档**: Swagger/OpenAPI 3.0
- **日志**: Python logging
- **部署**: Uvicorn ASGI服务器

### 前端技术栈
- **框架**: Vue.js 3
- **UI组件**: Element Plus
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **构建工具**: Vite

## 项目结构

```
member_report/
├── aiui-api/                    # 后端API服务
│   ├── api/                     # API路由模块
│   │   ├── week_inquiry/        # 周报查询接口
│   │   ├── week_report/         # 周报生成接口
│   │   ├── mouth_inquiry/       # 月报查询接口
│   │   ├── mouth_report/        # 月报生成接口
│   │   ├── quarter_inquiry/     # 季报查询接口
│   │   └── quarter_report/      # 季报生成接口
│   ├── core/                    # 核心模块
│   │   ├── config.py           # 配置管理
│   │   ├── database.py         # 数据库连接
│   │   ├── middleware.py       # 中间件
│   │   ├── models.py           # 数据模型
│   │   └── exception_handlers.py # 异常处理
│   ├── services/               # 业务逻辑层
│   │   ├── llm_service.py      # LLM服务
│   │   ├── data_service.py     # 数据查询服务
│   │   ├── report_service.py   # 报表生成服务
│   │   └── calculation_service.py # 计算服务
│   ├── utils/                  # 工具模块
│   │   ├── setup_logging.py   # 日志配置
│   │   ├── date_utils.py       # 日期工具
│   │   └── validators.py       # 数据验证
│   ├── scripts/                # 脚本文件
│   ├── main.py                 # 应用入口
│   └── requirements.txt        # 依赖包
├── vue-element/                # 前端应用
│   ├── src/
│   │   ├── api/                # API接口封装
│   │   ├── components/         # 公共组件
│   │   ├── views/              # 页面组件
│   │   ├── utils/              # 工具函数
│   │   ├── router.js           # 路由配置
│   │   ├── main.js             # 应用入口
│   │   └── App.vue             # 根组件
│   └── package.json            # 前端依赖
└── README.md                   # 项目文档
```

## 核心功能设计

### 1. 数据查询架构

#### 数据源整合
- **微生活会员系统**: 会员基础数据、消费记录
- **收银系统**: 交易数据、商品信息
- **其他业务系统**: 30-40个不同数据字段

#### 数据服务层设计
```python
# services/data_service.py
class DataService:
    async def get_brand_data(self, brand_id: str, date_range: DateRange)
    async def get_member_metrics(self, brand_id: str, period: str)
    async def get_transaction_data(self, brand_id: str, filters: dict)
```

### 2. 报表生成架构

#### 时间维度支持
- **周报**: 与前3周对比 + 去年同期
- **月报**: 与前3个月对比 + 去年同期  
- **季报**: 与前一季度对比 + 去年同期
- **半年报**: 与前半年对比 + 去年同期

#### 计算服务设计
```python
# services/calculation_service.py
class CalculationService:
    async def calculate_period_comparison(self, current_data, previous_data)
    async def calculate_year_over_year(self, current_data, last_year_data)
    async def generate_comparison_metrics(self, base_data, comparison_periods)
```

### 3. 报表格式设计

#### 周报&月报格式 (7列)
| 字段名 | 真实值 | 环比变化1 | 环比变化2 | 环比变化3 | 同期变化 | 字段来源/计算方法 |
|--------|--------|-----------|-----------|-----------|----------|------------------|

#### 季报&半年报格式 (5列)
| 字段名 | 真实值 | 环比变化 | 同期变化 | 字段来源/计算方法 |
|--------|--------|----------|----------|------------------|

## API接口设计

### 核心接口规范

#### 1. 报表查询接口
```
POST /api/report/{period}/query
Content-Type: application/json

{
    "brand_id": "string",
    "date_range": {
        "start_date": "2024-01-01",
        "end_date": "2024-01-31"
    },
    "comparison_type": "sequential|year_over_year|both"
}
```

#### 2. 数据字段配置接口
```
GET /api/fields/config/{brand_id}
```

#### 3. 报表导出接口
```
POST /api/report/{period}/export
```

### 响应格式标准
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "report_data": [],
        "metadata": {
            "total_fields": 35,
            "calculation_time": "2024-01-15T10:30:00Z",
            "data_sources": ["member_system", "pos_system"]
        }
    }
}
```

## 数据库设计考虑

### 核心表结构
1. **品牌配置表**: 存储品牌基础信息和数据源配置
2. **字段定义表**: 存储30-40个数据字段的定义和计算规则
3. **报表缓存表**: 存储已生成的报表数据，提高查询性能
4. **数据源映射表**: 管理不同系统的数据字段映射关系

### 性能优化策略
- **数据预聚合**: 定期预计算常用指标
- **缓存机制**: Redis缓存热点数据
- **分库分表**: 按品牌ID进行数据分片
- **异步处理**: 大数据量报表异步生成

## 开发阶段规划

### Stage 1 (4-5天) - 当前阶段
**目标**: 基础框架搭建和核心查询功能

**完成内容**:
- [x] 项目结构搭建
- [x] FastAPI基础框架
- [x] Vue.js前端框架
- [ ] 数据库连接和基础模型
- [ ] 品牌数据查询API
- [ ] 基础报表展示页面

### Stage 2 (预计3-4天)
**目标**: 报表计算和对比功能

**计划内容**:
- [ ] 环比计算逻辑实现
- [ ] 同比计算逻辑实现
- [ ] 多时间维度支持
- [ ] 报表格式化输出

### Stage 3 (预计2-3天)
**目标**: 前端界面和用户体验

**计划内容**:
- [ ] 报表展示组件
- [ ] 数据可视化图表
- [ ] 导出功能
- [ ] 响应式设计

## 部署和运维

### 开发环境启动
```bash
# 后端启动
cd aiui-api
pip install -r requirements.txt
python main.py

# 前端启动
cd vue-element
npm install
npm run dev
```

### 生产环境部署
- **后端**: Docker + Nginx反向代理
- **前端**: 静态文件部署到CDN
- **数据库**: 主从复制 + 读写分离
- **监控**: 日志收集 + 性能监控

## 注意事项

1. **数据一致性**: 确保与现有系统数据展示保持一致
2. **性能要求**: 30-40个字段的查询需要优化响应时间
3. **扩展性**: 支持未来新增数据源和字段
4. **安全性**: API接口需要完善的认证和权限控制
5. **容错性**: 处理数据源不可用的情况

## 技术选型说明

### 为什么选择FastAPI
- 高性能异步框架，适合数据密集型应用
- 自动生成API文档，便于前后端协作
- 强类型支持，减少运行时错误
- 丰富的生态系统和中间件支持

### 为什么选择Vue.js + Element Plus
- 组件化开发，提高代码复用性
- Element Plus提供丰富的表格和图表组件
- 良好的TypeScript支持
- 活跃的社区和完善的文档

这个架构设计充分考虑了系统的可扩展性、性能要求和维护便利性，为后续的功能迭代和系统优化奠定了良好的基础。