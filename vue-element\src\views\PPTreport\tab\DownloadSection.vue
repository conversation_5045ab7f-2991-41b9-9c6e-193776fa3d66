<template>
  <el-card class="download-card" shadow="never" v-if="generatedFile">
    <template #header>
      <div class="card-header">
        <h3>
          <el-icon><Download /></el-icon>
          下载PPT报告
        </h3>
        <el-tag type="success">生成成功</el-tag>
      </div>
    </template>
    
    <div class="download-info">
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="info-item">
            <span class="label">文件名：</span>
            <span class="value">{{ generatedFile.fileName }}</span>
          </div>
          <div class="info-item">
            <span class="label">文件大小：</span>
            <span class="value">{{ generatedFile.fileSize }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="label">生成时间：</span>
            <span class="value">{{ formatTime(generatedFile.generatedAt) }}</span>
          </div>
          <div class="info-item">
            <span class="label">页数：</span>
            <span class="value">{{ generatedFile.slideCount || 12 }} 页</span>
          </div>
        </el-col>
      </el-row>
    </div>
    
    <!-- 12小时过期提醒 -->
    <el-alert
      title="下载提醒"
      type="warning"
      :closable="false"
      show-icon
      style="margin: 15px 0"
    >
      <template #default>
        <div>
          <p style="margin: 0 0 5px 0">请在生成后的12小时内下载，防止链接过期</p>
          <p style="margin: 0; font-size: 12px; color: #909399">
            如遇下载失败，请使用备用下载链接或联系管理员
          </p>
        </div>
      </template>
    </el-alert>
    
    <div class="download-buttons">
      <!-- 主下载按钮 -->
      <el-button
        type="success"
        size="large"
        @click="downloadPPT"
        class="download-btn"
      >
        <el-icon><Download /></el-icon>
        下载PPT报告
      </el-button>
      
      <!-- Excel数据下载按钮 -->
      <el-button
        v-if="generatedFile.dataDownloadUrl"
        type="primary"
        size="large"
        @click="downloadExcel"
        class="download-btn excel-download-btn"
        style="margin-left: 10px"
      >
        <el-icon><Document /></el-icon>
        下载数据Excel
      </el-button>
      
      <!-- 备用下载链接 -->
      <el-dropdown
        style="margin-left: 10px"
        @command="handleBackupDownload"
      >
        <el-button type="warning" size="large">
          备用下载 <el-icon class="el-icon--right"><arrow-down /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="ppt">
              <el-icon><Document /></el-icon>
              备用PPT下载
            </el-dropdown-item>
            <el-dropdown-item command="excel" v-if="generatedFile.dataDownloadUrl">
              <el-icon><DataAnalysis /></el-icon>
              备用Excel下载
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </el-card>
</template>

<script>
import { ElMessage } from 'element-plus'
import { Download, Document, DataAnalysis, ArrowDown } from '@element-plus/icons-vue'

export default {
  name: 'DownloadSection',
  
  components: {
    Download,
    Document,
    DataAnalysis,
    ArrowDown
  },
  
  props: {
    generatedFile: {
      type: Object,
      default: null
    },
    queryParams: {
      type: Object,
      required: true
    }
  },
  
  methods: {
    formatTime(timeString) {
      return new Date(timeString).toLocaleString('zh-CN')
    },
    
    async downloadPPT() {
      if (!this.generatedFile || !this.generatedFile.downloadUrl) {
        ElMessage.error('下载链接不可用')
        return
      }

      const isOSSUrl = this.generatedFile.downloadUrl.includes('oss-cn-beijing.aliyuncs.com')
      
      console.log('PPT下载信息:', {
        isOSSUrl,
        downloadUrl: this.generatedFile.downloadUrl,
        fileName: this.generatedFile.fileName
      })

      // OSS链接 - 直接下载
      if (isOSSUrl) {
        try {
          ElMessage.info('正在从OSS云存储下载...')
          
          // OSS链接直接打开下载，不需要HEAD检查
          window.open(this.generatedFile.downloadUrl, '_blank')
          
          ElMessage.success('开始下载PPT文件 (OSS云存储)')
          
        } catch (error) {
          console.error('OSS下载失败:', error)
          ElMessage.warning('OSS下载失败，请尝试备用下载')
          
          // OSS失败后自动尝试本地备用下载
          this.downloadLocalBackup('ppt')
        }
      } 
      // 本地链接 - 先检查有效性
      else {
        try {
          ElMessage.info('正在准备下载...')
          
          // 本地API链接先检查有效性
          const checkResponse = await fetch(this.generatedFile.downloadUrl, { method: 'HEAD' })
          if (!checkResponse.ok) {
            throw new Error(`文件不存在或已过期 (状态码: ${checkResponse.status})`)
          }
          
          // 创建下载链接
          const link = document.createElement('a')
          link.href = this.generatedFile.downloadUrl
          link.download = this.generatedFile.fileName || `会员分析报告_${new Date().getTime()}.pptx`
          link.target = '_blank'
          
          // 添加到DOM并触发下载
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          
          ElMessage.success('开始下载PPT文件 (本地存储)')
          
        } catch (error) {
          console.error('本地下载失败:', error)
          ElMessage.error(`下载失败: ${error.message}`)
          
          // 本地失败后直接尝试打开链接
          ElMessage.info('正在尝试直接打开下载链接...')
          window.open(this.generatedFile.downloadUrl, '_blank')
        }
      }
    },
    
    async downloadExcel() {
      if (!this.generatedFile || !this.generatedFile.dataDownloadUrl) {
        ElMessage.error('Excel下载链接不可用')
        return
      }
      
      const isOSSUrl = this.generatedFile.dataDownloadUrl.includes('oss-cn-beijing.aliyuncs.com')
      
      console.log('Excel下载信息:', {
        isOSSUrl,
        dataDownloadUrl: this.generatedFile.dataDownloadUrl
      })
      
      const excelFileName = this.generatedFile.fileName 
        ? this.generatedFile.fileName.replace('.pptx', '.xlsx')
        : `会员数据分析_${new Date().getTime()}.xlsx`
      
      // OSS链接 - 直接下载
      if (isOSSUrl) {
        try {
          ElMessage.info('正在从OSS云存储下载Excel...')
          window.open(this.generatedFile.dataDownloadUrl, '_blank')
          ElMessage.success('开始下载Excel文件 (OSS云存储)')
        } catch (error) {
          console.error('OSS Excel下载失败:', error)
          ElMessage.warning('OSS下载失败，请尝试备用下载')
          this.downloadLocalBackup('excel')
        }
      }
      // 本地链接 - 创建下载
      else {
        try {
          ElMessage.info('正在准备下载Excel数据文件...')
          
          // 创建下载链接
          const link = document.createElement('a')
          link.href = this.generatedFile.dataDownloadUrl
          link.download = excelFileName
          link.target = '_blank'
          
          // 添加到DOM并触发下载
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          
          ElMessage.success('开始下载Excel数据文件 (本地存储)')
          
        } catch (error) {
          console.error('Excel下载失败:', error)
          ElMessage.error(`Excel下载失败: ${error.message}`)
          
          // 尝试直接打开链接作为备用方案
          ElMessage.info('正在尝试直接打开下载链接...')
          window.open(this.generatedFile.dataDownloadUrl, '_blank')
        }
      }
    },
    
    // 本地备用下载辅助方法
    downloadLocalBackup(fileType) {
      if (!this.generatedFile) {
        ElMessage.error('没有可用的文件信息')
        return
      }
      
      const bid = this.generatedFile.bid || this.queryParams.bid || 'unknown'
      const taskId = this.generatedFile.taskUuid || this.generatedFile.taskId || 'unknown'
      
      // 清理taskId中可能的路径信息
      let cleanTaskId = taskId
      if (taskId.includes('/')) {
        const parts = taskId.split('/')
        cleanTaskId = parts[parts.length - 1]
        console.log('清理taskId路径:', taskId, '->', cleanTaskId)
      }
      
      const folderName = `${bid}_${cleanTaskId}`
      
      let fileName = ''
      if (fileType === 'ppt') {
        fileName = this.generatedFile.fileName || 'report.pptx'
      } else if (fileType === 'excel') {
        fileName = this.generatedFile.fileName 
          ? this.generatedFile.fileName.replace('.pptx', '.xlsx')
          : 'data.xlsx'
      }
      
      // 清理fileName中可能的路径信息
      if (fileName.includes('/')) {
        const parts = fileName.split('/')
        fileName = parts[parts.length - 1]
        console.log('清理fileName路径:', fileName)
      }
      
      const backupUrl = `/api/ppt-report/download/ppt-reports/${folderName}/${fileName}`
      const token = localStorage.getItem('access_token')
      const urlWithToken = token ? `${backupUrl}?token=${encodeURIComponent(token)}` : backupUrl
      
      console.log('本地备用下载路径:', {
        fileType,
        bid,
        cleanTaskId,
        folderName,
        fileName,
        fullUrl: urlWithToken
      })
      
      ElMessage.info(`正在使用本地备用链接下载${fileType === 'ppt' ? 'PPT' : 'Excel'}...`)
      window.open(urlWithToken, '_blank')
    },
    
    handleBackupDownload(command) {
      // 备用下载总是使用本地存储路径
      // command: 'ppt' 或 'excel'
      this.downloadLocalBackup(command)
    }
  }
}
</script>

<style scoped>
.download-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  color: #303133;
}

.download-info {
  margin-bottom: 20px;
}

.info-item {
  margin-bottom: 10px;
  font-size: 14px;
}

.info-item .label {
  color: #909399;
  margin-right: 8px;
}

.info-item .value {
  color: #303133;
  font-weight: 500;
}

.download-buttons {
  display: flex;
  align-items: center;
  margin-top: 20px;
}

.download-btn {
  font-size: 16px;
  padding: 12px 24px;
}

.excel-download-btn {
  background-color: #13ce66;
  border-color: #13ce66;
}

.excel-download-btn:hover {
  background-color: #42d885;
  border-color: #42d885;
}
</style>