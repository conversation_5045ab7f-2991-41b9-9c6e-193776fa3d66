"""
认证中间件
用于验证请求的Token和权限
"""
import logging
from typing import Optional, List
from fastapi import HTTPException, Security, Depends, status
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials

from ..utils.jwt_handler import verify_token
from ..schemas.user import UserRole
from ..services.bid_permission_service import BidPermissionService
from core.database import db

logger = logging.getLogger(__name__)

# HTTP Bearer认证
security = HTTPBearer()


class AuthMiddleware:
    """认证中间件类"""
    
    @staticmethod
    async def get_current_user(
        credentials: HTTPAuthorizationCredentials = Security(security)
    ) -> dict:
        """
        获取当前用户信息
        
        Args:
            credentials: HTTP认证凭据
            
        Returns:
            dict: 用户信息
            
        Raises:
            HTTPException: 认证失败时抛出
        """
        token = credentials.credentials
        
        # 验证Token
        payload = verify_token(token)
        
        if not payload:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证凭据",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        return payload
    
    @staticmethod
    def require_roles(allowed_roles: List[UserRole]):
        """
        角色权限装饰器
        
        Args:
            allowed_roles: 允许的角色列表
            
        Returns:
            依赖函数
        """
        async def role_checker(
            current_user: dict = Depends(AuthMiddleware.get_current_user)
        ):
            """检查用户角色"""
            user_role = current_user.get("role")
            
            # 超级管理员拥有所有权限
            if user_role == UserRole.SUPER_ADMIN.value:
                return current_user
            
            # 检查角色是否在允许列表中
            if user_role not in [role.value for role in allowed_roles]:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="权限不足"
                )
            
            return current_user
        
        return role_checker
    
    @staticmethod
    async def check_bid_permission(
        bid: str,
        permission: str,
        current_user: dict = Depends(get_current_user)
    ) -> bool:
        """
        检查BID权限（使用新的权限关联表）
        
        Args:
            bid: BID代码
            permission: 需要的权限（read/write/admin）
            current_user: 当前用户
            
        Returns:
            bool: 是否有权限
        """
        # 超级管理员拥有所有权限
        if current_user.get("role") == UserRole.SUPER_ADMIN.value:
            return True
        
        # 使用新的权限服务检查
        permission_service = BidPermissionService(db.task_management_pool)
        username = current_user.get("username")
        
        if not username:
            return False
            
        has_permission = await permission_service.check_user_bid_permission(
            username=username,
            bid=bid,
            permission=permission
        )
        
        return has_permission
    
    @staticmethod
    async def check_sid_permission(
        bid: str,
        sid: str,
        current_user: dict = Depends(get_current_user)
    ) -> bool:
        """
        检查SID权限（使用新的权限关联表）
        
        Args:
            bid: BID代码
            sid: SID代码
            current_user: 当前用户
            
        Returns:
            bool: 是否有权限
        """
        # 超级管理员拥有所有权限
        if current_user.get("role") == UserRole.SUPER_ADMIN.value:
            return True
        
        # 使用新的权限服务检查
        permission_service = BidPermissionService(db.task_management_pool)
        username = current_user.get("username")
        
        if not username:
            return False
            
        has_permission = await permission_service.check_user_sid_permission(
            username=username,
            bid=bid,
            sid=sid
        )
        
        return has_permission
    
    @staticmethod
    def require_bid_permission(bid: str, permission: str = "read"):
        """
        BID权限装饰器
        
        Args:
            bid: BID代码
            permission: 需要的权限
            
        Returns:
            依赖函数
        """
        async def permission_checker(
            current_user: dict = Depends(AuthMiddleware.get_current_user)
        ):
            """检查BID权限"""
            has_permission = await AuthMiddleware.check_bid_permission(
                bid, permission, current_user
            )
            
            if not has_permission:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"没有访问BID {bid} 的 {permission} 权限"
                )
            
            return current_user
        
        return permission_checker
    
    @staticmethod
    async def get_optional_user(
        credentials: Optional[HTTPAuthorizationCredentials] = Security(HTTPBearer(auto_error=False))
    ) -> Optional[dict]:
        """
        获取可选的用户信息（用于可选认证的接口）
        
        Args:
            credentials: HTTP认证凭据
            
        Returns:
            dict: 用户信息，未认证时返回None
        """
        if not credentials:
            return None
        
        token = credentials.credentials
        payload = verify_token(token)
        
        return payload


# 导出便捷函数
get_current_user = AuthMiddleware.get_current_user
require_roles = AuthMiddleware.require_roles
check_bid_permission = AuthMiddleware.check_bid_permission
check_sid_permission = AuthMiddleware.check_sid_permission
require_bid_permission = AuthMiddleware.require_bid_permission
get_optional_user = AuthMiddleware.get_optional_user

# 预定义的角色依赖
require_super_admin = require_roles([UserRole.SUPER_ADMIN])
require_admin = require_roles([UserRole.SUPER_ADMIN, UserRole.ADMIN])
require_user = require_roles([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.USER])