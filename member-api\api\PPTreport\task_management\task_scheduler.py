"""
任务调度器模块
负责定期检查和处理任务状态，包括重试失败任务、检查超时任务等
"""

import asyncio
import logging
from typing import Optional
from datetime import datetime, timedelta
from core.config import settings
from .models import TaskStatus
from .task_manager import TaskManager

logger = logging.getLogger(__name__)


class TaskScheduler:
    """任务调度器"""
    
    def __init__(self):
        """初始化任务调度器"""
        self.task_manager = TaskManager()
        self._running = False
        self._scheduler_tasks = []
        
        # 从配置读取调度参数
        self.retry_check_interval = getattr(settings, 'RETRY_CHECK_INTERVAL', 300)
        self.timeout_check_interval = getattr(settings, 'TIMEOUT_CHECK_INTERVAL', 1200)
        self.auto_reset_failed_tasks = getattr(settings, 'AUTO_RESET_FAILED_TASKS', True)
        self.auto_reset_interval = getattr(settings, 'AUTO_RESET_INTERVAL', 300)
        
        logger.info(f"任务调度器初始化完成：")
        logger.info(f"  - 重试检查间隔: {self.retry_check_interval}秒")
        logger.info(f"  - 超时检查间隔: {self.timeout_check_interval}秒")
        logger.info(f"  - 自动重置失败任务: {self.auto_reset_failed_tasks}")
        if self.auto_reset_failed_tasks:
            logger.info(f"  - 自动重置间隔: {self.auto_reset_interval}秒")
    
    async def start(self):
        """启动调度器"""
        if self._running:
            logger.warning("任务调度器已在运行中")
            return
        
        self._running = True
        
        # 启动各个调度任务
        self._scheduler_tasks = [
            asyncio.create_task(self._retry_failed_tasks_loop()),
            asyncio.create_task(self._check_timeout_tasks_loop()),
        ]
        
        # 如果启用了自动重置，启动重置任务
        if self.auto_reset_failed_tasks:
            self._scheduler_tasks.append(
                asyncio.create_task(self._auto_reset_failed_tasks_loop())
            )
        
        logger.info("任务调度器已启动")
    
    async def stop(self):
        """停止调度器"""
        self._running = False
        
        # 取消所有调度任务
        for task in self._scheduler_tasks:
            task.cancel()
        
        # 等待所有任务完成
        if self._scheduler_tasks:
            await asyncio.gather(*self._scheduler_tasks, return_exceptions=True)
        
        self._scheduler_tasks = []
        logger.info("任务调度器已停止")
    
    async def _retry_failed_tasks_loop(self):
        """重试失败任务的循环"""
        logger.info("失败任务重试检查器已启动")
        
        while self._running:
            try:
                await self._check_and_retry_failed_tasks()
                await asyncio.sleep(self.retry_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"重试失败任务时出错: {e}", exc_info=True)
                await asyncio.sleep(60)  # 出错后等待1分钟
    
    async def _check_and_retry_failed_tasks(self):
        """检查并重试失败的任务"""
        try:
            # 获取可重试的失败任务
            failed_tasks = await self.task_manager.get_retryable_failed_tasks()
            
            if failed_tasks:
                logger.info(f"发现 {len(failed_tasks)} 个可重试的失败任务")
                
                for task in failed_tasks:
                    if task.retry_count < task.max_retry_count:
                        # 重置任务状态为待处理，准备重试
                        await self.task_manager.reset_task_for_retry(task.id)
                        logger.info(f"任务 {task.id} 已重置为待处理状态，准备第 {task.retry_count + 1} 次重试")
                    else:
                        logger.warning(f"任务 {task.id} 已达到最大重试次数 {task.max_retry_count}，不再重试")
        
        except Exception as e:
            logger.error(f"检查失败任务时出错: {e}")
    
    async def _check_timeout_tasks_loop(self):
        """检查超时任务的循环"""
        logger.info("超时任务检查器已启动")
        
        while self._running:
            try:
                await self._check_timeout_tasks()
                await asyncio.sleep(self.timeout_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"检查超时任务时出错: {e}", exc_info=True)
                await asyncio.sleep(60)  # 出错后等待1分钟
    
    async def _check_timeout_tasks(self):
        """检查并处理超时的任务"""
        try:
            # 定义任务超时时间（例如：30分钟）
            timeout_minutes = 30
            timeout_threshold = datetime.now() - timedelta(minutes=timeout_minutes)
            
            # 获取处理中但可能超时的任务
            processing_tasks = await self.task_manager.get_processing_tasks()
            
            timeout_count = 0
            for task in processing_tasks:
                if task.started_at and task.started_at < timeout_threshold:
                    # 任务已超时
                    timeout_count += 1
                    logger.warning(f"任务 {task.id} 已超时（开始时间: {task.started_at}）")
                    
                    # 标记任务为失败
                    await self.task_manager.mark_task_failed(
                        task.id,
                        f"任务执行超时（超过{timeout_minutes}分钟）",
                        error_details={"timeout": True, "started_at": str(task.started_at)}
                    )
            
            if timeout_count > 0:
                logger.info(f"处理了 {timeout_count} 个超时任务")
        
        except Exception as e:
            logger.error(f"检查超时任务时出错: {e}")
    
    async def _auto_reset_failed_tasks_loop(self):
        """自动重置失败任务的循环"""
        logger.info("失败任务自动重置器已启动")
        
        while self._running:
            try:
                await self._auto_reset_old_failed_tasks()
                await asyncio.sleep(self.auto_reset_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"自动重置失败任务时出错: {e}", exc_info=True)
                await asyncio.sleep(60)  # 出错后等待1分钟
    
    async def _auto_reset_old_failed_tasks(self):
        """自动重置长时间失败的任务"""
        try:
            # 定义失败任务保留时间（例如：24小时）
            reset_threshold_hours = 24
            reset_threshold = datetime.now() - timedelta(hours=reset_threshold_hours)
            
            # 获取需要重置的旧失败任务
            old_failed_tasks = await self.task_manager.get_old_failed_tasks(reset_threshold)
            
            if old_failed_tasks:
                logger.info(f"发现 {len(old_failed_tasks)} 个需要自动重置的旧失败任务")
                
                for task in old_failed_tasks:
                    # 重置任务状态为待处理
                    await self.task_manager.reset_task_for_retry(task.id, reset_retry_count=True)
                    logger.info(f"任务 {task.id} 已自动重置（失败时间超过{reset_threshold_hours}小时）")
        
        except Exception as e:
            logger.error(f"自动重置失败任务时出错: {e}")
    
    async def manually_retry_task(self, task_id: int) -> bool:
        """
        手动重试指定任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功重置任务
        """
        try:
            task = await self.task_manager.get_task_by_id(task_id)
            
            if not task:
                logger.error(f"任务 {task_id} 不存在")
                return False
            
            if task.status != TaskStatus.FAILED:
                logger.warning(f"任务 {task_id} 状态为 {task.status}，只能重试失败的任务")
                return False
            
            # 重置任务为待处理状态
            await self.task_manager.reset_task_for_retry(task_id)
            logger.info(f"任务 {task_id} 已手动重置为待处理状态")
            
            return True
            
        except Exception as e:
            logger.error(f"手动重试任务失败: {e}")
            return False
    
    def get_scheduler_status(self) -> dict:
        """获取调度器状态"""
        return {
            "is_running": self._running,
            "retry_check_interval": self.retry_check_interval,
            "timeout_check_interval": self.timeout_check_interval,
            "auto_reset_enabled": self.auto_reset_failed_tasks,
            "auto_reset_interval": self.auto_reset_interval if self.auto_reset_failed_tasks else None,
            "active_schedulers": len([t for t in self._scheduler_tasks if not t.done()])
        }


# 全局调度器实例
task_scheduler_instance: Optional[TaskScheduler] = None


def get_task_scheduler() -> TaskScheduler:
    """获取全局任务调度器实例"""
    global task_scheduler_instance
    if task_scheduler_instance is None:
        task_scheduler_instance = TaskScheduler()
    return task_scheduler_instance


async def init_task_scheduler():
    """初始化并启动任务调度器"""
    scheduler = get_task_scheduler()
    await scheduler.start()
    return scheduler


async def shutdown_task_scheduler():
    """关闭任务调度器"""
    if task_scheduler_instance:
        await task_scheduler_instance.stop()