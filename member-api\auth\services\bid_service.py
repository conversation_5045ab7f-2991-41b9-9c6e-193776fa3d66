"""
BID配置管理服务
处理BID配置的CRUD操作、SID管理、配额管理等
"""
import json
import logging
from typing import Optional, List, Dict, Any
from datetime import datetime
import aiomysql

from ..schemas.bid_config import (
    BidConfigCreate, BidConfigUpdate, BidConfigResponse, BidConfigList,
    SidConfig, BidSidMapping, BidStatus
)

logger = logging.getLogger(__name__)


class BidService:
    """BID管理服务类"""
    
    def __init__(self, db_pool):
        """
        初始化BID服务
        
        Args:
            db_pool: 数据库连接池
        """
        self.db_pool = db_pool
    
    async def get_bid_list(
        self,
        page: int = 1,
        page_size: int = 20,
        status: Optional[str] = None,
        owner_username: Optional[str] = None,
        search: Optional[str] = None
    ) -> BidConfigList:
        """
        获取BID配置列表
        
        Args:
            page: 页码
            page_size: 每页大小
            status: 状态筛选
            owner_user_id: 所有者筛选
            search: 搜索关键词
            
        Returns:
            BidConfigList: BID配置列表响应
        """
        try:
            offset = (page - 1) * page_size
            
            # 构建查询条件
            where_conditions = ["b.deleted_at IS NULL"]
            params = []
            
            if status:
                where_conditions.append("b.status = %s")
                params.append(status)
            
            if owner_username:
                where_conditions.append("b.owner_username = %s")
                params.append(owner_username)
            
            if search:
                where_conditions.append("(b.bid LIKE %s OR b.bid_name LIKE %s OR b.company_name LIKE %s)")
                search_pattern = f"%{search}%"
                params.extend([search_pattern, search_pattern, search_pattern])
            
            where_clause = " AND ".join(where_conditions)
            
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 获取总数
                    count_sql = f"SELECT COUNT(*) as total FROM member_report_bid_configs b WHERE {where_clause}"
                    await cursor.execute(count_sql, params)
                    total = (await cursor.fetchone())['total']
                    
                    # 获取列表
                    list_sql = f"""
                    SELECT b.*
                    FROM member_report_bid_configs b
                    WHERE {where_clause}
                    ORDER BY b.created_at ASC
                    LIMIT %s OFFSET %s
                    """
                    params.extend([page_size, offset])
                    await cursor.execute(list_sql, params)
                    configs = await cursor.fetchall()
                    
                    # 转换为响应模型
                    items = []
                    for config in configs:
                        items.append(BidConfigResponse(
                            id=config['id'],
                            bid=config['bid'],
                            bid_name=config.get('bid_name', config.get('description', '')),
                            description=config.get('description'),
                            business_type=config.get('business_type'),
                            owner_username=config.get('owner_username'),
                            status=config['status'],
                            sid_list=json.loads(config['sid_list']) if config.get('sid_list') else None,
                            contact_name=config.get('contact_name'),
                            contact_phone=config.get('contact_phone'),
                            contact_email=config.get('contact_email'),
                            company_name=config.get('company_name'),
                            industry=config.get('industry'),
                            created_at=config['created_at'].strftime("%Y-%m-%d %H:%M:%S") if config.get('created_at') else '',
                            updated_at=config['updated_at'].strftime("%Y-%m-%d %H:%M:%S") if config.get('updated_at') else ''
                        ))
                    
                    return BidConfigList(
                        total=total,
                        items=items,
                        page=page,
                        page_size=page_size
                    )
                    
        except Exception as e:
            logger.error(f"获取BID配置列表失败: {e}")
            return BidConfigList(total=0, items=[], page=page, page_size=page_size)
    
    async def get_bid_by_id(self, bid_id: int) -> Optional[BidConfigResponse]:
        """
        根据ID获取BID配置
        
        Args:
            bid_id: BID配置ID
            
        Returns:
            BidConfigResponse: BID配置信息
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    sql = """
                    SELECT b.*
                    FROM member_report_bid_configs b
                    WHERE b.id = %s AND b.deleted_at IS NULL
                    """
                    await cursor.execute(sql, (bid_id,))
                    config = await cursor.fetchone()
                    
                    if not config:
                        return None
                    
                    return BidConfigResponse(
                        id=config['id'],
                        bid=config['bid'],
                        bid_name=config.get('bid_name', ''),  # 使用bid_name字段
                        description=config.get('description'),
                        business_type=config.get('business_type'),
                        owner_username=config.get('owner_username'),
                        status=config['status'],
                        sid_list=json.loads(config['sid_list']) if config.get('sid_list') else None,
                        created_at=config['created_at'].strftime("%Y-%m-%d %H:%M:%S") if config.get('created_at') else '',
                        updated_at=config['updated_at'].strftime("%Y-%m-%d %H:%M:%S") if config.get('updated_at') else ''
                    )
                    
        except Exception as e:
            logger.error(f"获取BID配置失败: {e}")
            return None
    
    async def get_bid_by_code(self, bid: str) -> Optional[BidConfigResponse]:
        """
        根据BID代码获取配置
        
        Args:
            bid: BID代码
            
        Returns:
            BidConfigResponse: BID配置信息
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    sql = """
                    SELECT b.*
                    FROM member_report_bid_configs b
                    WHERE b.bid = %s AND b.deleted_at IS NULL
                    """
                    await cursor.execute(sql, (bid,))
                    config = await cursor.fetchone()
                    
                    if not config:
                        return None
                    
                    return BidConfigResponse(
                        id=config['id'],
                        bid=config['bid'],
                        bid_name=config.get('bid_name', ''),  # 使用bid_name字段
                        description=config.get('description'),
                        business_type=config.get('business_type'),
                        owner_username=config.get('owner_username'),
                        status=config['status'],
                        sid_list=json.loads(config['sid_list']) if config.get('sid_list') else None,
                        created_at=config['created_at'].strftime("%Y-%m-%d %H:%M:%S") if config.get('created_at') else '',
                        updated_at=config['updated_at'].strftime("%Y-%m-%d %H:%M:%S") if config.get('updated_at') else ''
                    )
                    
        except Exception as e:
            logger.error(f"获取BID配置失败: {e}")
            return None
    
    async def create_bid(self, bid_data: BidConfigCreate) -> Optional[BidConfigResponse]:
        """
        创建BID配置
        
        Args:
            bid_data: BID创建数据
            
        Returns:
            BidConfigResponse: 创建的BID配置信息
        """
        try:
            # 检查当前用户是否已经申请过这个BID
            if await self._check_user_bid_exists(bid_data.bid, bid_data.owner_username):
                raise ValueError(f"您已经申请过BID代码 {bid_data.bid}")
            
            async with self.db_pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    sql = """
                    INSERT INTO member_report_bid_configs 
                    (bid, bid_name, description, business_type, owner_username, status,
                     sid_list, contact_name, contact_phone, contact_email,
                     company_name, industry, remark,
                     created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                    """
                    
                    await cursor.execute(sql, (
                        bid_data.bid,
                        bid_data.bid_name,
                        bid_data.description,
                        bid_data.business_type.value if bid_data.business_type else None,
                        bid_data.owner_username,
                        BidStatus.ACTIVE.value,
                        json.dumps(bid_data.sid_list) if bid_data.sid_list else None,
                        bid_data.contact_name,
                        bid_data.contact_phone,
                        bid_data.contact_email,
                        bid_data.company_name,
                        bid_data.industry,
                        bid_data.remark
                    ))
                    
                    bid_id = cursor.lastrowid
                    await conn.commit()
                    
                    # 返回创建的BID配置信息
                    return await self.get_bid_by_id(bid_id)
                    
        except ValueError as e:
            logger.error(f"创建BID配置失败: {e}")
            raise
        except Exception as e:
            logger.error(f"创建BID配置失败: {e}")
            return None
    
    async def update_bid(self, bid_id: int, bid_data: BidConfigUpdate) -> Optional[BidConfigResponse]:
        """
        更新BID配置
        
        Args:
            bid_id: BID配置ID
            bid_data: 更新数据
            
        Returns:
            BidConfigResponse: 更新后的BID配置信息
        """
        try:
            # 构建更新字段
            update_fields = []
            params = []
            
            if bid_data.bid_name is not None:
                update_fields.append("bid_name = %s")
                params.append(bid_data.bid_name)
            
            if bid_data.description is not None:
                update_fields.append("description = %s")
                params.append(bid_data.description)
            
            if bid_data.business_type is not None:
                update_fields.append("business_type = %s")
                params.append(bid_data.business_type.value)
            
            if bid_data.owner_username is not None:
                update_fields.append("owner_username = %s")
                params.append(bid_data.owner_username)
            
            if bid_data.status is not None:
                update_fields.append("status = %s")
                params.append(bid_data.status.value)
            
            if bid_data.sid_list is not None:
                update_fields.append("sid_list = %s")
                params.append(json.dumps(bid_data.sid_list))
            
            if bid_data.contact_name is not None:
                update_fields.append("contact_name = %s")
                params.append(bid_data.contact_name)
            
            if bid_data.contact_phone is not None:
                update_fields.append("contact_phone = %s")
                params.append(bid_data.contact_phone)
            
            if bid_data.contact_email is not None:
                update_fields.append("contact_email = %s")
                params.append(bid_data.contact_email)
            
            if bid_data.company_name is not None:
                update_fields.append("company_name = %s")
                params.append(bid_data.company_name)
            
            if bid_data.industry is not None:
                update_fields.append("industry = %s")
                params.append(bid_data.industry)
            
            if bid_data.remark is not None:
                update_fields.append("remark = %s")
                params.append(bid_data.remark)
            
            if not update_fields:
                return await self.get_bid_by_id(bid_id)
            
            # 添加更新时间
            update_fields.append("updated_at = NOW()")
            
            async with self.db_pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    sql = f"""
                    UPDATE member_report_bid_configs 
                    SET {', '.join(update_fields)}
                    WHERE id = %s AND deleted_at IS NULL
                    """
                    params.append(bid_id)
                    
                    await cursor.execute(sql, params)
                    await conn.commit()
                    
                    return await self.get_bid_by_id(bid_id)
                    
        except Exception as e:
            logger.error(f"更新BID配置失败: {e}")
            return None
    
    async def delete_bid(self, bid_id: int, hard_delete: bool = False) -> bool:
        """
        删除BID配置
        
        Args:
            bid_id: BID配置ID
            hard_delete: 是否硬删除
            
        Returns:
            bool: 是否成功
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    if hard_delete:
                        # 硬删除
                        sql = "DELETE FROM member_report_bid_configs WHERE id = %s"
                    else:
                        # 软删除
                        sql = """
                        UPDATE member_report_bid_configs 
                        SET deleted_at = NOW(), status = 'inactive'
                        WHERE id = %s AND deleted_at IS NULL
                        """
                    
                    await cursor.execute(sql, (bid_id,))
                    await conn.commit()
                    
                    return cursor.rowcount > 0
                    
        except Exception as e:
            logger.error(f"删除BID配置失败: {e}")
            return False
    
    async def update_sid_list(self, bid_id: int, sid_list: List[str]) -> bool:
        """
        更新BID的SID列表
        
        Args:
            bid_id: BID配置ID
            sid_list: SID列表
            
        Returns:
            bool: 是否成功
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    sql = """
                    UPDATE member_report_bid_configs 
                    SET sid_list = %s, updated_at = NOW()
                    WHERE id = %s AND deleted_at IS NULL
                    """
                    
                    await cursor.execute(sql, (json.dumps(sid_list), bid_id))
                    await conn.commit()
                    
                    return cursor.rowcount > 0
                    
        except Exception as e:
            logger.error(f"更新SID列表失败: {e}")
            return False
    
    
    async def get_user_accessible_bids(self, user_id: int) -> List[str]:
        """
        获取用户可访问的BID列表
        
        Args:
            user_id: 用户ID
            
        Returns:
            List[str]: BID列表
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    # 获取用户的BID权限
                    sql = """
                    SELECT bid_permissions 
                    FROM member_report_users 
                    WHERE id = %s AND deleted_at IS NULL
                    """
                    await cursor.execute(sql, (user_id,))
                    result = await cursor.fetchone()
                    
                    if not result or not result[0]:
                        return []
                    
                    bid_permissions = json.loads(result[0])
                    bids = []
                    
                    for perm in bid_permissions:
                        if perm.get('bid') == '*':
                            # 如果有通配符权限，返回所有活跃的BID
                            sql = """
                            SELECT bid FROM member_report_bid_configs 
                            WHERE status = 'active' AND deleted_at IS NULL
                            """
                            await cursor.execute(sql)
                            all_bids = await cursor.fetchall()
                            return [b[0] for b in all_bids]
                        else:
                            bids.append(perm.get('bid'))
                    
                    return bids
                    
        except Exception as e:
            logger.error(f"获取用户可访问BID失败: {e}")
            return []
    
    async def get_bid_statistics(self, bid_id: Optional[int] = None) -> Dict[str, Any]:
        """
        获取BID统计信息
        
        Args:
            bid_id: 特定BID的ID（可选）
            
        Returns:
            Dict: 统计信息
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 如果指定了bid_id，返回特定BID的统计
                    if bid_id:
                        sql = """
                        SELECT bid, bid_name, status, sid_list
                        FROM member_report_bid_configs
                        WHERE id = %s AND deleted_at IS NULL
                        """
                        await cursor.execute(sql, (bid_id,))
                        bid_info = await cursor.fetchone()
                        
                        if not bid_info:
                            return {
                                "today_queries": 0,
                                "month_queries": 0,
                                "quota_limit": 0,
                                "quota_used": 0
                            }
                        
                        # 这里应该从实际的使用记录表获取数据
                        # 目前返回默认值
                        return {
                            "bid": bid_info['bid'],
                            "bid_name": bid_info['bid_name'],
                            "status": bid_info['status'],
                            "today_queries": 0,  # TODO: 从实际使用记录获取
                            "month_queries": 0,  # TODO: 从实际使用记录获取
                            "quota_limit": 0,    # BID没有配额限制
                            "quota_used": 0      # BID没有配额使用
                        }
                    
                    # 否则返回整体统计
                    sql = """
                    SELECT 
                        COUNT(*) as total_bids,
                        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_count,
                        SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_count,
                        COUNT(DISTINCT owner_user_id) as unique_owners
                    FROM member_report_bid_configs
                    WHERE deleted_at IS NULL
                    """
                    
                    await cursor.execute(sql)
                    stats = await cursor.fetchone()
                    
                    # 获取SID总数
                    sql = """
                    SELECT SUM(JSON_LENGTH(sid_list)) as total_sids
                    FROM member_report_bid_configs
                    WHERE deleted_at IS NULL AND sid_list IS NOT NULL
                    """
                    await cursor.execute(sql)
                    sid_result = await cursor.fetchone()
                    
                    return {
                        "total_bids": stats['total_bids'] or 0,
                        "status_distribution": {
                            "active": stats['active_count'] or 0,
                            "inactive": stats['inactive_count'] or 0
                        },
                        "unique_owners": stats['unique_owners'] or 0,
                        "total_sids": sid_result['total_sids'] or 0
                    }
                    
        except Exception as e:
            logger.error(f"获取BID统计失败: {e}")
            return {
                "total_bids": 0,
                "status_distribution": {},
                "unique_owners": 0,
                "total_sids": 0
            }
    
    # ========== 私有方法 ==========
    
    async def _check_bid_exists(self, bid: str, exclude_id: Optional[int] = None) -> bool:
        """检查BID是否存在"""
        async with self.db_pool.acquire() as conn:
            async with conn.cursor() as cursor:
                sql = "SELECT COUNT(*) FROM member_report_bid_configs WHERE bid = %s AND deleted_at IS NULL"
                params = [bid]
                
                if exclude_id:
                    sql += " AND id != %s"
                    params.append(exclude_id)
                
                await cursor.execute(sql, params)
                count = (await cursor.fetchone())[0]
                return count > 0
    
    async def _check_user_bid_exists(self, bid: str, owner_username: str) -> bool:
        """检查特定用户是否已经申请过该BID"""
        async with self.db_pool.acquire() as conn:
            async with conn.cursor() as cursor:
                sql = """SELECT COUNT(*) FROM member_report_bid_configs 
                         WHERE bid = %s AND owner_username = %s AND deleted_at IS NULL"""
                await cursor.execute(sql, (bid, owner_username))
                count = (await cursor.fetchone())[0]
                return count > 0