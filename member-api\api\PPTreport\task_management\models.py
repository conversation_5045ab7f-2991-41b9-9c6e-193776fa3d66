"""
任务数据模型定义
"""

from enum import Enum
from datetime import datetime
from typing import Optional, Dict, Any
from dataclasses import dataclass, field
import json
import uuid


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"          # 待处理
    PROCESSING = "processing"    # 处理中
    COMPLETED = "completed"      # 已完成
    FAILED = "failed"           # 失败
    CANCELLED = "cancelled"     # 已取消


class TaskPriority(Enum):
    """任务优先级枚举"""
    LOWEST = 1   # 最低
    LOW = 2      # 低
    NORMAL = 3   # 正常
    HIGH = 4     # 高
    HIGHEST = 5  # 最高


@dataclass
class TaskModel:
    """任务数据模型"""
    
    # 基础字段
    id: Optional[int] = None
    task_uuid: str = field(default_factory=lambda: str(uuid.uuid4()))
    user_id: Optional[str] = None  # 修改为字符串类型，存储username
    bid: Optional[str] = None
    sid: Optional[str] = None
    
    # 任务信息
    task_type: Optional[str] = None
    task_name: Optional[str] = None
    file_excel_size: int = 0
    file_PPT_size: int = 0
    priority: int = TaskPriority.NORMAL.value
    
    # 文件和结果
    result_file_url: Optional[str] = None
    ppt_oss_url: Optional[str] = None
    excel_oss_url: Optional[str] = None
    report_type: Optional[str] = None
    
    # 状态和进度
    status: str = TaskStatus.PENDING.value
    progress: int = 0
    current_step: Optional[str] = None
    
    # 统计信息
    task_start_ftime: Optional[str] = None
    task_end_ftime: Optional[str] = None
    tokens_used: int = 0
    
    # 时间字段
    estimated_duration: Optional[int] = None
    actual_duration: Optional[int] = None
    created_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # 错误处理
    error_info: Optional[Dict[str, Any]] = None
    retry_count: int = 0
    max_retry_count: int = 3
    
    # 扩展功能
    parent_task_id: Optional[int] = None
    notification_config: Optional[Dict[str, Any]] = None
    source: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = {
            'id': self.id,
            'task_uuid': self.task_uuid,
            'user_id': self.user_id,
            'bid': self.bid,
            'sid': self.sid,
            'task_type': self.task_type,
            'task_name': self.task_name,
            'file_excel_size': self.file_excel_size,
            'file_PPT_size': self.file_PPT_size,
            'priority': self.priority,
            'result_file_url': self.result_file_url,
            'ppt_oss_url': self.ppt_oss_url,
            'excel_oss_url': self.excel_oss_url,
            'report_type': self.report_type,
            'status': self.status,
            'progress': self.progress,
            'current_step': self.current_step,
            'task_start_ftime': self.task_start_ftime,
            'task_end_ftime': self.task_end_ftime,
            'tokens_used': self.tokens_used,
            'estimated_duration': self.estimated_duration,
            'actual_duration': self.actual_duration,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'error_info': json.dumps(self.error_info) if self.error_info else None,
            'retry_count': self.retry_count,
            'max_retry_count': self.max_retry_count,
            'parent_task_id': self.parent_task_id,
            'notification_config': json.dumps(self.notification_config) if self.notification_config else None,
            'source': self.source
        }
        # 移除None值
        return {k: v for k, v in data.items() if v is not None}
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TaskModel':
        """从字典创建实例"""
        # 处理JSON字段
        if 'error_info' in data and isinstance(data['error_info'], str):
            try:
                data['error_info'] = json.loads(data['error_info'])
            except:
                data['error_info'] = None
                
        if 'notification_config' in data and isinstance(data['notification_config'], str):
            try:
                data['notification_config'] = json.loads(data['notification_config'])
            except:
                data['notification_config'] = None
        
        # 处理日期时间字段
        for field_name in ['created_at', 'started_at', 'completed_at']:
            if field_name in data and data[field_name]:
                if isinstance(data[field_name], str):
                    try:
                        data[field_name] = datetime.fromisoformat(data[field_name])
                    except:
                        data[field_name] = None
        
        return cls(**data)
    
    def update_status(self, status: TaskStatus, current_step: Optional[str] = None):
        """更新任务状态"""
        self.status = status.value
        if current_step:
            self.current_step = current_step
        
        # 根据状态更新时间戳
        if status == TaskStatus.PROCESSING and not self.started_at:
            self.started_at = datetime.now()
        elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            self.completed_at = datetime.now()
            if self.started_at:
                duration = (self.completed_at - self.started_at).total_seconds() / 60
                self.actual_duration = int(duration)
    
    def update_progress(self, progress: int, current_step: Optional[str] = None):
        """更新任务进度"""
        self.progress = min(100, max(0, progress))
        if current_step:
            self.current_step = current_step
    
    def add_error(self, error_message: str, error_details: Optional[Dict] = None):
        """添加错误信息"""
        if not self.error_info:
            self.error_info = {"errors": []}
        
        error_entry = {
            "timestamp": datetime.now().isoformat(),
            "message": error_message,
            "details": error_details
        }
        
        if "errors" not in self.error_info:
            self.error_info["errors"] = []
        
        self.error_info["errors"].append(error_entry)
        self.retry_count += 1
    
    def can_retry(self) -> bool:
        """检查是否可以重试"""
        return self.retry_count < self.max_retry_count
    
    def is_terminal_status(self) -> bool:
        """检查是否为终止状态"""
        return self.status in [
            TaskStatus.COMPLETED.value,
            TaskStatus.FAILED.value,
            TaskStatus.CANCELLED.value
        ]
    
    def __repr__(self) -> str:
        """字符串表示"""
        return (f"TaskModel(id={self.id}, task_uuid={self.task_uuid}, "
                f"task_name={self.task_name}, status={self.status}, "
                f"progress={self.progress}%)")