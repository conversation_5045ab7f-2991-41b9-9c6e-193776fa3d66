# -*- coding: utf-8 -*-
"""
新增会员数据图片生成模块
生成新增会员、取关会员、取关占比的图表
"""

import datetime
import logging
import os
from typing import Dict, Any, List, Tuple, Optional
from pathlib import Path
import asyncio

# 设置matplotlib后端（在导入pyplot之前）
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import pandas as pd
import numpy as np

# 导入企微查询函数
from api.query.Wechat.Wechat import get_wechat_new_customers_count, get_merchant_id_by_boss_id

# 导入智能标签管理器
from .SmartLabels import add_smart_bar_labels, add_smart_line_labels

# 导入数据收集装饰器
from ..excel import collect_chart_data

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class NewMembersPicGenerator:
    """新增会员图片生成器"""

    def __init__(self, bid: str, image_manager):
        """
        初始化图片生成器

        Args:
            bid: 品牌ID
            image_manager: 图片管理器实例
        """
        self.bid = bid
        self.image_manager = image_manager

    def _extract_param(self, query_params, key, default=None):
        """
        从查询参数中提取值

        Args:
            query_params: 查询参数（对象或字典）
            key: 参数键
            default: 默认值

        Returns:
            参数值
        """
        if hasattr(query_params, key):
            return getattr(query_params, key)
        elif isinstance(query_params, dict):
            return query_params.get(key, default)
        else:
            return default

    async def generate_new_members_charts(self, query_params) -> Dict[str, str]:
        """
        生成新增会员图表

        Args:
            query_params: 查询参数（对象或字典）

        Returns:
            Dict: 包含两张图片路径的字典
        """
        try:
            logger.info(f"开始生成新增会员图表 - bid: {self.bid}")
            logger.info(f"查询参数类型: {type(query_params)}")
            logger.info(f"图片管理器会话目录: {self.image_manager.session_dir}")

            # 计算时间范围
            current_date = datetime.datetime.now()
            end_date = self._extract_param(query_params, 'end_date', '2025-06-30')
            query_end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d")

            logger.info(f"时间参数 - 当前日期: {current_date}, 查询结束日期: {query_end_date}")

            # 计算去年和今年的时间范围
            last_year = query_end_date.year - 1
            this_year = query_end_date.year

            # 去年：完整的12个月
            last_year_ranges = self._generate_monthly_ranges(last_year, 1, 12)

            # 今年：从1月到查询结束月份（如果是当前年份，则到前一天）
            if this_year == current_date.year:
                # 如果是当前年份，计算到昨天
                yesterday = current_date - datetime.timedelta(days=1)
                end_month = yesterday.month
            else:
                # 如果不是当前年份，使用查询结束日期的月份
                end_month = query_end_date.month

            this_year_ranges = self._generate_monthly_ranges(this_year, 1, end_month)

            # 获取数据
            logger.info(f"开始获取数据 - 去年范围: {len(last_year_ranges)}个月, 今年范围: {len(this_year_ranges)}个月")
            last_year_data = await self._fetch_monthly_data(last_year_ranges, query_params)
            this_year_data = await self._fetch_monthly_data(this_year_ranges, query_params)

            logger.info(f"数据获取结果 - 去年: {len(last_year_data)}条, 今年: {len(this_year_data)}条")

            # 初始化结果字典
            result = {}

            # 如果数据获取失败，生成错误图片
            if not last_year_data:
                logger.warning("去年数据获取失败，生成错误图片")
                error_path = self._generate_error_image("new_member_add_last_year", "去年数据获取失败")
                if error_path:
                    result["new_member_add_last_year"] = error_path

            if not this_year_data:
                logger.warning("今年数据获取失败，生成错误图片")
                error_path = self._generate_error_image("new_member_add_this_year", "今年数据获取失败")
                if error_path:
                    result["new_member_add_this_year"] = error_path

            logger.info(f"最终数据 - 去年: {len(last_year_data)}条, 今年: {len(this_year_data)}条")

            # 只要有数据就生成图片（包括全零数据，因为0也是有意义的数据）
            if last_year_data:
                last_year_path = await self._generate_chart(
                    last_year_data,
                    f"{last_year}年会员新增及取关分析",
                    "new_member_add_last_year"
                )
                if last_year_path:
                    result["new_member_add_last_year"] = last_year_path

            if this_year_data:
                this_year_path = await self._generate_chart(
                    this_year_data,
                    f"{this_year}年会员新增及取关分析",
                    "new_member_add_this_year"
                )
                if this_year_path:
                    result["new_member_add_this_year"] = this_year_path

            # 生成AI分析
            try:
                from .PictureAi import PictureAiAnalyzer
                ai_analyzer = PictureAiAnalyzer()

                logger.info("开始生成新增会员AI分析...")

                # 无论数据是否完整，都尝试生成AI分析
                if last_year_data and this_year_data:
                    # 数据完整，生成完整分析
                    logger.info("数据完整，生成完整AI分析")
                    ai_analysis = await ai_analyzer.generate_all_new_member_analysis(this_year_data, last_year_data)
                    result.update(ai_analysis)
                elif this_year_data:
                    # 只有今年数据，生成今年分析和默认去年分析
                    logger.info("只有今年数据，生成部分AI分析")
                    this_year_analysis = await ai_analyzer.analyze_new_member_this_year_data(this_year_data, [])
                    result.update({
                        "new_member_add_last_year_analysis_report": "1、去年数据缺失，无法进行详细分析。\n2、建议完善数据收集机制，确保历史数据完整性。\n3、可通过其他渠道补充去年同期数据作为对比基准。\n4、重点关注今年数据趋势，制定针对性运营策略。",
                        "new_member_add_this_year_analysis_report": this_year_analysis
                    })
                elif last_year_data:
                    # 只有去年数据，生成去年分析和默认今年分析
                    logger.info("只有去年数据，生成部分AI分析")
                    last_year_analysis = await ai_analyzer.analyze_new_member_last_year_data(last_year_data)
                    result.update({
                        "new_member_add_last_year_analysis_report": last_year_analysis,
                        "new_member_add_this_year_analysis_report": "1、今年数据缺失，无法进行当期分析。\n2、建议立即启动数据收集，确保实时监控会员增长情况。\n3、基于去年数据制定今年增长目标和策略。\n4、加强数据统计和分析能力建设。"
                    })
                else:
                    # 数据都缺失，生成默认分析
                    logger.warning("数据完全缺失，生成默认AI分析")
                    result.update({
                        "new_member_add_last_year_analysis_report": "1、历史数据缺失，无法进行趋势分析。\n2、建议立即建立完善的数据收集和统计体系。\n3、制定数据恢复计划，尽可能补充历史数据。\n4、建立数据质量监控机制，确保未来数据完整性。",
                        "new_member_add_this_year_analysis_report": "1、当前数据缺失，无法评估会员增长情况。\n2、建议紧急启动数据收集工作，确保业务监控正常。\n3、制定应急数据获取方案，通过多渠道收集会员数据。\n4、建立数据备份和恢复机制，防止数据丢失。"
                    })

                logger.info("新增会员AI分析生成完成")

            except Exception as ai_error:
                logger.error(f"生成AI分析失败: {ai_error}")
                import traceback
                traceback.print_exc()
                result.update({
                    "new_member_add_last_year_analysis_report": "1、AI分析系统暂时不可用，请稍后重试。\n2、建议检查AI服务连接状态和配置。\n3、可暂时使用人工分析替代AI分析功能。\n4、联系技术支持解决AI分析问题。",
                    "new_member_add_this_year_analysis_report": "1、AI分析服务异常，无法生成智能分析报告。\n2、建议检查系统日志，排查AI服务故障原因。\n3、可使用历史分析模板进行手动分析。\n4、尽快恢复AI分析功能，确保报告质量。"
                })

            logger.info(f"新增会员图表和分析生成完成，共生成 {len(result)} 个结果")
            return result

        except Exception as e:
            logger.error(f"生成新增会员图表失败: {e}")
            return {}

    def _generate_error_image(self, image_type: str, error_message: str) -> str:
        """
        生成错误提示图片

        Args:
            image_type: 图片类型
            error_message: 错误信息

        Returns:
            str: 图片保存路径
        """
        try:
            # 创建错误图片
            fig, ax = plt.subplots(figsize=(12, 8))
            ax.text(0.5, 0.5, f'数据生成失败\n{error_message}',
                   horizontalalignment='center', verticalalignment='center',
                   fontsize=20, color='red', weight='bold',
                   transform=ax.transAxes)
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')

            # 保存图片
            file_path = self.image_manager.get_image_path(image_type)
            plt.savefig(file_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close(fig)

            logger.info(f"错误图片生成完成: {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"生成错误图片失败: {e}")
            return ""

    def _generate_monthly_ranges(self, year: int, start_month: int, end_month: int) -> List[Tuple[str, str, str]]:
        """
        生成月度时间范围

        Args:
            year: 年份
            start_month: 开始月份
            end_month: 结束月份

        Returns:
            List: [(月份标签, 开始日期, 结束日期), ...]
        """
        ranges = []

        for month in range(start_month, end_month + 1):
            # 计算月份的第一天和最后一天
            first_day = datetime.date(year, month, 1)

            # 计算下个月的第一天，然后减去一天得到当月最后一天
            if month == 12:
                next_month_first = datetime.date(year + 1, 1, 1)
            else:
                next_month_first = datetime.date(year, month + 1, 1)

            last_day = next_month_first - datetime.timedelta(days=1)

            # 如果是当前月份且是今年，需要截止到昨天
            if year == datetime.datetime.now().year and month == datetime.datetime.now().month:
                yesterday = datetime.datetime.now().date() - datetime.timedelta(days=1)
                if yesterday < last_day:
                    last_day = yesterday

            month_label = f"{year}年{month}月"
            start_date = first_day.strftime("%Y-%m-%d")
            end_date = last_day.strftime("%Y-%m-%d")

            ranges.append((month_label, start_date, end_date))

        return ranges

    async def _fetch_monthly_data(self, time_ranges: List[Tuple[str, str, str]], query_params) -> List[Dict[str, Any]]:
        """
        获取月度数据

        Args:
            time_ranges: 时间范围列表
            query_params: 查询参数

        Returns:
            List: 月度数据列表
        """
        try:
            from api.query.MemberBaseSql import MemberBaseSqlQueries, MemberBaseSqlCalculator
            from core.database import db

            monthly_data = []
            bid = self._extract_param(query_params, 'bid')
            sid = self._extract_param(query_params, 'sid', None)

            # 检查是否存在企微配置
            wechat_enabled = False
            try:
                wechat_mapping = await get_merchant_id_by_boss_id(str(bid))
                wechat_enabled = wechat_mapping is not None
                if wechat_enabled:
                    logger.info(f"检测到企微配置，merchant_id: {wechat_mapping['id']}")
                else:
                    logger.info("未检测到企微配置，不显示企微数据")
            except Exception as wechat_check_error:
                logger.warning(f"检查企微配置失败: {wechat_check_error}")
                wechat_enabled = False

            for month_label, start_date, end_date in time_ranges:
                logger.info(f"获取 {month_label} 数据: {start_date} 到 {end_date}")

                # 转换日期格式为YYYYMMDD
                start_date_db = start_date.replace('-', '')
                end_date_db = end_date.replace('-', '')

                try:
                    # 1. 查询新增会员数量
                    new_user_sql = f"SELECT {MemberBaseSqlQueries.get_dwoutput_new_user_total_sql(start_date_db, end_date_db, bid, sid)}"
                    new_user_result = await db.execute_dwoutput_one(new_user_sql)
                    new_members = new_user_result.get('new_user_total', 0) if new_user_result else 0
                    new_members = int(new_members) if new_members else 0

                    # 2. 查询新增取消注册会员数量
                    new_cancel_user_sql = f"SELECT {MemberBaseSqlQueries.get_dwoutput_new_cancel_user_sql(start_date_db, end_date_db, bid, sid)}"
                    new_cancel_user_result = await db.execute_dwoutput_one(new_cancel_user_sql)
                    new_unfollow_members = new_cancel_user_result.get('new_cancel_user', 0) if new_cancel_user_result else 0
                    new_unfollow_members = float(new_unfollow_members) if new_unfollow_members else 0.0

                    # 3. 计算取关率（MemberBaseSqlCalculator返回小数形式，需要乘以100转换为百分比）
                    unfollow_rate_decimal = MemberBaseSqlCalculator.calculate_cancel_user_rate(
                        new_unfollow_members, float(new_members)
                    )
                    unfollow_rate = unfollow_rate_decimal * 100  # 转换为百分比

                    # 4. 查询企微加好友数（如果启用）
                    wechat_new_friends = 0
                    if wechat_enabled:
                        try:
                            wechat_count = await get_wechat_new_customers_count(str(bid), start_date_db, end_date_db)
                            wechat_new_friends = wechat_count if wechat_count is not None else 0
                            logger.info(f"{month_label} 企微加好友数: {wechat_new_friends}")
                        except Exception as wechat_error:
                            logger.warning(f"获取 {month_label} 企微数据失败: {wechat_error}")
                            wechat_new_friends = 0

                    logger.info(f"{month_label} 数据: 新增{new_members}, 取关{int(new_unfollow_members)}, 取关率{unfollow_rate:.2f}%, 企微{wechat_new_friends}")

                    monthly_data.append({
                        'month': month_label,
                        'new_members': new_members,
                        'new_unfollow_members': int(new_unfollow_members),
                        'unfollow_rate': unfollow_rate,  # 现在是百分比形式
                        'wechat_new_friends': wechat_new_friends,
                        'wechat_enabled': wechat_enabled
                    })

                except Exception as month_error:
                    logger.error(f"获取 {month_label} 数据失败: {month_error}")
                    # 添加失败的月份数据，避免图表中断
                    monthly_data.append({
                        'month': month_label,
                        'new_members': 0,
                        'new_unfollow_members': 0,
                        'unfollow_rate': 0.0,
                        'wechat_new_friends': 0,
                        'wechat_enabled': wechat_enabled
                    })

            logger.info(f"数据获取结果 - 去年: {len([d for d in monthly_data if '2024' in d['month']])}条, 今年: {len([d for d in monthly_data if '2025' in d['month']])}条")
            return monthly_data

        except Exception as e:
            logger.error(f"获取月度数据失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    @collect_chart_data("new_members", include_comparison=True)
    async def _generate_chart(self, data: List[Dict[str, Any]], title: str, image_type: str) -> str:
        """
        生成图表

        Args:
            data: 月度数据列表
            title: 图表标题
            image_type: 图片类型

        Returns:
            str: 图片保存路径
        """
        try:
            if not data:
                logger.warning(f"数据为空，无法生成图表: {image_type}")
                return ""

            # 创建图表
            fig, ax1 = plt.subplots(figsize=(14, 8))

            # 提取数据
            months = [item['month'] for item in data]
            new_members = [item['new_members'] for item in data]
            new_unfollow_members = [item['new_unfollow_members'] for item in data]
            unfollow_rates = [item['unfollow_rate'] for item in data]

            # 检查是否有企微数据
            wechat_enabled = data[0].get('wechat_enabled', False) if data else False
            wechat_new_friends = [item.get('wechat_new_friends', 0) for item in data] if wechat_enabled else []

            # 设置X轴位置
            x_pos = range(len(months))

            # 根据是否有企微数据调整柱状图布局
            if wechat_enabled:
                # 有企微数据：三个柱状图
                bar_width = 0.25
                bars1 = ax1.bar([x - bar_width for x in x_pos], new_members,
                               bar_width, label='新增会员数量', color='#4472C4', alpha=0.8)
                bars2 = ax1.bar(x_pos, new_unfollow_members,
                               bar_width, label='取关会员数量', color='#E74C3C', alpha=0.8)
                bars3 = ax1.bar([x + bar_width for x in x_pos], wechat_new_friends,
                               bar_width, label='企微加好友数', color='#FFD700', alpha=0.8)
            else:
                # 无企微数据：两个柱状图
                bar_width = 0.35
                bars1 = ax1.bar([x - bar_width/2 for x in x_pos], new_members,
                               bar_width, label='新增会员数量', color='#4472C4', alpha=0.8)
                bars2 = ax1.bar([x + bar_width/2 for x in x_pos], new_unfollow_members,
                               bar_width, label='取关会员数量', color='#E74C3C', alpha=0.8)

            # 设置左侧Y轴
            ax1.set_xlabel('月份', fontsize=12)
            ax1.set_ylabel('会员数量', fontsize=12)

            ax1.set_xticks(x_pos)
            # 去掉年份前缀，只保留月份，水平显示
            month_labels = [month.split('-')[-1] + '月' if '-' in month else month for month in months]
            ax1.set_xticklabels(month_labels, rotation=0, ha='center')
            ax1.grid(True, alpha=0.3)

            # 创建右侧Y轴用于取关占比
            ax2 = ax1.twinx()
            ax2.plot(x_pos, unfollow_rates, color='#F39C12', marker='o',
                    linewidth=3, markersize=8, label='取关占比')
            ax2.set_ylabel('取关占比 (%)', fontsize=12)
            # 设置右Y轴范围，处理全零数据的情况
            max_rate = max(unfollow_rates) if unfollow_rates and max(unfollow_rates) > 0 else 10
            ax2.set_ylim(0, max_rate * 1.2)

            # 在柱状图上添加数值标签（智能避免重叠）
            all_values = new_members + new_unfollow_members
            if wechat_enabled:
                all_values.extend(wechat_new_friends)
            max_member_count = max(all_values) if all_values and max(all_values) > 0 else 10

            # 收集所有柱状图的位置和数值
            bar_data = []
            for bar in bars1:
                height = bar.get_height()
                if height > 0:
                    bar_data.append((bar.get_x() + bar.get_width()/2., height))

            for bar in bars2:
                height = bar.get_height()
                if height > 0:
                    bar_data.append((bar.get_x() + bar.get_width()/2., height))

            # 如果有企微数据，添加企微柱状图数据
            if wechat_enabled:
                for bar in bars3:
                    height = bar.get_height()
                    if height > 0:
                        bar_data.append((bar.get_x() + bar.get_width()/2., height))

            # 使用智能标签添加方法
            if bar_data:
                x_positions = [x for x, y in bar_data]
                values = [y for x, y in bar_data]
                add_smart_bar_labels(ax1, values, max_member_count, x_positions, fontsize=10, decimal_places=0)

            # 在折线图上添加百分比标签（智能避免重叠）
            add_smart_line_labels(ax2, unfollow_rates, max_rate, fontsize=10, color='#F39C12', is_percentage=True, decimal_places=2, reset_positions=False)

            # 合并图例 - 放在图表外部顶部
            lines1, labels1 = ax1.get_legend_handles_labels()
            lines2, labels2 = ax2.get_legend_handles_labels()
            ax1.legend(lines1 + lines2, labels1 + labels2,
                      bbox_to_anchor=(0.5, 1.02), loc='lower center',
                      ncol=len(labels1 + labels2), fontsize=11)

            # 添加数据表格
            self._add_data_table(data, ax1)

            # 调整布局
            plt.tight_layout()

            # 保存图片
            save_path = self.image_manager.get_image_path(image_type)
            logger.info(f"准备保存图片到: {save_path}")

            # 确保保存目录存在
            save_dir = Path(save_path).parent
            save_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"保存目录: {save_dir}, 存在: {save_dir.exists()}")

            # 保存图片
            try:
                plt.savefig(save_path, dpi=300, bbox_inches='tight',
                           facecolor='white', edgecolor='none')
                logger.info(f"matplotlib保存完成")

                # 验证文件是否真的被创建
                if Path(save_path).exists():
                    file_size = Path(save_path).stat().st_size
                    logger.info(f"图表生成成功: {save_path}, 文件大小: {file_size} 字节")
                else:
                    logger.error(f"图片保存失败: 文件未创建 {save_path}")
                    return ""

            except Exception as save_error:
                logger.error(f"matplotlib保存失败: {save_error}")
                return ""
            finally:
                plt.close()

            return save_path

        except Exception as e:
            logger.error(f"生成图表失败 {image_type}: {e}")
            return ""

    def _add_data_table(self, data: List[Dict[str, Any]], ax):
        """
        在图表下方添加数据表格（横向布局，时间为横轴）

        Args:
            data: 数据列表
            ax: 坐标轴对象
        """
        try:
            # 检查是否有企微数据
            wechat_enabled = data[0].get('wechat_enabled', False) if data else False

            # 准备横向表格数据
            months = [item['month'] for item in data]
            new_members = [f"{item['new_members']:,}" for item in data]
            unfollow_members = [f"{item['new_unfollow_members']:,}" for item in data]
            unfollow_rates = [f"{item['unfollow_rate']:.2f}" for item in data]

            # 构建横向表格数据
            table_data = [
                months,  # 第一行：月份
                new_members,  # 第二行：新增会员数量
                unfollow_members,  # 第三行：取关会员数量
                unfollow_rates  # 第四行：取关占比
            ]

            # 行标题（左侧标签）
            row_labels = ['月份', '新增会员(人)', '取关会员(人)', '取关占比(%)']

            # 如果有企微数据，添加企微行
            if wechat_enabled:
                wechat_friends = [f"{item.get('wechat_new_friends', 0):,}" for item in data]
                table_data.append(wechat_friends)  # 第五行：企微加好友数
                row_labels.append('企微加好友(人)')

            # 创建表格
            table_height = 0.55 if wechat_enabled else 0.45  # 有企微数据时增加表格高度
            table = ax.table(
                cellText=table_data,
                rowLabels=row_labels,
                cellLoc='center',
                loc='bottom',
                bbox=[0, -0.75 if wechat_enabled else -0.65, 1, table_height]  # 调整位置和高度
            )

            # 设置表格样式
            table.auto_set_font_size(False)
            table.set_fontsize(9)  # 增大字体大小提高可读性
            table.scale(1, 1.3)

            # 设置行标题样式（左侧列）
            for i in range(len(row_labels)):
                table[(i, -1)].set_facecolor('#4472C4')
                table[(i, -1)].set_text_props(weight='bold', color='white')

            # 设置数据单元格样式
            for i in range(len(row_labels)):
                for j in range(len(months)):
                    if i == 0:  # 月份行使用浅蓝色
                        table[(i, j)].set_facecolor('#E7F3FF')
                        table[(i, j)].set_text_props(weight='bold')
                    elif i % 2 == 1:  # 奇数行使用浅灰色
                        table[(i, j)].set_facecolor('#F8F9FA')
                    else:  # 偶数行使用白色
                        table[(i, j)].set_facecolor('white')

            logger.info(f"横向数据表格创建完成，包含 {len(months)} 个月的数据")

        except Exception as e:
            logger.error(f"添加数据表格失败: {e}")




# 工厂函数
def create_new_members_pic_generator(bid: str, image_manager) -> NewMembersPicGenerator:
    """
    创建新增会员图片生成器

    Args:
        bid: 品牌ID
        image_manager: 图片管理器实例

    Returns:
        NewMembersPicGenerator: 图片生成器实例
    """
    return NewMembersPicGenerator(bid, image_manager)