"""
PPT任务执行器
包装PPT生成流程，提供任务状态管理和进度跟踪
"""

import logging
import asyncio
import traceback
from typing import Dict, Any, Optional
from datetime import datetime
import os
import shutil
from pathlib import Path

from core.models import QueryParams
from ..PPTReport import ppt_report_service
from .models import TaskModel, TaskStatus
from .task_manager import TaskManager

logger = logging.getLogger(__name__)


class PPTTaskExecutor:
    """PPT任务执行器"""
    
    def __init__(self):
        self.task_manager = TaskManager()
        self.ppt_service = ppt_report_service
        
    async def execute_task(self, task: TaskModel) -> Dict[str, Any]:
        """
        执行PPT生成任务
        
        Args:
            task: 任务模型
            
        Returns:
            执行结果
        """
        try:
            logger.info(f"开始执行PPT任务: ID={task.id}, UUID={task.task_uuid}")
            
            # 更新任务状态为处理中
            await self._update_task_progress(task, 5, "初始化任务")
            
            # 1. 解析任务参数
            query_params = self._parse_task_params(task)
            if not query_params:
                raise ValueError("无效的任务参数")
            
            # 2. 数据获取阶段（30%）
            await self._update_task_progress(task, 10, "开始获取数据")
            
            try:
                # 这里模拟调用数据获取，实际会在PPT生成过程中自动执行
                await self._update_task_progress(task, 20, "获取会员基础数据")
                await asyncio.sleep(0.5)  # 模拟处理时间
                
                await self._update_task_progress(task, 25, "获取消费和充值数据")
                await asyncio.sleep(0.5)
                
                await self._update_task_progress(task, 30, "数据获取完成")
                
            except Exception as e:
                logger.error(f"数据获取失败: {e}")
                await self._handle_task_error(task, f"数据获取失败: {str(e)}", {"phase": "data_acquisition"})
                raise
            
            # 3. 图片生成阶段（50%）
            await self._update_task_progress(task, 35, "开始生成统计图表")
            
            try:
                # 图片生成在PPT生成过程中自动执行
                await self._update_task_progress(task, 40, "生成会员分析图表")
                await asyncio.sleep(0.5)
                
                await self._update_task_progress(task, 45, "生成消费趋势图表")
                await asyncio.sleep(0.5)
                
                await self._update_task_progress(task, 50, "图表生成完成")
                
            except Exception as e:
                logger.error(f"图表生成失败: {e}")
                await self._handle_task_error(task, f"图表生成失败: {str(e)}", {"phase": "picture_generation"})
                raise
            
            # 4. PPT生成阶段（80%）
            await self._update_task_progress(task, 55, "开始生成PPT文件")
            
            try:
                # 调用实际的PPT生成服务，传递task_uuid用于创建独立文件夹
                result = await self.ppt_service.generate_member_report(
                    query_params=query_params,
                    output_filename=task.task_name,
                    task_uuid=task.task_uuid  # 传递任务UUID
                )
                
                if not result["success"]:
                    raise Exception(result.get("message", "PPT生成失败"))
                
                await self._update_task_progress(task, 70, "PPT文件生成成功")
                
                # 保存生成结果到任务
                await self._save_task_result(task, result["data"])
                
                await self._update_task_progress(task, 80, "文件处理完成")
                
            except Exception as e:
                logger.error(f"PPT生成失败: {e}")
                await self._handle_task_error(task, f"PPT生成失败: {str(e)}", {"phase": "ppt_generation"})
                raise
            
            # 5. 文件上传阶段（95%）
            await self._update_task_progress(task, 85, "开始上传文件")
            
            try:
                # 文件上传已在PPT生成服务中完成
                file_info = result.get("data", {})
                
                # 更新任务的文件信息
                task.ppt_oss_url = file_info.get("file_url") or file_info.get("download_url")
                task.excel_oss_url = file_info.get("dataDownloadUrl")
                task.file_PPT_size = file_info.get("file_size", 0)
                task.result_file_url = task.ppt_oss_url
                
                await self._update_task_progress(task, 95, "文件上传完成")
                
            except Exception as e:
                logger.warning(f"文件上传信息更新失败（不影响任务）: {e}")
            
            # 6. 任务完成
            await self._complete_task(task)
            
            logger.info(f"PPT任务执行成功: ID={task.id}")
            
            return {
                "success": True,
                "task_id": task.id,
                "task_uuid": task.task_uuid,
                "result": result.get("data"),
                "message": "任务执行成功"
            }
            
        except Exception as e:
            logger.error(f"PPT任务执行失败: ID={task.id}, 错误: {e}", exc_info=True)
            
            # 更新任务失败状态
            await self._handle_task_error(
                task,
                f"任务执行失败: {str(e)}",
                {
                    "exception": str(type(e).__name__),
                    "traceback": traceback.format_exc()
                }
            )
            
            return {
                "success": False,
                "task_id": task.id,
                "task_uuid": task.task_uuid,
                "error": str(e),
                "message": "任务执行失败"
            }
    
    def _parse_task_params(self, task: TaskModel) -> Optional[QueryParams]:
        """
        解析任务参数为QueryParams
        
        Args:
            task: 任务模型
            
        Returns:
            查询参数对象
        """
        try:
            # 确定查询类型
            # 如果有开始和结束日期，使用 custom 类型
            if task.task_start_ftime and task.task_end_ftime:
                query_type = "custom"
            else:
                # 否则使用任务中指定的类型，默认为 month
                query_type = task.task_type if task.task_type in ["week", "month", "quarter", "halfyear", "custom"] else "month"
            
            # 从任务中提取参数
            params = QueryParams(
                query_type=query_type,
                bid=task.bid,
                sid=task.sid,
                start_date=task.task_start_ftime,
                end_date=task.task_end_ftime
            )
            
            logger.info(f"解析任务参数: query_type={query_type}, bid={task.bid}, start={task.task_start_ftime}, end={task.task_end_ftime}")
            
            return params
            
        except Exception as e:
            logger.error(f"解析任务参数失败: {e}")
            return None
    
    async def _update_task_progress(
        self,
        task: TaskModel,
        progress: int,
        current_step: str
    ):
        """更新任务进度"""
        try:
            task.progress = progress
            task.current_step = current_step
            
            # 如果是第一次更新进度，设置开始时间
            if progress > 0 and not task.started_at:
                task.started_at = datetime.now()
                task.status = TaskStatus.PROCESSING.value
            
            await self.task_manager.update_task(task)
            
            logger.info(f"任务进度更新: ID={task.id}, 进度={progress}%, 步骤={current_step}")
            
        except Exception as e:
            logger.error(f"更新任务进度失败: {e}")
    
    async def _save_task_result(self, task: TaskModel, result_data: Dict[str, Any]):
        """保存任务执行结果"""
        try:
            # 更新任务的结果字段
            task.file_PPT_size = result_data.get("file_size", 0)
            task.ppt_oss_url = result_data.get("file_url") or result_data.get("download_url")
            task.excel_oss_url = result_data.get("dataDownloadUrl")
            task.result_file_url = task.ppt_oss_url
            
            # 保存文件名
            if result_data.get("file_name"):
                task.task_name = result_data["file_name"]
            
            await self.task_manager.update_task(task)
            
            logger.info(f"任务结果已保存: ID={task.id}")
            
        except Exception as e:
            logger.error(f"保存任务结果失败: {e}")
    
    async def _complete_task(self, task: TaskModel):
        """完成任务"""
        try:
            task.status = TaskStatus.COMPLETED.value
            task.progress = 100
            task.current_step = "任务完成"
            task.completed_at = datetime.now()
            
            # 计算实际执行时长
            if task.started_at:
                duration = (task.completed_at - task.started_at).total_seconds() / 60
                task.actual_duration = int(duration)
            
            await self.task_manager.update_task(task)
            
            logger.info(f"任务已完成: ID={task.id}, 耗时={task.actual_duration}分钟")
            
        except Exception as e:
            logger.error(f"标记任务完成失败: {e}")
    
    async def _handle_task_error(
        self,
        task: TaskModel,
        error_message: str,
        error_details: Optional[Dict] = None
    ):
        """处理任务错误"""
        try:
            # 添加错误信息
            task.add_error(error_message, error_details)
            
            # 检查是否可以重试
            if task.can_retry():
                task.status = TaskStatus.PENDING.value
                task.current_step = f"等待重试 (第{task.retry_count}次失败)"
                logger.info(f"任务失败但可重试: ID={task.id}, 重试次数={task.retry_count}")
            else:
                task.status = TaskStatus.FAILED.value
                task.current_step = "任务失败"
                task.completed_at = datetime.now()
                logger.error(f"任务失败且无法重试: ID={task.id}")
            
            await self.task_manager.update_task(task)
            
        except Exception as e:
            logger.error(f"处理任务错误失败: {e}")
    
    async def cleanup_task_resources(self, task: TaskModel):
        """
        清理任务相关的临时资源
        
        Args:
            task: 任务模型
        """
        try:
            # 构建临时目录路径
            if task.bid:
                date_str = datetime.now().strftime("%Y%m%d")
                temp_dir = Path(f"uploads/temp/{task.bid}_{date_str}")
                
                if temp_dir.exists():
                    logger.info(f"清理任务临时文件: {temp_dir}")
                    shutil.rmtree(temp_dir, ignore_errors=True)
            
        except Exception as e:
            logger.error(f"清理任务资源失败: {e}")


# 创建全局执行器实例
ppt_task_executor = PPTTaskExecutor()