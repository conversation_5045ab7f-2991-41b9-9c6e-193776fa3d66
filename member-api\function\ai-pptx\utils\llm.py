import json
import requests


class LLM:
    def __init__(self, api_key, base_url=None, model_name=None):
        self.api_key = api_key
        self.base_url = base_url.rstrip('/') if base_url else None
        self.default_model_name = model_name

    def _make_request(self, messages: list, model_name: str = None, temperature: float = 0.1, stream: bool = False):
        """发送HTTP请求到LLM API"""
        url = f"{self.base_url}/chat/completions"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        data = {
            "model": model_name if model_name else self.default_model_name,
            "messages": messages,
            "temperature": temperature,
            "stream": stream
        }

        return requests.post(url, headers=headers, json=data, stream=stream)

    def chat(self, messages: list, model_name: str, temperature: float):
        """对话返回迭代器"""
        response = self._make_request(messages, model_name, temperature, stream=True)

        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    line = line[6:]  # 移除 'data: ' 前缀
                    if line.strip() == '[DONE]':
                        break
                    try:
                        chunk_data = json.loads(line)
                        if 'choices' in chunk_data and len(chunk_data['choices']) > 0:
                            delta = chunk_data['choices'][0].get('delta', {})
                            content = delta.get('content', '')
                            if content:
                                yield content
                    except json.JSONDecodeError:
                        continue

    def chat_in_all(self, messages: list, model_name: str = None, temperature: float = 0.1):
        """对话返回全部内容"""
        response = self._make_request(messages, model_name, temperature, stream=False)

        if response.status_code == 200:
            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                return result['choices'][0]['message']['content']

        # 如果非流式失败，尝试流式
        collected = []
        for chunk in self.chat(messages, model_name, temperature):
            collected.append(chunk)
        return ''.join(collected)

    def chat_once(self, prompt: str, system_prompt: str = None, model_name: str = None, temperature: float = 0.1):
        """一次性对话"""
        messages = [
            {"role": "system", "content": system_prompt or "你是个全能助手"},
            {"role": "user", "content": prompt}
        ]
        return self.chat_in_all(messages, model_name, temperature)
