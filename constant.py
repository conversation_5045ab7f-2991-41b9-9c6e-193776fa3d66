# -*- coding: utf-8 -*-
"""
该文件用于记录所有统计的常量，包括多个模块，基于fieldREAADME.md文件定义的数据字段和计算逻辑

"""
"""
参数说明：
table: 数据库表名
field: 字段名
logic: 计算逻辑（用于前端展示说明）
note: 计算备注
key: 驼峰命名（用于程序调用）
"""
# 会员基础模块常量
MEMBER_BASE_MODULE = {
    "会员总数量": {
        "table": "dprpt_welife_user_log",
        "field": "all_user + all_unregistered_user",
        "logic": "累计会员量 + 取消关注会员量",
        "note": "取范围内最后一天数值加和",
        "key": "totalMembers"
    },
    "会员净存量": {
        "table": "dprpt_welife_user_log", 
        "field": "all_user",
        "logic": "累计会员量",
        "note": "取范围内最后一天数值加和后进行加减法",
        "key": "netMembers"
    },
    "新增会员数量": {
        "table": "dprpt_welife_user_log",
        "field": "new_user", 
        "logic": "新增的注册用户数",
        "note": "加和",
        "key": "newMembers"
    },
    "取关会员数量": {
        "table": "dprpt_welife_user_log",
        "field": "new_unregistered_user",
        "logic": "取消关注会员量", 
        "note": "加和",
        "key": "unfollowMembers"
    },
    "取关占比": {
        "table": "dprpt_welife_user_log",
        "field": "new_unregistered_user / new_user",
        "logic": "取消关注会员量 / 新增的注册用户数",
        "note": "加和后进行除法",
        "key": "unfollowRate"
    },
    "新增消费的会员数": {
        "table": "dprpt_welife_user_log",
        "field": "new_user_consomer",
        "logic": "新增消费会员量",
        "note": "加和",
        "key": "newConsumeMembers"
    },
    "新增储值的会员数": {
        "table": "dprpt_welife_user_log", 
        "field": "new_user_charger",
        "logic": "新增储值会员量",
        "note": "加和",
        "key": "newChargeMembers"
    },
    "累计消费的会员数": {
        "table": "dprpt_welife_user_log",
        "field": "all_user_consomer", 
        "logic": "累计新增消费会员量",
        "note": "取范围内最后一天数值进行加和",
        "key": "totalConsumeMembers"
    },
    "累计储值的会员数": {
        "table": "dprpt_welife_user_log",
        "field": "all_user_charger",
        "logic": "累计新增储值会员量", 
        "note": "取范围内最后一天数值进行加和",
        "key": "totalChargeMembers"
    },
    "新增完善会员量": {
        "table": "dprpt_welife_users_stat",
        "field": "uUserInfoNum",
        "logic": "新增完善四项基础资料会员量",
        "note": "加和",
        "key": "newCompleteMembers"
    },
    "新增会员完善率": {
        "table": "dprpt_welife_users_stat",
        "field": "uUserInfoNum / uIncreNum",
        "logic": "新增完善四项基础资料会员量/ 新增会员量",
        "note": "加和后进行除法",
        "key": "newCompleteRate"
    },
    "完善手机号会员数量": {
        "table": "dprpt_welife_users_stat",
        "field": "uCardPhoneNum",
        "logic": "取范围内最后一天数值加和",
        "note": "加和",
        "key": "completePhoneMembers"
    },
    "完善会员总数": {
        "table": "dprpt_welife_users_stat",
        "field": "uCardInfoNum",
        "logic": "期末四项基础资料会员存量",
        "note": "取范围内最后一天数值进行加和",
        "key": "totalCompleteMembers"
    },
    "会员1次消费总人数": {
        "table": "dprpt_welife_users_stat",
        "field": "uConsume1Num",
        "logic": "期末历史消费1次会员数量",
        "note": "取范围内最后一天数值进行加和",
        "key": "consumeOnceMembers"
    },
    "会员2次消费以上总人数": {
        "table": "dprpt_welife_users_stat",
        "field": "uConsume2Num",
        "logic": "期末历史消费2次（含）以上会员数量",
        "note": "取范围内最后一天数值进行加和",
        "key": "consumeMultipleMembers"
    }
}

# 会员消费模块常量
MEMBER_CONSUME_MODULE = {
    "消费总金额": {
        "table": "dprpt_welife_consume_log",
        "field": "consume_amount + overdue_amount - cancel_amount",
        "logic": "正常消费 + 逾期消费 - 取消消费",
        "note": "分别进行加和后进行加减法",
        "key": "totalAmount"
    },
    "会员消费人数": {
        "table": "dprpt_welife_consume_log",
        "field": "consume_amount_uv + overdue_uv",
        "logic": "正常消费人数 + 逾期消费人数（取消的用户不减）",
        "note": "分别进行加和后进行加减法",
        "key": "consumeUsers"
    },
    "会员总消费笔数": {
        "table": "dprpt_welife_consume_log",
        "field": "consume_amount_pv + overdue_pv - cancel_amount_pv",
        "logic": "正常消费笔数 + 逾期消费笔数 - 取消消费笔数",
        "note": "分别进行加和后进行加减法",
        "key": "totalConsumeCount"
    },
    "会员实收金额": {
        "table": "dprpt_welife_consume_log",
        "field": "consume_cash - cancel_cash",
        "logic": "现金支付 - 取消现金支付",
        "note": "分别进行加和后进行加减法",
        "key": "actualAmount"
    },
    "现金支付会员数": {
        "table": "dprpt_welife_consume_log",
        "field": "consume_cash_uv",
        "logic": "使用现金支付的用户数（取消不减）",
        "note": "加和",
        "key": "cashUsers"
    },
    "会员现金消费笔数": {
        "table": "dprpt_welife_consume_log",
        "field": "consume_cash_pv - cancel_cash_pv",
        "logic": "现金支付笔数 - 取消现金支付笔数",
        "note": "分别进行加和后进行加减法",
        "key": "cashConsumeCount"
    },
    "会员储值消费金额": {
        "table": "dprpt_welife_consume_log",
        "field": "consume_prepay - cancel_prepay + overdue_amount",
        "logic": "储值预存 - 取消储值预存 + 逾期金额",
        "note": "分别进行加和后进行加减法",
        "key": "prepayAmount"
    },
    "会员使用储值的实收金额": {
        "table": "dprpt_welife_trade_consume_detail",
        "field": "tclprinciple",
        "logic": "加和计算",
        "note": "加和",
        "key": "prepayActualAmount"
    },
    "储值支付会员数": {
        "table": "dprpt_welife_consume_log",
        "field": "consume_prepay_uv + overdue_uv",
        "logic": "正常储值人数 + 逾期储值人数",
        "note": "分别进行加和后进行加减法",
        "key": "prepayUsers"
    },
    "会员储值消费笔数": {
        "table": "dprpt_welife_consume_log",
        "field": "consume_prepay_pv - cancel_prepay_pv + overdue_pv",
        "logic": "储值消费笔数 - 取消储值消费笔数 + 逾期消费笔数",
        "note": "分别进行加和后进行加减法",
        "key": "prepayConsumeCount"
    },
    "会员总实收金额": {
        "table": "dprpt_welife_consume_log",
        "field": "会员使用储值的实收金额+会员实收现金金额",
        "logic": "会员使用储值的实收金额 + 会员实收现金金额",
        "note": "分别进行加和后进行加减法",
        "key": "totalActualAmount"
    },
    "会员人均贡献": {
        "table": "dprpt_welife_consume_log",
        "field": "会员总实收金额/会员消费人数",
        "logic": "会员总实收金额/会员消费人数",
        "note": "计算后除",
        "key": "avgContribution"
    },
    "券带动总交易金额": {
        "table": "wedatas_welife_coupon_log",
        "field": "trade_amount - cancel_trade_amount",
        "logic": "交易总金额 - 撤销总金额",
        "note": "分别进行加和后进行加减法",
        "key": "couponTradeAmount"
    }
}

# 会员充值模块常量
MEMBER_CHARGE_MODULE = {
    "期间会员充值笔数": {
        "table": "dprpt_welife_charge_log_bid",
        "field": "charge_amount_pv - cancel_charge_amount_pv",
        "logic": "充值笔数 - 取消充值笔数",
        "note": "分别进行加和后进行加减法",
        "key": "chargeCount"
    },
    "期间充值实收总金额": {
        "table": "dprpt_welife_charge_log_bid",
        "field": "charge_cash - cancel_charge_cash",
        "logic": "实收金额 - 撤销储值实收金额",
        "note": "分别进行加和后进行加减法",
        "key": "chargeAmount"
    },
    "期间消耗储值实收总金额": {
        "table": "dprpt_welife_consume_log",
        "field": "consume_prepay - cancel_prepay + overdue_amount",
        "logic": "储值预存 - 取消储值预存 + 逾期金额",
        "note": "分别进行加和后进行加减法",
        "key": "consumePrepayAmount"
    },
    "存储留存率": {
        "table": "",
        "field": "(期间储值总金额-期间消费使用的储值金额)/期间储值总金额",
        "logic": "(期间储值总金额-期间消费使用的储值金额)/期间储值总金额",
        "note": "计算后除",
        "key": "retentionRate"
    }
}

"""
券模块需要先获取bid或sid在时间范围内的券信息，然后在分别统计每个券的下面字段
"""
# 券交易模块常量
COUPON_TRADE_MODULE = {
    "券名称": {
        "table": "wedatas_welife_coupon_log",
        "field": "couponname",
        "logic": "券名称",
        "note": "直接获取",
        "key": "couponName"
    },
    "券编号id": {
        "table": "wedatas_welife_coupon_log",
        "field": "couponid",
        "logic": "券id",
        "note": "直接获取",
        "key": "couponId"
    },
    "券发放量(张)": {
        "table": "wedatas_welife_coupon_log",
        "field": "coupon_send - cancel_coupon_send",
        "logic": "券发放数量 - 撤销券发放数量",
        "note": "分别进行加和后进行加减法",
        "key": "couponSendCount"
    },
    "券使用量(张)": {
        "table": "wedatas_welife_coupon_log",
        "field": "coupon_used - cancel_coupon_used",
        "logic": "券使用数量 - 撤销使用券数量",
        "note": "分别进行加和后进行加减法",
        "key": "couponUsedCount"
    },
    "券使用率(%)": {
        "table": "wedatas_welife_coupon_log",
        "field": "券使用量/券发放量",
        "logic": "券使用量/券发放量",
        "note": "计算后除",
        "key": "couponUsageRate"
    },
    "券抵扣金额(元)": {
        "table": "wedatas_welife_coupon_log",
        "field": "(coupon_used - cancel_coupon_used) * camount",
        "logic": "(券使用数量 - 撤销使用券数量) * 券抵扣金额",
        "note": "分别进行加和后进行乘法",
        "key": "couponDiscountAmount"
    },
    "带动储值消费(元)": {
        "table": "wedatas_welife_coupon_log",
        "field": "trade_prepay - cancel_trade_prepay",
        "logic": "交易预存 - 撤销预存",
        "note": "分别进行加和后进行加减法",
        "key": "drivePrepayAmount"
    },
    "带动现金消费(元)": {
        "table": "wedatas_welife_coupon_log",
        "field": "trade_cash - cancel_trade_cash",
        "logic": "交易现金 - 撤销现金",
        "note": "分别进行加和后进行加减法",
        "key": "driveCashAmount"
    },
    "带动总交易金额": {
        "table": "wedatas_welife_coupon_log",
        "field": "trade_amount - cancel_trade_amount",
        "logic": "交易总金额 - 撤销总金额",
        "note": "分别进行加和后进行加减法",
        "key": "driveTotalAmount"
    }
}

# 所有模块的统一索引
ALL_MODULES = {
    "会员基础模块": MEMBER_BASE_MODULE,
    "会员消费模块": MEMBER_CONSUME_MODULE,
    "会员充值模块": MEMBER_CHARGE_MODULE,
    "券交易模块": COUPON_TRADE_MODULE
}

# 数据库表名常量
DATABASE_TABLES = {
    "dprpt_welife_user_log": "来源数据库：dwoutput，表名：会员日志表",
    "dprpt_welife_users_stat": "来源数据库：dwoutput，表名：会员统计表", 
    "dprpt_welife_consume_log": "来源数据库：dwoutput，表名：消费日志表",
    "dprpt_welife_charge_log_bid": "来源数据库：dwoutput，表名：充值日志表",
    "wedatas_welife_coupon_log": "来源数据库：wedatas，表名：券交易日志表"
}

# 报告类型常量
REPORT_TYPES = {
    "week": "周报",
    "month": "月报", 
    "quarter": "季报",
    "halfyear": "半年报"
}

# 反向映射字典：通过key查找字段信息
KEY_TO_FIELD_MAP = {}

def _build_key_mapping():
    """构建key到字段的反向映射"""
    for module_name, module_data in ALL_MODULES.items():
        for field_name, field_data in module_data.items():
            if "key" in field_data:
                KEY_TO_FIELD_MAP[field_data["key"]] = {
                    "module": module_name,
                    "field": field_name,
                    "data": field_data
                }

# 初始化反向映射
_build_key_mapping()

def get_field_info(module_name, field_name):
    """
    获取指定模块和字段的详细信息
    
    Args:
        module_name (str): 模块名称
        field_name (str): 字段名称
        
    Returns:
        dict: 字段信息字典，包含table、field、logic、note、key
    """
    if module_name in ALL_MODULES and field_name in ALL_MODULES[module_name]:
        return ALL_MODULES[module_name][field_name]
    return None

def get_field_by_key(key):
    """
    通过key获取字段信息
    
    Args:
        key (str): 驼峰命名的key
        
    Returns:
        dict: 字段信息字典，包含module、field、data
    """
    return KEY_TO_FIELD_MAP.get(key)

def get_module_fields(module_name):
    """
    获取指定模块的所有字段
    
    Args:
        module_name (str): 模块名称
        
    Returns:
        dict: 模块的所有字段信息
    """
    return ALL_MODULES.get(module_name, {})

def get_all_fields():
    """
    获取所有字段信息
    
    Returns:
        dict: 所有模块的所有字段信息
    """
    return ALL_MODULES

def get_all_keys():
    """
    获取所有字段的key列表
    
    Returns:
        list: 所有字段的key列表
    """
    return list(KEY_TO_FIELD_MAP.keys())