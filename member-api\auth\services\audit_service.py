"""
审计日志服务
记录用户操作日志和系统事件
"""
import json
import logging
from typing import Optional, Dict, Any
from datetime import datetime
import aiomysql

logger = logging.getLogger(__name__)


class AuditService:
    """审计日志服务类"""
    
    def __init__(self, db_pool):
        """
        初始化审计服务
        
        Args:
            db_pool: 数据库连接池
        """
        self.db_pool = db_pool
    
    async def log_action(
        self,
        action: str,
        user_id: Optional[int] = None,
        username: Optional[str] = None,
        resource: Optional[str] = None,
        resource_id: Optional[str] = None,
        request_data: Optional[Dict[str, Any]] = None,
        response_data: Optional[Dict[str, Any]] = None,
        status: str = "success",
        error_message: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> bool:
        """
        记录操作日志
        
        Args:
            action: 操作类型（如 login, logout, create_user, update_bid等）
            user_id: 用户ID
            username: 用户名
            resource: 资源类型（如 user, bid, task等）
            resource_id: 资源ID
            request_data: 请求数据
            response_data: 响应数据
            status: 操作状态（success/failure）
            error_message: 错误信息
            ip_address: IP地址
            user_agent: 用户代理
            
        Returns:
            bool: 是否记录成功
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    sql = """
                    INSERT INTO member_report_audit_logs 
                    (user_id, username, action, resource, resource_id, 
                     request_data, response_data, status, error_message,
                     ip_address, user_agent, created_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                    """
                    
                    # 处理JSON数据
                    request_json = json.dumps(request_data) if request_data else None
                    response_json = json.dumps(response_data) if response_data else None
                    
                    await cursor.execute(sql, (
                        user_id,
                        username,
                        action,
                        resource,
                        resource_id,
                        request_json,
                        response_json,
                        status,
                        error_message,
                        ip_address,
                        user_agent
                    ))
                    await conn.commit()
                    return True
                    
        except Exception as e:
            logger.error(f"记录审计日志失败: {e}")
            return False
    
    async def log_login(
        self,
        username: str,
        user_id: Optional[int] = None,
        success: bool = True,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        error_message: Optional[str] = None
    ) -> bool:
        """
        记录登录日志
        
        Args:
            username: 用户名
            user_id: 用户ID（成功时）
            success: 是否成功
            ip_address: IP地址
            user_agent: 用户代理
            error_message: 错误信息（失败时）
            
        Returns:
            bool: 是否记录成功
        """
        return await self.log_action(
            action="login",
            user_id=user_id,
            username=username,
            resource="auth",
            status="success" if success else "failure",
            error_message=error_message,
            ip_address=ip_address,
            user_agent=user_agent
        )
    
    async def log_logout(
        self,
        user_id: int,
        username: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> bool:
        """
        记录登出日志
        """
        return await self.log_action(
            action="logout",
            user_id=user_id,
            username=username,
            resource="auth",
            status="success",
            ip_address=ip_address,
            user_agent=user_agent
        )
    
    async def log_user_operation(
        self,
        action: str,
        operator_id: int,
        operator_username: str,
        target_user_id: Optional[int] = None,
        request_data: Optional[Dict[str, Any]] = None,
        success: bool = True,
        error_message: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> bool:
        """
        记录用户操作日志（创建、更新、删除用户等）
        """
        return await self.log_action(
            action=action,
            user_id=operator_id,
            username=operator_username,
            resource="user",
            resource_id=str(target_user_id) if target_user_id else None,
            request_data=request_data,
            status="success" if success else "failure",
            error_message=error_message,
            ip_address=ip_address,
            user_agent=user_agent
        )
    
    async def log_bid_operation(
        self,
        action: str,
        user_id: int,
        username: str,
        bid_id: Optional[int] = None,
        bid_code: Optional[str] = None,
        request_data: Optional[Dict[str, Any]] = None,
        success: bool = True,
        error_message: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> bool:
        """
        记录BID操作日志
        """
        resource_id = bid_code if bid_code else str(bid_id) if bid_id else None
        
        return await self.log_action(
            action=action,
            user_id=user_id,
            username=username,
            resource="bid",
            resource_id=resource_id,
            request_data=request_data,
            status="success" if success else "failure",
            error_message=error_message,
            ip_address=ip_address,
            user_agent=user_agent
        )
    
    async def get_audit_logs(
        self,
        page: int = 1,
        page_size: int = 50,
        user_id: Optional[int] = None,
        action: Optional[str] = None,
        resource: Optional[str] = None,
        status: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        查询审计日志
        
        Args:
            page: 页码
            page_size: 每页大小
            user_id: 用户ID筛选
            action: 操作类型筛选
            resource: 资源类型筛选
            status: 状态筛选
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            Dict: 审计日志列表
        """
        try:
            offset = (page - 1) * page_size
            
            # 构建查询条件
            where_conditions = []
            params = []
            
            if user_id:
                where_conditions.append("user_id = %s")
                params.append(user_id)
            
            if action:
                where_conditions.append("action = %s")
                params.append(action)
            
            if resource:
                where_conditions.append("resource = %s")
                params.append(resource)
            
            if status:
                where_conditions.append("status = %s")
                params.append(status)
            
            if start_date:
                where_conditions.append("created_at >= %s")
                params.append(start_date)
            
            if end_date:
                where_conditions.append("created_at <= %s")
                params.append(end_date)
            
            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
            
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 获取总数
                    count_sql = f"SELECT COUNT(*) as total FROM member_report_audit_logs WHERE {where_clause}"
                    await cursor.execute(count_sql, params)
                    total = (await cursor.fetchone())['total']
                    
                    # 获取列表
                    list_sql = f"""
                    SELECT * FROM member_report_audit_logs 
                    WHERE {where_clause}
                    ORDER BY created_at DESC
                    LIMIT %s OFFSET %s
                    """
                    params.extend([page_size, offset])
                    await cursor.execute(list_sql, params)
                    logs = await cursor.fetchall()
                    
                    # 处理JSON字段
                    for log in logs:
                        if log['request_data']:
                            log['request_data'] = json.loads(log['request_data'])
                        if log['response_data']:
                            log['response_data'] = json.loads(log['response_data'])
                        if log['created_at']:
                            log['created_at'] = log['created_at'].strftime("%Y-%m-%d %H:%M:%S")
                    
                    return {
                        "total": total,
                        "page": page,
                        "page_size": page_size,
                        "items": logs
                    }
                    
        except Exception as e:
            logger.error(f"查询审计日志失败: {e}")
            return {
                "total": 0,
                "page": page,
                "page_size": page_size,
                "items": []
            }
    
    async def clean_old_logs(self, days: int = 90) -> int:
        """
        清理旧日志
        
        Args:
            days: 保留天数
            
        Returns:
            int: 删除的记录数
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    sql = """
                    DELETE FROM member_report_audit_logs 
                    WHERE created_at < DATE_SUB(NOW(), INTERVAL %s DAY)
                    """
                    await cursor.execute(sql, (days,))
                    await conn.commit()
                    return cursor.rowcount
                    
        except Exception as e:
            logger.error(f"清理审计日志失败: {e}")
            return 0
    
    async def get_today_statistics(self) -> Dict[str, Any]:
        """
        获取今日操作统计
        
        Returns:
            Dict: 今日统计信息
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 获取今日各类操作统计
                    sql = """
                    SELECT 
                        COUNT(*) as total_actions,
                        SUM(CASE WHEN action = 'login' THEN 1 ELSE 0 END) as login_count,
                        SUM(CASE WHEN action IN ('query', 'data_query', 'member_query') THEN 1 ELSE 0 END) as query_count,
                        SUM(CASE WHEN action IN ('generate_ppt', 'create_ppt', 'export_ppt') THEN 1 ELSE 0 END) as ppt_count,
                        COUNT(DISTINCT username) as active_users
                    FROM member_report_audit_logs
                    WHERE DATE(created_at) = CURDATE()
                    AND status = 'success'
                    """
                    
                    await cursor.execute(sql)
                    stats = await cursor.fetchone()
                    
                    return {
                        "total_actions": stats['total_actions'] or 0,
                        "login_count": stats['login_count'] or 0,
                        "query_count": stats['query_count'] or 0,
                        "ppt_generation_count": stats['ppt_count'] or 0,
                        "active_users": stats['active_users'] or 0
                    }
                    
        except Exception as e:
            logger.error(f"获取今日统计失败: {e}")
            return {
                "total_actions": 0,
                "login_count": 0,
                "query_count": 0,
                "ppt_generation_count": 0,
                "active_users": 0
            }