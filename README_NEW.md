# 会员报表系统

一个完整的会员数据查询和报表生成系统，包含前台查询系统、后台管理系统和API服务。

## 系统架构

```
┌─────────────────────────────────────────────────┐
│                   前台查询系统                    │
│              (Vue 3 + Element Plus)              │
│                  端口: 5173                      │
│         - 会员数据查询                           │
│         - PPT报告生成                            │
└──────────────────┬──────────────────────────────┘
                   │
┌─────────────────────────────────────────────────┐
│                   后台管理系统                    │
│              (Vue 3 + Element Plus)              │
│                  端口: 5175                      │
│         - 用户管理                               │
│         - 权限配置                               │
│         - 系统初始化                             │
└──────────────────┬──────────────────────────────┘
                   │
                   ▼
┌─────────────────────────────────────────────────┐
│                  后端API服务                      │
│                 (FastAPI + MySQL)                │
│                  端口: 8000                      │
│         - 认证授权                               │
│         - 数据接口                               │
│         - 任务管理                               │
└──────────────────┬──────────────────────────────┘
                   │
                   ▼
┌─────────────────────────────────────────────────┐
│                   MySQL数据库                     │
│              Database: task_management           │
│         - member_report_users (用户表)           │
│         - member_report_sessions (会话表)        │
│         - member_report_audit_logs (审计日志表)  │
└─────────────────────────────────────────────────┘
```

## 快速开始

### 步骤 1: 初始化数据库
```bash
# 创建数据库和表结构
init_database.bat
```

### 步骤 2: 启动所有服务
```bash
# 一键启动前后台和API服务
start_all.bat
```

### 步骤 3: 初始化超级管理员

访问后台管理系统 http://localhost:5175，点击"初始化超级管理员"链接创建管理员账号。

或者运行命令行工具：
```bash
init_admin.bat
```

### 步骤 4: 开始使用

- **后台管理系统**: http://localhost:5175 (管理员专用)
- **前台查询系统**: http://localhost:5173 (所有用户)
- **API文档**: http://localhost:8000/docs

## 项目结构

```
member_report_test/
├── member-api/              # 后端API服务
│   ├── auth/               # 认证模块
│   ├── core/               # 核心配置
│   ├── api/                # API接口
│   └── main.py             # 主程序入口
│
├── vue-element/             # 前台查询系统
│   ├── src/
│   │   ├── views/          # 页面组件
│   │   ├── api/            # API接口
│   │   └── stores/         # 状态管理
│   └── vite.config.js      # Vite配置
│
├── back_management/         # 后台管理系统
│   ├── src/
│   │   ├── views/          # 页面组件
│   │   │   ├── Login.vue   # 登录页
│   │   │   └── InitSuperAdmin.vue # 初始化页
│   │   ├── api/            # API接口
│   │   └── stores/         # 状态管理
│   └── vite.config.js      # Vite配置
│
├── init_database.bat        # 数据库初始化
├── init_database.py         # 数据库初始化脚本
├── init_admin.bat          # 管理员初始化
├── init_admin.py           # 管理员初始化脚本
├── start_all.bat           # 一键启动
├── stop_all.bat            # 一键停止
├── test_login.html         # 登录测试工具
└── 系统使用说明.md          # 详细使用文档
```

## 功能特性

### 前台查询系统
- 会员基础数据查询
- 会员消费数据分析
- 会员充值数据统计
- PPT报告自动生成
- 数据导出功能

### 后台管理系统
- 超级管理员初始化
- 用户账号管理
- 权限配置
- 系统监控
- 审计日志

### API服务
- JWT认证
- RESTful API
- 异步处理
- 任务队列
- 数据缓存

## 系统要求

- Python 3.8+
- Node.js 14+
- MySQL 5.7+
- Windows/Linux/MacOS

## 常见问题

### Q: 如何重置管理员密码？
A: 运行 `init_admin.bat`，选择重置密码选项。

### Q: 端口被占用怎么办？
A: 修改相应的配置文件：
- API服务: `member-api/core/config.py`
- 前台系统: `vue-element/vite.config.js`
- 后台系统: `back_management/vite.config.js`

### Q: 如何查看日志？
A: 日志文件位于 `member-api/logs/` 目录。

### Q: 如何停止所有服务？
A: 运行 `stop_all.bat` 或手动关闭命令行窗口。

## 技术支持

详细使用说明请查看 [系统使用说明.md](系统使用说明.md)

## 许可证

本项目仅供内部使用。