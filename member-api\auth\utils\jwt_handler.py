"""
JWT Token处理工具
用于生成和验证JWT Token
"""
import os
import logging
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from jose import JWTError, jwt

logger = logging.getLogger(__name__)

# JWT配置（从环境变量读取）
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key-change-this-in-production")
JWT_ALGORITHM = os.getenv("JWT_ALGORITHM", "HS256")
JWT_ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "1440"))  # 访问Token过期时间（分钟）- 默认24小时
JWT_REFRESH_TOKEN_EXPIRE_DAYS = int(os.getenv("JWT_REFRESH_TOKEN_EXPIRE_DAYS", "30"))  # 刷新Token过期时间（天）- 默认30天


class JWTHandler:
    """JWT处理器"""
    
    def __init__(
        self,
        secret_key: str = JWT_SECRET_KEY,
        algorithm: str = JWT_ALGORITHM,
        access_token_expire_minutes: int = JWT_ACCESS_TOKEN_EXPIRE_MINUTES,
        refresh_token_expire_days: int = JWT_REFRESH_TOKEN_EXPIRE_DAYS
    ):
        """
        初始化JWT处理器
        
        Args:
            secret_key: 密钥
            algorithm: 加密算法
            access_token_expire_minutes: 访问Token过期时间（分钟）
            refresh_token_expire_days: 刷新Token过期时间（天）
        """
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.access_token_expire_minutes = access_token_expire_minutes
        self.refresh_token_expire_days = refresh_token_expire_days
    
    def create_access_token(
        self,
        data: Dict[str, Any],
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """
        创建访问Token
        
        Args:
            data: Token载荷数据
            expires_delta: 过期时间差
            
        Returns:
            str: JWT Token
        """
        to_encode = data.copy()
        
        # 设置过期时间
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        to_encode.update({
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "access"
        })
        
        # 生成Token
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def create_refresh_token(
        self,
        data: Dict[str, Any],
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """
        创建刷新Token
        
        Args:
            data: Token载荷数据
            expires_delta: 过期时间差
            
        Returns:
            str: JWT Refresh Token
        """
        to_encode = data.copy()
        
        # 设置过期时间
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)
        
        to_encode.update({
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "refresh"
        })
        
        # 生成Token
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def decode_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        解码Token
        
        Args:
            token: JWT Token
            
        Returns:
            Dict: 解码后的载荷数据，失败返回None
        """
        # 检查Token是否为空或无效
        if not token or token == "null" or token == "undefined" or token == "":
            logger.warning(f"无效的Token值: '{token}'")
            return None
            
        # 检查Token格式（应该有3个部分）
        parts = token.split('.')
        if len(parts) != 3:
            logger.warning(f"Token格式错误，期望3个部分，实际{len(parts)}个部分")
            return None
            
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except JWTError as e:
            logger.error(f"Token解码失败: {e}")
            return None
        except Exception as e:
            logger.error(f"Token解码异常: {e}")
            return None
    
    def verify_token(self, token: str, token_type: str = "access") -> Optional[Dict[str, Any]]:
        """
        验证Token
        
        Args:
            token: JWT Token
            token_type: Token类型 (access/refresh)
            
        Returns:
            Dict: 验证成功返回载荷数据，失败返回None
        """
        payload = self.decode_token(token)
        
        if not payload:
            return None
        
        # 检查Token类型
        if payload.get("type") != token_type:
            logger.warning(f"Token类型不匹配: 期望{token_type}, 实际{payload.get('type')}")
            return None
        
        # 检查过期时间
        exp = payload.get("exp")
        if exp:
            if datetime.fromtimestamp(exp) < datetime.utcnow():
                logger.info("Token已过期")
                return None
        
        return payload
    
    def get_token_expire_time(self, token: str) -> Optional[datetime]:
        """
        获取Token过期时间
        
        Args:
            token: JWT Token
            
        Returns:
            datetime: 过期时间，失败返回None
        """
        payload = self.decode_token(token)
        
        if not payload:
            return None
        
        exp = payload.get("exp")
        if exp:
            return datetime.fromtimestamp(exp)
        
        return None
    
    def create_token_pair(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建Token对（访问Token和刷新Token）
        
        Args:
            user_data: 用户数据
            
        Returns:
            Dict: 包含access_token和refresh_token
        """
        # 创建访问Token
        access_token = self.create_access_token(user_data)
        
        # 创建刷新Token（只包含必要信息）
        refresh_data = {
            "user_id": user_data.get("user_id"),
            "username": user_data.get("username")
        }
        refresh_token = self.create_refresh_token(refresh_data)
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "Bearer",
            "expires_in": self.access_token_expire_minutes * 60  # 转换为秒
        }
    
    def refresh_access_token(self, refresh_token: str) -> Optional[str]:
        """
        使用刷新Token生成新的访问Token
        
        Args:
            refresh_token: 刷新Token
            
        Returns:
            str: 新的访问Token，失败返回None
        """
        # 验证刷新Token
        payload = self.verify_token(refresh_token, token_type="refresh")
        
        if not payload:
            return None
        
        # 创建新的访问Token
        user_data = {
            "user_id": payload.get("user_id"),
            "username": payload.get("username")
        }
        
        return self.create_access_token(user_data)


# 创建默认JWT处理器实例
jwt_handler = JWTHandler()

# 导出便捷函数
create_access_token = jwt_handler.create_access_token
create_refresh_token = jwt_handler.create_refresh_token
decode_token = jwt_handler.decode_token
verify_token = jwt_handler.verify_token
create_token_pair = jwt_handler.create_token_pair
refresh_access_token = jwt_handler.refresh_access_token