# API服务配置
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=true

# 环境设置
# 可选值: production, beta, development
ENVIRONMENT=development

# 灵积配置，通义api配置
DASHSCOPE_API_KEY=your_api_key_here
DASHSCOPE_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
DASHSCOPE_MODEL=qwen-max

# MySQL数据库配置 - dwoutput数据库
DWOUTPUT_DB_HOST=localhost
DWOUTPUT_DB_PORT=3306
DWOUTPUT_DB_USER=your_actual_username
DWOUTPUT_DB_PASSWORD=your_actual_password
DWOUTPUT_DB_NAME=dwoutput
DWOUTPUT_DB_CHARSET=utf8mb4

# MySQL数据库配置 - wedatas数据库  
WEDATAS_DB_HOST=localhost
WEDATAS_DB_PORT=3306
WEDATAS_DB_USER=your_actual_username
WEDATAS_DB_PASSWORD=your_actual_password
WEDATAS_DB_NAME=wedatas
WEDATAS_DB_CHARSET=utf8mb4

# welife数据库配置 - welife_hydb数据库
WELIFE_HYDB_DB_HOST=localhost
WELIFE_HYDB_DB_PORT=3306
WELIFE_HYDB_DB_USER=your_actual_username
WELIFE_HYDB_DB_PASSWORD=your_actual_password
WELIFE_HYDB_DB_NAME=welife_hydb
WELIFE_HYDB_DB_CHARSET=utf8mb4

# MySQL数据库配置 - basic_info数据库（企业微信数据库）
BASIC_INFO_DB_HOST=localhost
BASIC_INFO_DB_PORT=3306
BASIC_INFO_DB_USER=your_actual_username
BASIC_INFO_DB_PASSWORD=your_actual_password
BASIC_INFO_DB_NAME=basic_info
BASIC_INFO_DB_CHARSET=utf8mb4

# MySQL数据库配置 - backend数据库（企业微信和微生活对应数据库）
BACKEND_DB_HOST=localhost
BACKEND_DB_PORT=3306
BACKEND_DB_USER=your_actual_username
BACKEND_DB_PASSWORD=your_actual_password
BACKEND_DB_NAME=backend
BACKEND_DB_CHARSET=utf8mb4

# PostgreSQL数据库配置 - 品质收银数据库
POS_DW_DB_HOST=your_postgres_host_here
POS_DW_DB_PORT=5432
POS_DW_DB_USER=your_postgres_username_here
POS_DW_DB_PASSWORD=your_postgres_password_here
POS_DW_DB_NAME=pos_dw
POS_DW_DB_CHARSET=utf8

# 阿里云OSS配置（用于PPT文件存储 - 云存储+本地留档双重模式）
# 请替换为您的真实OSS配置信息
OSS_ACCESS_KEY_ID=your_oss_access_key_id
OSS_ACCESS_KEY_SECRET=your_oss_access_key_secret
OSS_BUCKET_NAME=your_oss_bucket_name
OSS_ENDPOINT=https://oss-cn-hangzhou.aliyuncs.com
OSS_REGION=oss-cn-hangzhou

# OSS配置说明：
# 1. 如果OSS配置不完整或连接失败，系统将自动降级为仅本地存储模式
# 2. 配置完整且连接成功时，文件将同时上传到OSS和保存本地副本
# 3. 下载时优先使用OSS预签名URL，本地文件作为备选方案
# 4. 本地留档路径：./uploads/ppt-reports/

# MySQL数据库配置 - task_management任务管理数据库
TASK_MANAGEMENT_DB_HOST=localhost
TASK_MANAGEMENT_DB_PORT=3306
TASK_MANAGEMENT_DB_USER=your_actual_username
TASK_MANAGEMENT_DB_PASSWORD=your_actual_password
TASK_MANAGEMENT_DB_NAME=ai_yuce
TASK_MANAGEMENT_DB_CHARSET=utf8mb4

# 任务管理配置
# 最大并行任务数量，控制同时处理的任务数
MAX_CONCURRENT_TASKS=1

# 调度器轮询间隔（秒，建议5-10秒）
WORKER_POLL_INTERVAL=5

# 重试失败任务的检查间隔（秒，建议10-30秒）
RETRY_CHECK_INTERVAL=300

# 超时任务检查间隔（秒，建议30-60秒）
TIMEOUT_CHECK_INTERVAL=1200

# ==============================================================================
# 自动重置失败任务配置
# ==============================================================================

# 是否启用自动重置失败任务（true/false）
AUTO_RESET_FAILED_TASKS=true

# 自动重置失败任务的间隔（秒，建议300-600秒）
AUTO_RESET_INTERVAL=300

# 系统版本号
SYSTEM_VERSION=1.0.0


# ==============================================================================
# JWT认证配置（重要：生产环境必须修改）
# ==============================================================================

# JWT密钥 - 用于签名和验证Token
# 重要：生产环境必须修改为至少32位的随机字符串！
# 生成强随机密钥命令：python3 -c "import secrets; print(secrets.token_hex(32))"
JWT_SECRET_KEY=your-secret-key-change-this-in-production

# JWT算法 - 推荐使用HS256
JWT_ALGORITHM=HS256

# 访问Token过期时间（分钟）- 默认24小时
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=1440

# 刷新Token过期时间（天）- 默认30天
JWT_REFRESH_TOKEN_EXPIRE_DAYS=30

# ==============================================================================
# CORS跨域配置
# ==============================================================================

# 允许的跨域来源（生产环境请修改为实际域名或IP）
# 格式：["http://domain1.com", "https://domain2.com"]
# 示例配置：
# - 开发环境：["http://localhost:5173", "http://localhost:5174", "http://localhost:3000"]
# - 生产环境（域名）：["https://app.yourdomain.com", "https://admin.yourdomain.com"]
# - 生产环境（IP）：["http://123.456.789.0:5173", "http://123.456.789.0:5174"]
# 注意：使用服务器公网IP时，格式为 http://公网IP:端口
CORS_ORIGINS=["http://localhost:5173", "http://localhost:5174", "http://localhost:3000"]

# ==============================================================================
# 公网访问配置（生产环境）
# ==============================================================================

# 文件服务基础URL（用于生成文件下载链接）
# 开发环境：http://localhost:8000
# 生产环境（域名）：https://api.yourdomain.com
# 生产环境（IP）：http://公网IP:8000
FILE_BASE_URL=http://localhost:8000

# API服务器监听地址
# 0.0.0.0 表示监听所有网络接口（支持公网访问）
# 127.0.0.1 表示仅本地访问
API_HOST=0.0.0.0