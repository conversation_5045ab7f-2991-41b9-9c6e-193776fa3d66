#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复OSS服务属性错误的脚本
强制重新初始化PPTReportService实例
"""

import sys
import importlib
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def force_reinit_ppt_service():
    """强制重新初始化PPT服务"""
    try:
        # 清理已导入的模块
        modules_to_reload = [
            'api.PPTreport.PPTReport',
            'services.oss_service',
            'services.ppt_service'
        ]
        
        for module_name in modules_to_reload:
            if module_name in sys.modules:
                logger.info(f"重新加载模块: {module_name}")
                del sys.modules[module_name]
        
        # 重新导入PPTReport模块
        from api.PPTreport import PPTReport
        
        # 重新加载模块
        importlib.reload(PPTReport)
        
        # 获取新的服务实例
        from api.PPTreport.PPTReport import ppt_report_service
        
        # 验证OSS服务
        if hasattr(ppt_report_service, 'oss_service'):
            logger.info("✅ OSS服务属性存在")
            if ppt_report_service.oss_service:
                logger.info("✅ OSS服务已初始化")
            else:
                logger.info("⚠️ OSS服务为None（可能是配置未启用）")
        else:
            logger.error("❌ OSS服务属性不存在")
            
            # 手动添加OSS服务
            try:
                from services.oss_service import oss_service
                ppt_report_service.oss_service = oss_service
                logger.info("✅ 手动添加OSS服务成功")
            except Exception as e:
                logger.error(f"❌ 手动添加OSS服务失败: {e}")
                ppt_report_service.oss_service = None
        
        return ppt_report_service
        
    except Exception as e:
        logger.error(f"重新初始化失败: {e}")
        return None

if __name__ == "__main__":
    print("=" * 60)
    print("修复OSS服务属性错误")
    print("=" * 60)
    
    service = force_reinit_ppt_service()
    
    if service:
        print("\n✅ 服务重新初始化成功")
        print("\n建议操作：")
        print("1. 重启FastAPI服务")
        print("2. 执行: python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000")
    else:
        print("\n❌ 服务重新初始化失败")