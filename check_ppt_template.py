#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查PPT模板文件的幻灯片数量和参数
"""

import re
from pptx import Presentation

def check_ppt_template():
    """检查PPT模板"""
    template_path = "member-api/ppt_template/会员数据报告-模板.pptx"
    
    try:
        ppt = Presentation(template_path)
        print(f"PPT模板文件: {template_path}")
        print(f"总幻灯片数量: {len(ppt.slides)}")
        print()
        
        # 检查每一页的参数
        param_pattern = r'\{(.*?)\}'
        
        for slide_idx, slide in enumerate(ppt.slides):
            slide_num = slide_idx + 1
            params = set()
            
            for shape in slide.shapes:
                if shape.has_text_frame:
                    for paragraph in shape.text_frame.paragraphs:
                        for run in paragraph.runs:
                            matches = re.findall(param_pattern, run.text)
                            params.update(matches)
            
            print(f"幻灯片 {slide_num}: 参数数量 {len(params)}")
            if params:
                # 按字母顺序排序参数
                sorted_params = sorted(params)
                for param in sorted_params:
                    print(f"  - {param}")
            print()
        
        # 特别检查第19页
        if len(ppt.slides) >= 19:
            slide_19 = ppt.slides[18]  # 索引从0开始
            params_19 = set()
            
            for shape in slide_19.shapes:
                if shape.has_text_frame:
                    for paragraph in shape.text_frame.paragraphs:
                        for run in paragraph.runs:
                            matches = re.findall(param_pattern, run.text)
                            params_19.update(matches)
            
            print("=" * 50)
            print(f"第19页详细分析:")
            print(f"参数数量: {len(params_19)}")
            if params_19:
                print("参数列表:")
                for param in sorted(params_19):
                    print(f"  - {param}")
            else:
                print("第19页没有找到任何参数占位符")
        else:
            print("=" * 50)
            print(f"模板只有 {len(ppt.slides)} 页，没有第19页")
            
    except Exception as e:
        print(f"检查PPT模板失败: {e}")

if __name__ == "__main__":
    check_ppt_template()
