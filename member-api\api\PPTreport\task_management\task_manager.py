"""
任务管理器核心功能实现
"""

import logging
import json
from typing import List, Optional, Dict, Any
from datetime import datetime
from core.config import settings
from core.database import db
from .models import TaskModel, TaskStatus, TaskPriority
from .database_init import TaskDatabaseInitializer

logger = logging.getLogger(__name__)


class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self.initializer = TaskDatabaseInitializer()
        self.table_name = self.initializer.table_name
        
    async def create_task(self, task: TaskModel) -> TaskModel:
        """创建新任务"""
        try:
            # 构建插入SQL
            task_dict = task.to_dict()
            
            # 移除id字段（自增）
            task_dict.pop('id', None)
            
            # 确保created_at有值
            if 'created_at' not in task_dict:
                task_dict['created_at'] = datetime.now()
            
            columns = list(task_dict.keys())
            values = list(task_dict.values())
            placeholders = ', '.join(['%s'] * len(columns))
            columns_str = ', '.join([f'`{col}`' for col in columns])
            
            sql = f"""
                INSERT INTO `{self.table_name}` ({columns_str})
                VALUES ({placeholders})
            """
            
            async with db.get_task_management_connection() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(sql, values)
                    await conn.commit()
                    
                    # 获取插入的ID
                    task.id = cursor.lastrowid
                    
                    logger.info(f"任务创建成功: ID={task.id}, UUID={task.task_uuid}")
                    return task
                    
        except Exception as e:
            logger.error(f"创建任务失败: {e}")
            raise
    
    async def get_task_by_id(self, task_id: int) -> Optional[TaskModel]:
        """根据ID获取任务"""
        try:
            sql = f"SELECT * FROM `{self.table_name}` WHERE `id` = %s"
            
            result = await db.execute_task_management_one(sql, (task_id,))
            
            if result:
                return TaskModel.from_dict(result)
            return None
            
        except Exception as e:
            logger.error(f"获取任务失败: {e}")
            return None
    
    async def get_task_by_uuid(self, task_uuid: str) -> Optional[TaskModel]:
        """根据UUID获取任务"""
        try:
            sql = f"SELECT * FROM `{self.table_name}` WHERE `task_uuid` = %s"
            
            result = await db.execute_task_management_one(sql, (task_uuid,))
            
            if result:
                return TaskModel.from_dict(result)
            return None
            
        except Exception as e:
            logger.error(f"获取任务失败: {e}")
            return None
    
    async def get_pending_tasks(self, limit: int = 10) -> List[TaskModel]:
        """获取待处理的任务列表（按优先级和创建时间排序）"""
        try:
            sql = f"""
                SELECT * FROM `{self.table_name}`
                WHERE `status` = %s
                ORDER BY `priority` DESC, `created_at` ASC
                LIMIT %s
            """
            
            results = await db.execute_task_management_query(
                sql, (TaskStatus.PENDING.value, limit)
            )
            
            tasks = [TaskModel.from_dict(row) for row in results]
            logger.info(f"获取到 {len(tasks)} 个待处理任务")
            return tasks
            
        except Exception as e:
            logger.error(f"获取待处理任务失败: {e}")
            return []
    
    async def get_processing_tasks(self) -> List[TaskModel]:
        """获取正在处理的任务列表"""
        try:
            sql = f"""
                SELECT * FROM `{self.table_name}`
                WHERE `status` = %s
                ORDER BY `started_at` ASC
            """
            
            results = await db.execute_task_management_query(
                sql, (TaskStatus.PROCESSING.value,)
            )
            
            return [TaskModel.from_dict(row) for row in results]
            
        except Exception as e:
            logger.error(f"获取处理中任务失败: {e}")
            return []
    
    async def update_task(self, task: TaskModel) -> bool:
        """更新任务信息"""
        try:
            task_dict = task.to_dict()
            
            # 构建更新SQL
            update_fields = []
            values = []
            
            for key, value in task_dict.items():
                if key != 'id':  # 跳过ID字段
                    update_fields.append(f"`{key}` = %s")
                    values.append(value)
            
            # 添加ID到参数末尾
            values.append(task.id)
            
            sql = f"""
                UPDATE `{self.table_name}`
                SET {', '.join(update_fields)}
                WHERE `id` = %s
            """
            
            affected_rows = await db.execute_task_management_update(sql, tuple(values))
            
            if affected_rows > 0:
                logger.info(f"任务更新成功: ID={task.id}")
                return True
            else:
                logger.warning(f"任务更新失败，未找到任务: ID={task.id}")
                return False
                
        except Exception as e:
            logger.error(f"更新任务失败: {e}")
            return False
    
    async def update_task_status(
        self, 
        task_id: int, 
        status: TaskStatus,
        current_step: Optional[str] = None,
        progress: Optional[int] = None
    ) -> bool:
        """更新任务状态"""
        try:
            # 获取任务
            task = await self.get_task_by_id(task_id)
            if not task:
                logger.error(f"任务不存在: ID={task_id}")
                return False
            
            # 更新状态
            task.update_status(status, current_step)
            
            # 更新进度
            if progress is not None:
                task.update_progress(progress)
            
            # 保存到数据库
            return await self.update_task(task)
            
        except Exception as e:
            logger.error(f"更新任务状态失败: {e}")
            return False
    
    async def update_task_progress(
        self,
        task_id: int,
        progress: int,
        current_step: Optional[str] = None
    ) -> bool:
        """更新任务进度"""
        try:
            sql = f"""
                UPDATE `{self.table_name}`
                SET `progress` = %s, `current_step` = %s
                WHERE `id` = %s
            """
            
            affected_rows = await db.execute_task_management_update(
                sql, (progress, current_step, task_id)
            )
            
            if affected_rows > 0:
                logger.info(f"任务进度更新成功: ID={task_id}, 进度={progress}%")
                return True
            return False
            
        except Exception as e:
            logger.error(f"更新任务进度失败: {e}")
            return False
    
    async def mark_task_failed(
        self,
        task_id: int,
        error_message: str,
        error_details: Optional[Dict] = None
    ) -> bool:
        """标记任务失败"""
        try:
            task = await self.get_task_by_id(task_id)
            if not task:
                return False
            
            # 添加错误信息
            task.add_error(error_message, error_details)
            
            # 检查是否可以重试
            if task.can_retry():
                # 重置为待处理状态，等待重试
                task.status = TaskStatus.PENDING.value
                logger.info(f"任务失败但可重试: ID={task_id}, 重试次数={task.retry_count}")
            else:
                # 标记为失败
                task.status = TaskStatus.FAILED.value
                task.completed_at = datetime.now()
                logger.error(f"任务失败且无法重试: ID={task_id}")
            
            return await self.update_task(task)
            
        except Exception as e:
            logger.error(f"标记任务失败时出错: {e}")
            return False
    
    async def cancel_task(self, task_id: int) -> bool:
        """取消任务"""
        try:
            task = await self.get_task_by_id(task_id)
            if not task:
                return False
            
            # 只能取消未完成的任务
            if task.is_terminal_status():
                logger.warning(f"任务已处于终止状态，无法取消: ID={task_id}")
                return False
            
            task.status = TaskStatus.CANCELLED.value
            task.completed_at = datetime.now()
            
            return await self.update_task(task)
            
        except Exception as e:
            logger.error(f"取消任务失败: {e}")
            return False
    
    async def get_user_tasks(
        self,
        user_id: str,  # 修改为字符串类型，存储username
        status: Optional[TaskStatus] = None,
        limit: int = 20
    ) -> List[TaskModel]:
        """获取用户的任务列表"""
        try:
            if status:
                sql = f"""
                    SELECT * FROM `{self.table_name}`
                    WHERE `user_id` = %s AND `status` = %s
                    ORDER BY `created_at` DESC
                    LIMIT %s
                """
                params = (user_id, status.value, limit)
            else:
                sql = f"""
                    SELECT * FROM `{self.table_name}`
                    WHERE `user_id` = %s
                    ORDER BY `created_at` DESC
                    LIMIT %s
                """
                params = (user_id, limit)
            
            results = await db.execute_task_management_query(sql, params)
            
            return [TaskModel.from_dict(row) for row in results]
            
        except Exception as e:
            logger.error(f"获取用户任务失败: {e}")
            return []
    
    async def get_task_statistics(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        try:
            sql = f"""
                SELECT 
                    `status`,
                    COUNT(*) as count,
                    AVG(`actual_duration`) as avg_duration,
                    SUM(`tokens_used`) as total_tokens
                FROM `{self.table_name}`
                GROUP BY `status`
            """
            
            results = await db.execute_task_management_query(sql)
            
            stats = {
                'by_status': {},
                'total': 0,
                'total_tokens': 0,
                'avg_duration': 0
            }
            
            total_duration = 0
            duration_count = 0
            
            for row in results:
                status = row['status']
                stats['by_status'][status] = {
                    'count': row['count'],
                    'avg_duration': float(row['avg_duration']) if row['avg_duration'] else 0,
                    'total_tokens': row['total_tokens'] or 0
                }
                stats['total'] += row['count']
                stats['total_tokens'] += row['total_tokens'] or 0
                
                if row['avg_duration']:
                    total_duration += float(row['avg_duration']) * row['count']
                    duration_count += row['count']
            
            if duration_count > 0:
                stats['avg_duration'] = total_duration / duration_count
            
            return stats
            
        except Exception as e:
            logger.error(f"获取任务统计失败: {e}")
            return {}
    
    async def clean_old_tasks(self, days: int = 30) -> int:
        """清理旧任务记录"""
        try:
            sql = f"""
                DELETE FROM `{self.table_name}`
                WHERE `completed_at` < DATE_SUB(NOW(), INTERVAL %s DAY)
                AND `status` IN (%s, %s, %s)
            """
            
            affected_rows = await db.execute_task_management_update(
                sql, 
                (days, TaskStatus.COMPLETED.value, 
                 TaskStatus.FAILED.value, 
                 TaskStatus.CANCELLED.value)
            )
            
            logger.info(f"清理了 {affected_rows} 条旧任务记录")
            return affected_rows
            
        except Exception as e:
            logger.error(f"清理旧任务失败: {e}")
            return 0
    
    async def get_retryable_failed_tasks(self, limit: int = 10) -> List[TaskModel]:
        """获取可重试的失败任务"""
        try:
            sql = f"""
                SELECT * FROM `{self.table_name}`
                WHERE `status` = %s
                AND `retry_count` < `max_retry_count`
                ORDER BY `priority` DESC, `created_at` ASC
                LIMIT %s
            """
            
            results = await db.execute_task_management_query(
                sql, 
                (TaskStatus.FAILED.value, limit)
            )
            
            return [TaskModel.from_dict(row) for row in results]
            
        except Exception as e:
            logger.error(f"获取可重试任务失败: {e}")
            return []
    
    async def get_processing_tasks(self) -> List[TaskModel]:
        """获取所有处理中的任务"""
        try:
            sql = f"""
                SELECT * FROM `{self.table_name}`
                WHERE `status` = %s
                ORDER BY `started_at` ASC
            """
            
            results = await db.execute_task_management_query(
                sql, 
                (TaskStatus.PROCESSING.value,)
            )
            
            return [TaskModel.from_dict(row) for row in results]
            
        except Exception as e:
            logger.error(f"获取处理中任务失败: {e}")
            return []
    
    async def get_old_failed_tasks(self, threshold_time: datetime) -> List[TaskModel]:
        """获取超过指定时间的失败任务"""
        try:
            sql = f"""
                SELECT * FROM `{self.table_name}`
                WHERE `status` = %s
                AND `completed_at` < %s
                AND `retry_count` >= `max_retry_count`
                ORDER BY `completed_at` ASC
            """
            
            results = await db.execute_task_management_query(
                sql, 
                (TaskStatus.FAILED.value, threshold_time)
            )
            
            return [TaskModel.from_dict(row) for row in results]
            
        except Exception as e:
            logger.error(f"获取旧失败任务失败: {e}")
            return []
    
    async def reset_task_for_retry(self, task_id: int, reset_retry_count: bool = False) -> bool:
        """重置任务为待处理状态以便重试"""
        try:
            if reset_retry_count:
                sql = f"""
                    UPDATE `{self.table_name}`
                    SET `status` = %s,
                        `retry_count` = 0,
                        `progress` = 0,
                        `current_step` = '等待重试',
                        `started_at` = NULL,
                        `completed_at` = NULL,
                        `error_info` = NULL
                    WHERE `id` = %s
                """
            else:
                sql = f"""
                    UPDATE `{self.table_name}`
                    SET `status` = %s,
                        `retry_count` = `retry_count` + 1,
                        `progress` = 0,
                        `current_step` = '等待重试',
                        `started_at` = NULL,
                        `completed_at` = NULL,
                        `error_info` = NULL
                    WHERE `id` = %s
                """
            
            affected_rows = await db.execute_task_management_update(
                sql, 
                (TaskStatus.PENDING.value, task_id)
            )
            
            if affected_rows > 0:
                logger.info(f"任务 {task_id} 已重置为待处理状态")
                return True
            return False
            
        except Exception as e:
            logger.error(f"重置任务失败: {e}")
            return False