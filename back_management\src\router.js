import { createRouter, createWebHistory } from 'vue-router'

// 布局组件
import Layout from '@/layout/Layout.vue'

// 页面组件
import Login from '@/views/Login.vue'
import InitSuperAdmin from '@/views/InitSuperAdmin.vue'
import Dashboard from '@/views/dashboard/Dashboard.vue'
import UserList from '@/views/users/UserList.vue'
import BidList from '@/views/bid-config/BidList.vue'
import BidApproval from '@/views/bid-approval/BidApproval.vue'
import UserProfile from '@/views/profile/UserProfile.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresAuth: false }
  },
  {
    path: '/init',
    name: 'InitSuperAdmin',
    component: InitSuperAdmin,
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: Dashboard,
        meta: { title: '仪表盘', icon: 'HomeFilled', requiresAdmin: true }
      },
      {
        path: 'users',
        name: 'UserList',
        component: UserList,
        meta: { title: '用户管理', icon: 'User', requiresAdmin: true }
      },
      {
        path: 'bid-config',
        name: 'BidList',
        component: BidList,
        meta: { title: 'BID配置', icon: 'Setting', requiresAdmin: true }
      },
      {
        path: 'bid-approval',
        name: 'BidApproval',
        component: BidApproval,
        meta: { title: 'BID审批', icon: 'DocumentChecked', requiresAdmin: true }
      },
      {
        path: 'profile',
        name: 'UserProfile',
        component: UserProfile,
        meta: { title: '个人中心', icon: 'UserFilled', requiresAdmin: false }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
let authInitialized = false
router.beforeEach(async (to, from, next) => {
  // 动态导入，确保Pinia已经初始化
  const { useAuthStore } = await import('@/stores/auth')
  const authStore = useAuthStore()
  
  // 确保认证状态已初始化
  if (!authInitialized) {
    try {
      await authStore.initializeAuth()
      authInitialized = true
    } catch (error) {
      console.error('认证初始化失败:', error)
    }
  }
  
  if (to.meta.requiresAuth !== false) {
    if (!authStore.isLoggedIn) {
      next('/login')
    } else if (to.meta.requiresAdmin && !authStore.isAdmin) {
      next('/403')
    } else {
      next()
    }
  } else {
    if (to.path === '/login' && authStore.isLoggedIn) {
      next('/')
    } else {
      next()
    }
  }
})

export default router