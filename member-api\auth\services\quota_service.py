"""
用户配额管理服务
管理用户的PPT生成配额
"""
import logging
import json
from typing import Dict, Any, Optional
from datetime import datetime
import aiomysql

logger = logging.getLogger(__name__)


class QuotaService:
    """配额管理服务"""
    
    def __init__(self, db_pool):
        self.db_pool = db_pool
    
    async def check_user_quota(self, username: str) -> Dict[str, Any]:
        """
        检查用户配额使用情况
        
        Args:
            username: 用户名
            
        Returns:
            配额信息字典
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    sql = """
                    SELECT quotas, use_quotas 
                    FROM member_report_users 
                    WHERE username = %s
                    """
                    await cursor.execute(sql, (username,))
                    user = await cursor.fetchone()
                    
                    if not user:
                        return {
                            "has_quota": False,
                            "message": "用户不存在"
                        }
                    
                    # 获取配额信息
                    ppt_quota = user['quotas'] if user['quotas'] else 0
                    ppt_used = user['use_quotas'] if user['use_quotas'] else 0
                    
                    # 如果配额为0或None，表示无限制
                    if ppt_quota is None or ppt_quota == 0:
                        return {
                            "has_quota": True,
                            "unlimited": True,
                            "quota": 0 if ppt_quota == 0 else None,
                            "used": ppt_used,
                            "remaining": None,
                            "message": "无限配额"
                        }
                    
                    # 检查是否超出配额（只有当配额>0时才进行限制）
                    remaining = ppt_quota - ppt_used
                    has_quota = remaining > 0
                    
                    return {
                        "has_quota": has_quota,
                        "unlimited": False,
                        "quota": ppt_quota,
                        "used": ppt_used,
                        "remaining": remaining,
                        "message": "配额充足" if has_quota else "配额已耗尽，请联系管理员增加次数"
                    }
                    
        except Exception as e:
            logger.error(f"检查用户配额失败: {e}")
            return {
                "has_quota": False,
                "message": f"检查配额失败: {str(e)}"
            }
    
    async def use_quota(self, username: str, quota_type: str = "ppt_generation") -> bool:
        """
        使用配额（增加使用次数）
        
        Args:
            username: 用户名
            quota_type: 配额类型
            
        Returns:
            是否成功
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 获取当前配额信息
                    sql = """
                    SELECT quotas, use_quotas 
                    FROM member_report_users 
                    WHERE username = %s
                    """
                    await cursor.execute(sql, (username,))
                    user = await cursor.fetchone()
                    
                    if not user:
                        return False
                    
                    # 解析配额信息
                    ppt_quota = user['quotas'] if user['quotas'] else 0
                    ppt_used = user['use_quotas'] if user['use_quotas'] else 0
                    
                    # 获取配额限制（只支持PPT生成）
                    quota_limit = ppt_quota if quota_type == 'ppt_generation' else None
                    current_used = ppt_used if quota_type == 'ppt_generation' else 0
                    
                    # 如果有配额限制（且不是0=无限制），检查是否超出
                    if quota_limit is not None and quota_limit > 0 and current_used >= quota_limit:
                        logger.warning(f"用户 {username} 的 {quota_type} 配额已耗尽")
                        return False
                    
                    # 增加使用次数（只支持PPT生成）
                    if quota_type == 'ppt_generation':
                        new_used = current_used + 1
                        
                        # 更新数据库
                        sql = """
                        UPDATE member_report_users 
                        SET use_quotas = %s, updated_at = NOW()
                        WHERE username = %s
                        """
                        await cursor.execute(sql, (new_used, username))
                    else:
                        return False
                    await conn.commit()
                    
                    logger.info(f"用户 {username} 使用了1次 {quota_type} 配额，当前已使用: {new_used if quota_type == 'ppt_generation' else 0}")
                    return True
                    
        except Exception as e:
            logger.error(f"使用配额失败: {e}")
            return False
    
    async def set_user_quota(
        self, 
        username: str, 
        quota_type: str, 
        quota_limit: Optional[int]
    ) -> bool:
        """
        设置用户配额限制
        
        Args:
            username: 用户名
            quota_type: 配额类型
            quota_limit: 配额限制，None表示无限制
            
        Returns:
            是否成功
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 获取当前配额信息
                    sql = """
                    SELECT quotas 
                    FROM member_report_users 
                    WHERE username = %s
                    """
                    await cursor.execute(sql, (username,))
                    user = await cursor.fetchone()
                    
                    if not user:
                        return False
                    
                    # 设置配额（只支持PPT生成）
                    if quota_type == 'ppt_generation':
                        # 更新数据库
                        sql = """
                        UPDATE member_report_users 
                        SET quotas = %s, updated_at = NOW()
                        WHERE username = %s
                        """
                        await cursor.execute(sql, (quota_limit if quota_limit is not None else 0, username))
                    else:
                        logger.warning(f"不支持的配额类型: {quota_type}")
                        return False
                    await conn.commit()
                    
                    logger.info(f"设置用户 {username} 的 {quota_type} 配额为: {quota_limit}")
                    return True
                    
        except Exception as e:
            logger.error(f"设置配额失败: {e}")
            return False
    
    async def reset_user_quota(self, username: str, quota_type: str = "ppt_generation") -> bool:
        """
        重置用户配额使用次数
        
        Args:
            username: 用户名
            quota_type: 配额类型
            
        Returns:
            是否成功
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 获取当前使用配额信息
                    sql = """
                    SELECT use_quotas 
                    FROM member_report_users 
                    WHERE username = %s
                    """
                    await cursor.execute(sql, (username,))
                    user = await cursor.fetchone()
                    
                    if not user:
                        return False
                    
                    # 重置配额（只支持PPT生成）
                    if quota_type == 'ppt_generation':
                        # 更新数据库
                        sql = """
                        UPDATE member_report_users 
                        SET use_quotas = 0, updated_at = NOW()
                        WHERE username = %s
                        """
                        await cursor.execute(sql, (username,))
                    else:
                        logger.warning(f"不支持的配额类型: {quota_type}")
                        return False
                    await conn.commit()
                    
                    logger.info(f"重置用户 {username} 的 {quota_type} 使用次数")
                    return True
                    
        except Exception as e:
            logger.error(f"重置配额失败: {e}")
            return False
    
    async def get_all_users_quota_status(self) -> list:
        """
        获取所有用户的配额使用状态
        
        Returns:
            用户配额状态列表
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    sql = """
                    SELECT 
                        username,
                        real_name,
                        role,
                        quotas,
                        use_quotas,
                        updated_at
                    FROM member_report_users
                    WHERE status = 'active'
                    ORDER BY username
                    """
                    await cursor.execute(sql)
                    users = await cursor.fetchall()
                    
                    result = []
                    for user in users:
                        quotas = json.loads(user['quotas']) if user['quotas'] else {}
                        use_quotas = json.loads(user['use_quotas']) if user['use_quotas'] else {}
                        
                        ppt_quota = quotas.get('ppt_generation', None)
                        ppt_used = use_quotas.get('ppt_generation', 0)
                        
                        result.append({
                            "username": user['username'],
                            "real_name": user['real_name'],
                            "role": user['role'],
                            "ppt_quota": ppt_quota,
                            "ppt_used": ppt_used,
                            "ppt_remaining": (ppt_quota - ppt_used) if ppt_quota else None,
                            "unlimited": ppt_quota is None,
                            "last_updated": user['updated_at']
                        })
                    
                    return result
                    
        except Exception as e:
            logger.error(f"获取用户配额状态失败: {e}")
            return []