"""
BID申请审批服务
处理管理员对BID申请的审批操作
"""
import json
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
import aiomysql

logger = logging.getLogger(__name__)


class BidApprovalService:
    """BID申请审批服务"""
    
    def __init__(self, db_pool):
        self.db_pool = db_pool
    
    async def get_pending_applications(
        self,
        page: int = 1,
        page_size: int = 20,
        search: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取待审批的BID申请列表"""
        try:
            offset = (page - 1) * page_size
            where_conditions = ["status = 'pending'", "deleted_at IS NULL"]
            params = []
            
            if search:
                where_conditions.append("(bid LIKE %s OR bid_name LIKE %s OR company_name LIKE %s)")
                search_pattern = f"%{search}%"
                params.extend([search_pattern, search_pattern, search_pattern])
            
            where_clause = " AND ".join(where_conditions)
            
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 获取总数
                    count_sql = f"""
                    SELECT COUNT(*) as total 
                    FROM member_report_bid_configs
                    WHERE {where_clause}
                    """
                    await cursor.execute(count_sql, params)
                    total = (await cursor.fetchone())['total']
                    
                    # 获取列表
                    list_sql = f"""
                    SELECT 
                        id, bid, bid_name, description, owner_username,
                        status, apply_reason, sid_list,
                        contact_name, contact_phone, contact_email,
                        company_name, industry,
                        created_at, updated_at
                    FROM member_report_bid_configs
                    WHERE {where_clause}
                    ORDER BY created_at ASC
                    LIMIT %s OFFSET %s
                    """
                    params.extend([page_size, offset])
                    await cursor.execute(list_sql, params)
                    applications = await cursor.fetchall()
                    
                    # 处理JSON字段
                    for app in applications:
                        if app['sid_list']:
                            app['sid_list'] = json.loads(app['sid_list'])
                        else:
                            app['sid_list'] = []
                        
                        # 格式化时间
                        if app['created_at']:
                            app['created_at'] = app['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                        if app['updated_at']:
                            app['updated_at'] = app['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
                    
                    return {
                        'total': total,
                        'items': applications,
                        'page': page,
                        'page_size': page_size
                    }
                    
        except Exception as e:
            logger.error(f"获取待审批列表失败: {e}")
            return {
                'total': 0,
                'items': [],
                'page': page,
                'page_size': page_size
            }
    
    async def approve_application(
        self,
        bid: str,
        admin_username: str,
        auto_activate: bool = True,
        comment: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        批准BID申请
        
        Args:
            bid: BID代码
            admin_username: 审批人用户名
            auto_activate: 是否自动激活
            comment: 审批意见
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 获取申请信息
                    select_sql = """
                    SELECT id, bid, owner_username, status
                    FROM member_report_bid_configs
                    WHERE bid = %s AND deleted_at IS NULL
                    """
                    await cursor.execute(select_sql, (bid,))
                    config = await cursor.fetchone()
                    
                    if not config:
                        return {
                            'success': False,
                            'message': 'BID申请不存在'
                        }
                    
                    if config['status'] != 'pending':
                        return {
                            'success': False,
                            'message': '只能审批待审核的申请'
                        }
                    
                    # 更新申请状态
                    new_status = 'active' if auto_activate else 'approved'
                    update_sql = """
                    UPDATE member_report_bid_configs
                    SET status = %s,
                        approved_by = %s,
                        approved_at = NOW(),
                        updated_at = NOW()
                    WHERE bid = %s
                    """
                    await cursor.execute(update_sql, (new_status, admin_username, bid))
                    
                    # 如果自动激活，创建用户权限关联
                    if auto_activate and config['owner_username']:
                        # 检查权限是否已存在
                        check_perm_sql = """
                        SELECT id FROM member_report_user_bid_permissions
                        WHERE username = %s AND bid = %s
                        """
                        await cursor.execute(check_perm_sql, (config['owner_username'], bid))
                        existing_perm = await cursor.fetchone()
                        
                        if not existing_perm:
                            # 创建权限关联
                            insert_perm_sql = """
                            INSERT INTO member_report_user_bid_permissions (
                                username, bid, permissions, granted_by,
                                grant_reason, status, created_at, updated_at
                            ) VALUES (
                                %s, %s, %s, %s, %s, 'active', NOW(), NOW()
                            )
                            """
                            permissions = json.dumps(['read', 'write', 'generate'])
                            grant_reason = f"BID申请自动授权 - {comment or '申请通过'}"
                            
                            await cursor.execute(insert_perm_sql, (
                                config['owner_username'],
                                bid,
                                permissions,
                                admin_username,
                                grant_reason
                            ))
                    
                    
                    # 记录审计日志
                    audit_sql = """
                    INSERT INTO member_report_audit_logs (
                        username, action, resource, resource_id,
                        status, created_at
                    ) VALUES (%s, %s, %s, %s, %s, NOW())
                    """
                    await cursor.execute(audit_sql, (
                        admin_username,
                        'bid_approve',
                        'bid_config',
                        bid,
                        'success'
                    ))
                    
                    await conn.commit()
                    
                    return {
                        'success': True,
                        'message': f'BID申请已批准{"并激活" if auto_activate else ""}'
                    }
                    
        except Exception as e:
            logger.error(f"批准BID申请失败: {e}")
            return {
                'success': False,
                'message': f'批准失败: {str(e)}'
            }
    
    async def reject_application(
        self,
        bid: str,
        admin_username: str,
        reject_reason: str
    ) -> Dict[str, Any]:
        """
        拒绝BID申请
        
        Args:
            bid: BID代码
            admin_username: 审批人用户名
            reject_reason: 拒绝理由
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 获取申请信息
                    select_sql = """
                    SELECT id, status
                    FROM member_report_bid_configs
                    WHERE bid = %s AND deleted_at IS NULL
                    """
                    await cursor.execute(select_sql, (bid,))
                    config = await cursor.fetchone()
                    
                    if not config:
                        return {
                            'success': False,
                            'message': 'BID申请不存在'
                        }
                    
                    if config['status'] != 'pending':
                        return {
                            'success': False,
                            'message': '只能审批待审核的申请'
                        }
                    
                    # 更新申请状态
                    update_sql = """
                    UPDATE member_report_bid_configs
                    SET status = 'rejected',
                        reject_reason = %s,
                        approved_by = %s,
                        approved_at = NOW(),
                        updated_at = NOW()
                    WHERE bid = %s
                    """
                    await cursor.execute(update_sql, (reject_reason, admin_username, bid))
                    
                    
                    # 记录审计日志
                    audit_sql = """
                    INSERT INTO member_report_audit_logs (
                        username, action, resource, resource_id,
                        status, created_at
                    ) VALUES (%s, %s, %s, %s, %s, NOW())
                    """
                    await cursor.execute(audit_sql, (
                        admin_username,
                        'bid_reject',
                        'bid_config',
                        bid,
                        'success'
                    ))
                    
                    await conn.commit()
                    
                    return {
                        'success': True,
                        'message': 'BID申请已拒绝'
                    }
                    
        except Exception as e:
            logger.error(f"拒绝BID申请失败: {e}")
            return {
                'success': False,
                'message': f'拒绝失败: {str(e)}'
            }
    
    async def get_approval_history(
        self,
        page: int = 1,
        page_size: int = 20,
        status: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取审批历史"""
        try:
            offset = (page - 1) * page_size
            where_conditions = ["deleted_at IS NULL"]
            params = []
            
            if status and status != 'pending':
                where_conditions.append("status = %s")
                params.append(status)
            else:
                where_conditions.append("status IN ('approved', 'rejected', 'active', 'inactive')")
            
            where_clause = " AND ".join(where_conditions)
            
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 获取总数
                    count_sql = f"""
                    SELECT COUNT(*) as total 
                    FROM member_report_bid_configs
                    WHERE {where_clause}
                    """
                    await cursor.execute(count_sql, params)
                    total = (await cursor.fetchone())['total']
                    
                    # 获取列表
                    list_sql = f"""
                    SELECT 
                        id, bid, bid_name, description, owner_username,
                        status, apply_reason, reject_reason,
                        approved_by, approved_at, sid_list,
                        company_name, industry,
                        created_at, updated_at
                    FROM member_report_bid_configs
                    WHERE {where_clause}
                    ORDER BY approved_at DESC
                    LIMIT %s OFFSET %s
                    """
                    params.extend([page_size, offset])
                    await cursor.execute(list_sql, params)
                    history = await cursor.fetchall()
                    
                    # 处理JSON字段和时间格式
                    for item in history:
                        if item['sid_list']:
                            item['sid_list'] = json.loads(item['sid_list'])
                        else:
                            item['sid_list'] = []
                        
                        # 格式化时间
                        for field in ['created_at', 'updated_at', 'approved_at']:
                            if item.get(field):
                                item[field] = item[field].strftime('%Y-%m-%d %H:%M:%S')
                    
                    return {
                        'total': total,
                        'items': history,
                        'page': page,
                        'page_size': page_size
                    }
                    
        except Exception as e:
            logger.error(f"获取审批历史失败: {e}")
            return {
                'total': 0,
                'items': [],
                'page': page,
                'page_size': page_size
            }