<template>
  <div id="app">
    <!-- 只在非登录页面显示头部 -->
    <header class="app-header" v-if="!isLoginPage">
      <div class="header-content">
        <h1 class="app-title">
          <el-icon><TrendCharts /></el-icon>
          会员数据查询系统
        </h1>
        <div class="header-info">
          <span class="current-time">{{ currentTime }}</span>
          <el-dropdown trigger="click" @command="handleUserCommand">
            <div class="user-avatar">
              <el-avatar :size="32" :src="userAvatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <span class="user-name">{{ userName }}</span>
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item disabled>
                  <div class="user-info">
                    <div>用户名：{{ userName }}</div>
                    <div class="quota-info">配额：{{ userQuota }}</div>
                  </div>
                </el-dropdown-item>
                <el-dropdown-item divided command="profile">
                  <el-icon><User /></el-icon>
                  个人信息
                </el-dropdown-item>
                <el-dropdown-item command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </header>

    <!-- 导航菜单 - 只在非登录页面显示 -->
    <nav class="app-nav" v-if="!isLoginPage">
      <div class="nav-content">
        <el-menu
          :default-active="activeMenu"
          mode="horizontal"
          @select="handleMenuSelect"
          class="nav-menu"
        >
          <el-menu-item index="/query">
            <el-icon><Search /></el-icon>
            <span>数据查询</span>
          </el-menu-item>
          <el-menu-item index="/ppt-report">
            <el-icon><Document /></el-icon>
            <span>PPT报告生成</span>
          </el-menu-item>
          <el-menu-item index="/setting/bid-application">
            <el-icon><Setting /></el-icon>
            <span>BID申请</span>
          </el-menu-item>
        </el-menu>
      </div>
    </nav>
    
    <main :class="['app-main', { 'app-main--login': isLoginPage }]">
      <router-view />
    </main>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { TrendCharts, Search, Document, User, SwitchButton, ArrowDown, Setting } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'App',
  setup() {
    const currentTime = ref('')
    const router = useRouter()
    const route = useRoute()
    const authStore = useAuthStore()
    let timer = null

    // 判断是否为登录页面
    const isLoginPage = computed(() => route.path === '/login')

    // 当前激活的菜单项
    const activeMenu = computed(() => route.path)

    // 用户信息
    const userName = computed(() => {
      const user = authStore.user
      return user?.username || '未登录'
    })

    const userAvatar = computed(() => {
      const user = authStore.user
      return user?.avatar_url || ''
    })

    const userQuota = computed(() => {
      const user = authStore.user
      if (!user) return '未知'
      
      // quotas和use_quotas现在是整数，不是JSON对象
      const pptQuota = user.quotas || 0  // PPT生成配额限制（0表示无限制）
      const pptUsed = user.use_quotas || 0  // 已使用的PPT生成次数
      
      if (!pptQuota || pptQuota === 0) {
        // 配额为0表示无限制
        return `已使用 ${pptUsed} 次（无限制）`
      }
      
      // 显示已使用/总配额
      const remaining = Math.max(0, pptQuota - pptUsed)
      return `${pptUsed} / ${pptQuota} 次（剩余 ${remaining} 次）`
    })

    // 更新时间
    const updateTime = () => {
      const now = new Date()
      currentTime.value = now.toLocaleString('zh-CN')
    }

    // 处理菜单选择
    const handleMenuSelect = (index) => {
      if (index !== route.path) {
        router.push(index)
      }
    }

    // 处理用户菜单命令
    const handleUserCommand = async (command) => {
      switch (command) {
        case 'profile':
          ElMessage.info('个人信息功能开发中')
          break
        case 'logout':
          try {
            await ElMessageBox.confirm(
              '确定要退出登录吗？',
              '提示',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }
            )
            
            // 清除认证信息
            authStore.logout()
            
            // 跳转到登录页
            router.push('/login')
            
            ElMessage.success('已退出登录')
          } catch {
            // 用户取消
          }
          break
      }
    }

    onMounted(() => {
      // 初始化认证状态
      authStore.initializeAuth()
      
      updateTime()
      timer = setInterval(updateTime, 1000)
    })

    onUnmounted(() => {
      if (timer) {
        clearInterval(timer)
      }
    })

    return {
      currentTime,
      isLoginPage,
      activeMenu,
      userName,
      userAvatar,
      userQuota,
      handleMenuSelect,
      handleUserCommand,
      TrendCharts,
      Search,
      Document,
      User,
      SwitchButton,
      ArrowDown,
      Setting
    }
  }
}
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Helvetica Neue', Arial, sans-serif;
  background: #f5f7fa;
  color: #333;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: auto;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.app-title {
  font-size: 24px;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.current-time {
  font-size: 14px;
  opacity: 0.9;
}

/* 用户头像样式 */
.user-avatar {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 20px;
  transition: background-color 0.3s;
}

.user-avatar:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.user-name {
  font-size: 14px;
  color: white;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-info {
  padding: 5px 0;
  font-size: 12px;
  color: #606266;
}

.quota-info {
  margin-top: 5px;
  color: #909399;
}

/* 导航菜单样式 */
.app-nav {
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.nav-menu {
  border-bottom: none;
}

.nav-menu .el-menu-item {
  height: 50px;
  line-height: 50px;
  font-size: 16px;
  color: #303133 !important;
  border-bottom: 2px solid transparent;
  font-weight: 500;
}

.nav-menu .el-menu-item:hover {
  background-color: #f5f7fa;
  color: #303133 !important;
}

.nav-menu .el-menu-item.is-active {
  color: #303133 !important;
  border-bottom-color: #409eff;
  background-color: #ecf5ff;
}

.nav-menu .el-menu-item span {
  color: #303133 !important;
}

.nav-menu .el-menu-item:hover span {
  color: #303133 !important;
}

.nav-menu .el-menu-item.is-active span {
  color: #303133 !important;
}

.nav-menu .el-menu-item .el-icon {
  margin-right: 8px;
}

.app-main {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  padding: 20px;
  overflow: visible;
  min-height: calc(100vh - 120px); /* 调整高度，考虑导航菜单 */
}

/* 登录页面的主内容区域样式 */
.app-main--login {
  max-width: 100%;
  padding: 0;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    height: auto;
    padding: 15px 0;
  }

  .app-title {
    font-size: 20px;
    margin-bottom: 10px;
  }

  .nav-content {
    padding: 0 10px;
  }

  .nav-menu .el-menu-item {
    font-size: 14px;
    height: 45px;
    line-height: 45px;
  }

  .app-main {
    padding: 10px;
    min-height: calc(100vh - 140px); /* 移动端调整 */
  }
}
</style>
