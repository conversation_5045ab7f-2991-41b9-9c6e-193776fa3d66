"""
企业微信相关查询功能模块

本模块提供企业微信相关的数据库查询功能，主要用于处理企业微信和微生活系统之间的数据映射关系。
"""

import logging
from typing import Optional, Dict, Any
from datetime import datetime
from core.database import db

logger = logging.getLogger(__name__)


def convert_date_to_timestamp(date_str: str, is_end_date: bool = False) -> int:
    """
    将YYYYMMDD格式的日期字符串转换为时间戳

    功能说明：
    - 前端传入的时间格式为YYYYMMDD字符串格式（如：20240101）
    - 企业微信数据库中存储的时间为时间戳格式
    - 需要进行格式转换以匹配数据库查询条件

    参数说明：
    - date_str (str): YYYYMMDD格式的日期字符串
    - is_end_date (bool): 是否为结束日期，如果是则设置为当天23:59:59

    返回值说明：
    - int: 对应的时间戳

    使用示例：
    start_timestamp = convert_date_to_timestamp("20240101")  # 2024-01-01 00:00:00
    end_timestamp = convert_date_to_timestamp("20240131", True)  # 2024-01-31 23:59:59
    """
    try:
        # 解析YYYYMMDD格式的日期
        date_obj = datetime.strptime(date_str, "%Y%m%d")

        if is_end_date:
            # 如果是结束日期，设置为当天的23:59:59
            date_obj = date_obj.replace(hour=23, minute=59, second=59)
        else:
            # 如果是开始日期，设置为当天的00:00:00
            date_obj = date_obj.replace(hour=0, minute=0, second=0)

        # 转换为时间戳
        timestamp = int(date_obj.timestamp())

        logger.debug(f"日期转换: {date_str} -> {date_obj} -> {timestamp}")
        return timestamp

    except Exception as e:
        logger.error(f"日期转换失败: {date_str}, 错误信息: {str(e)}")
        raise ValueError(f"无效的日期格式: {date_str}，期望格式为YYYYMMDD")


async def get_merchant_id_by_boss_id(bid: str) -> Optional[Dict[str, Any]]:
    """
    根据boss_id获取企业微信中使用的商户标识符

    功能说明：
    - 该函数用于查询backend数据库中的fsl_merchants表
    - 根据前端传递的bid参数（对应数据库中的boss_id字段）查询对应的记录
    - 返回该记录的id字段，这个id是企业微信中使用的标识符

    参数说明：
    - bid (str): 前端传递的业务标识符，对应数据库表中的boss_id字段

    返回值说明：
    - Optional[Dict[str, Any]]: 查询结果字典，包含id等字段信息
      - 如果查询成功：返回包含id字段的字典
      - 如果未找到记录：返回None
      - 如果查询异常：返回None并记录错误日志

    使用示例：
    result = await get_merchant_id_by_boss_id("12345")
    if result:
        merchant_id = result['id']  # 获取企业微信中使用的标识符
        print(f"企业微信商户ID: {merchant_id}")
    else:
        print("未找到对应的商户记录")
    """
    try:
        # 记录查询开始日志
        logger.info(f"开始查询商户信息，boss_id: {bid}")

        # 构建SQL查询语句
        # 查询backend数据库中fsl_merchants表，根据boss_id获取对应的id字段
        # 根据实际表结构使用正确的字段名
        sql = """
        SELECT
            id,
            boss_id,
            merchant_name,
            create_time,
            update_time
        FROM fsl_merchants
        WHERE boss_id = %s
        LIMIT 1
        """

        # 执行数据库查询（使用backend数据库）
        result = await db.execute_backend_one(sql, (bid,))

        if result:
            logger.info(f"查询成功，找到商户记录: boss_id={bid}, merchant_id={result['id']}")
            return result
        else:
            logger.warning(f"未找到对应的商户记录: boss_id={bid}")
            return None

    except Exception as e:
        # 记录异常信息
        logger.error(f"查询商户信息时发生异常: boss_id={bid}, 错误信息: {str(e)}")
        logger.error(f"异常详情:", exc_info=True)
        return None


async def get_wechat_new_customers_count(bid: str, start_date: str, end_date: str) -> Optional[int]:
    """
    获取企业微信新增客户数

    功能说明：
    - 查询指定时间范围内的新增客户数量
    - 统计fsl_contact_follow表中不重复的contact_id数量
    - 自动将YYYYMMDD格式的日期转换为时间戳

    参数说明：
    - bid (str): 前端传递的业务标识符，对应数据库中的boss_id
    - start_date (str): 开始日期，YYYYMMDD格式（如：20240101）
    - end_date (str): 结束日期，YYYYMMDD格式（如：20240131）

    返回值说明：
    - Optional[int]: 新增客户数量，查询失败时返回None
    """
    try:
        # 先获取merchant_id
        merchant_mapping = await get_merchant_id_by_boss_id(bid)
        if not merchant_mapping:
            logger.warning(f"未找到商户映射关系: bid={bid}")
            return None

        # 转换日期格式为时间戳
        start_timestamp = convert_date_to_timestamp(start_date, False)
        end_timestamp = convert_date_to_timestamp(end_date, True)

        merchant_id = merchant_mapping['id']
        logger.info(f"开始查询企微新增客户数: bid={bid}, merchant_id={merchant_id}, 日期范围: {start_date}-{end_date}, 时间戳范围: {start_timestamp}-{end_timestamp}")

        sql = """
        SELECT COUNT(DISTINCT contact_id) as cnt
        FROM fsl_contact_follow
        WHERE merchant_id = %s
        AND bind_time BETWEEN %s AND %s
        """

        result = await db.execute_basic_info_one(sql, (merchant_id, start_timestamp, end_timestamp))

        if result:
            count = result['cnt']
            logger.info(f"企微新增客户数查询成功: bid={bid}, count={count}")
            return count
        else:
            logger.warning(f"企微新增客户数查询无结果: bid={bid}")
            return 0

    except Exception as e:
        logger.error(f"查询企微新增客户数时发生异常: bid={bid}, 错误信息: {str(e)}")
        logger.error(f"异常详情:", exc_info=True)
        return None


async def get_wechat_external_send_count(bid: str, start_date: str, end_date: str) -> Optional[int]:
    """
    获取企微群发次数

    功能说明：
    - 查询指定时间范围内的企业微信群发消息次数
    - 统计fsl_external_send表中的记录总数
    - 自动将YYYYMMDD格式的日期转换为时间戳

    参数说明：
    - bid (str): 前端传递的业务标识符，对应数据库中的boss_id
    - start_date (str): 开始日期，YYYYMMDD格式（如：20240101）
    - end_date (str): 结束日期，YYYYMMDD格式（如：20240131）

    返回值说明：
    - Optional[int]: 群发次数，查询失败时返回None
    """
    try:
        # 先获取merchant_id
        merchant_mapping = await get_merchant_id_by_boss_id(bid)
        if not merchant_mapping:
            logger.warning(f"未找到商户映射关系: bid={bid}")
            return None

        # 转换日期格式为时间戳
        start_timestamp = convert_date_to_timestamp(start_date, False)
        end_timestamp = convert_date_to_timestamp(end_date, True)

        merchant_id = merchant_mapping['id']
        logger.info(f"开始查询企微群发次数: bid={bid}, merchant_id={merchant_id}, 日期范围: {start_date}-{end_date}")

        sql = """
        SELECT COUNT(*) as cnt
        FROM fsl_external_send
        WHERE merchant_id = %s
        AND create_time BETWEEN %s AND %s
        """

        result = await db.execute_basic_info_one(sql, (merchant_id, start_timestamp, end_timestamp))

        if result:
            count = result['cnt']
            logger.info(f"企微群发次数查询成功: bid={bid}, count={count}")
            return count
        else:
            logger.warning(f"企微群发次数查询无结果: bid={bid}")
            return 0

    except Exception as e:
        logger.error(f"查询企微群发次数时发生异常: bid={bid}, 错误信息: {str(e)}")
        logger.error(f"异常详情:", exc_info=True)
        return None


async def get_wechat_moment_total_count(bid: str, start_date: str, end_date: str) -> Optional[int]:
    """
    获取企微朋友圈发布总数量

    功能说明：
    - 查询指定时间范围内的企业微信朋友圈发布总数量
    - 统计fsl_send_moment表中的记录总数
    - 自动将YYYYMMDD格式的日期转换为时间戳

    参数说明：
    - bid (str): 前端传递的业务标识符，对应数据库中的boss_id
    - start_date (str): 开始日期，YYYYMMDD格式（如：20240101）
    - end_date (str): 结束日期，YYYYMMDD格式（如：20240131）

    返回值说明：
    - Optional[int]: 朋友圈发布总数量，查询失败时返回None
    """
    try:
        # 先获取merchant_id
        merchant_mapping = await get_merchant_id_by_boss_id(bid)
        if not merchant_mapping:
            logger.warning(f"未找到商户映射关系: bid={bid}")
            return None

        # 转换日期格式为时间戳
        start_timestamp = convert_date_to_timestamp(start_date, False)
        end_timestamp = convert_date_to_timestamp(end_date, True)

        merchant_id = merchant_mapping['id']
        logger.info(f"开始查询企微朋友圈总数量: bid={bid}, merchant_id={merchant_id}, 日期范围: {start_date}-{end_date}")

        sql = """
        SELECT COUNT(*) as cnt
        FROM fsl_send_moment
        WHERE merchant_id = %s
        AND create_time BETWEEN %s AND %s
        """

        result = await db.execute_basic_info_one(sql, (merchant_id, start_timestamp, end_timestamp))

        if result:
            count = result['cnt']
            logger.info(f"企微朋友圈总数量查询成功: bid={bid}, count={count}")
            return count
        else:
            logger.warning(f"企微朋友圈总数量查询无结果: bid={bid}")
            return 0

    except Exception as e:
        logger.error(f"查询企微朋友圈总数量时发生异常: bid={bid}, 错误信息: {str(e)}")
        logger.error(f"异常详情:", exc_info=True)
        return None


async def get_wechat_moment_like_count(bid: str, start_date: str, end_date: str) -> Optional[int]:
    """
    获取企微朋友圈点赞总数

    功能说明：
    - 查询指定时间范围内的企业微信朋友圈点赞总数
    - 统计fsl_send_moment表中的点赞数量
    - 自动将YYYYMMDD格式的日期转换为时间戳

    参数说明：
    - bid (str): 前端传递的业务标识符，对应数据库中的boss_id
    - start_date (str): 开始日期，YYYYMMDD格式（如：20240101）
    - end_date (str): 结束日期，YYYYMMDD格式（如：20240131）

    返回值说明：
    - Optional[int]: 朋友圈点赞总数，查询失败时返回None
    """
    try:
        # 先获取merchant_id
        merchant_mapping = await get_merchant_id_by_boss_id(bid)
        if not merchant_mapping:
            logger.warning(f"未找到商户映射关系: bid={bid}")
            return None

        # 转换日期格式为时间戳
        start_timestamp = convert_date_to_timestamp(start_date, False)
        end_timestamp = convert_date_to_timestamp(end_date, True)

        merchant_id = merchant_mapping['id']
        logger.info(f"开始查询企微朋友圈点赞数: bid={bid}, merchant_id={merchant_id}, 日期范围: {start_date}-{end_date}")

        sql = """
        SELECT SUM(IF(like_list='', 0, JSON_LENGTH(like_list))) as total_like
        FROM fsl_send_moment
        WHERE merchant_id = %s
        AND create_time BETWEEN %s AND %s
        """

        result = await db.execute_basic_info_one(sql, (merchant_id, start_timestamp, end_timestamp))

        if result:
            count = result['total_like'] or 0
            logger.info(f"企微朋友圈点赞数查询成功: bid={bid}, count={count}")
            return count
        else:
            logger.warning(f"企微朋友圈点赞数查询无结果: bid={bid}")
            return 0

    except Exception as e:
        logger.error(f"查询企微朋友圈点赞数时发生异常: bid={bid}, 错误信息: {str(e)}")
        logger.error(f"异常详情:", exc_info=True)
        return None


async def get_wechat_moment_comment_count(bid: str, start_date: str, end_date: str) -> Optional[int]:
    """
    获取企微朋友圈评论总数

    功能说明：
    - 查询指定时间范围内的企业微信朋友圈评论总数
    - 统计fsl_send_moment表中的评论数量
    - 自动将YYYYMMDD格式的日期转换为时间戳

    参数说明：
    - bid (str): 前端传递的业务标识符，对应数据库中的boss_id
    - start_date (str): 开始日期，YYYYMMDD格式（如：20240101）
    - end_date (str): 结束日期，YYYYMMDD格式（如：20240131）

    返回值说明：
    - Optional[int]: 朋友圈评论总数，查询失败时返回None
    """
    try:
        # 先获取merchant_id
        merchant_mapping = await get_merchant_id_by_boss_id(bid)
        if not merchant_mapping:
            logger.warning(f"未找到商户映射关系: bid={bid}")
            return None

        # 转换日期格式为时间戳
        start_timestamp = convert_date_to_timestamp(start_date, False)
        end_timestamp = convert_date_to_timestamp(end_date, True)

        merchant_id = merchant_mapping['id']
        logger.info(f"开始查询企微朋友圈评论数: bid={bid}, merchant_id={merchant_id}, 日期范围: {start_date}-{end_date}")

        sql = """
        SELECT SUM(IF(comment_list='', 0, JSON_LENGTH(comment_list))) as total_comment
        FROM fsl_send_moment
        WHERE merchant_id = %s
        AND create_time BETWEEN %s AND %s
        """

        result = await db.execute_basic_info_one(sql, (merchant_id, start_timestamp, end_timestamp))

        if result:
            count = result['total_comment'] or 0
            logger.info(f"企微朋友圈评论数查询成功: bid={bid}, count={count}")
            return count
        else:
            logger.warning(f"企微朋友圈评论数查询无结果: bid={bid}")
            return 0

    except Exception as e:
        logger.error(f"查询企微朋友圈评论数时发生异常: bid={bid}, 错误信息: {str(e)}")
        logger.error(f"异常详情:", exc_info=True)
        return None


async def get_wechat_moment_look_count(bid: str, start_date: str, end_date: str) -> Optional[int]:
    """
    获取企微朋友圈查看总数

    功能说明：
    - 查询指定时间范围内的企业微信朋友圈查看总数
    - 统计fsl_send_moment表中的查看数量
    - 自动将YYYYMMDD格式的日期转换为时间戳

    参数说明：
    - bid (str): 前端传递的业务标识符，对应数据库中的boss_id
    - start_date (str): 开始日期，YYYYMMDD格式（如：20240101）
    - end_date (str): 结束日期，YYYYMMDD格式（如：20240131）

    返回值说明：
    - Optional[int]: 朋友圈查看总数，查询失败时返回None
    """
    try:
        # 先获取merchant_id
        merchant_mapping = await get_merchant_id_by_boss_id(bid)
        if not merchant_mapping:
            logger.warning(f"未找到商户映射关系: bid={bid}")
            return None

        # 转换日期格式为时间戳
        start_timestamp = convert_date_to_timestamp(start_date, False)
        end_timestamp = convert_date_to_timestamp(end_date, True)

        merchant_id = merchant_mapping['id']
        logger.info(f"开始查询企微朋友圈查看数: bid={bid}, merchant_id={merchant_id}, 日期范围: {start_date}-{end_date}")

        sql = """
        SELECT SUM(IF(like_list='', 0, JSON_LENGTH(look_list))) as total_look
        FROM fsl_send_moment
        WHERE merchant_id = %s
        AND create_time BETWEEN %s AND %s
        """

        result = await db.execute_basic_info_one(sql, (merchant_id, start_timestamp, end_timestamp))

        if result:
            count = result['total_look'] or 0
            logger.info(f"企微朋友圈查看数查询成功: bid={bid}, count={count}")
            return count
        else:
            logger.warning(f"企微朋友圈查看数查询无结果: bid={bid}")
            return 0

    except Exception as e:
        logger.error(f"查询企微朋友圈查看数时发生异常: bid={bid}, 错误信息: {str(e)}")
        logger.error(f"异常详情:", exc_info=True)
        return None


async def get_wechat_customer_follow_count(bid: str, start_date: str, end_date: str) -> Optional[int]:
    """
    获取企微客户关注总量

    功能说明：
    - 查询指定时间范围内的客户关注总量
    - 统计fsl_contact_follow表中的记录总数
    - 自动将YYYYMMDD格式的日期转换为时间戳

    参数说明：
    - bid (str): 前端传递的业务标识符，对应数据库中的boss_id
    - start_date (str): 开始日期，YYYYMMDD格式（如：20240101）
    - end_date (str): 结束日期，YYYYMMDD格式（如：20240131）

    返回值说明：
    - Optional[int]: 客户关注总量，查询失败时返回None
    """
    try:
        # 先获取merchant_id
        merchant_mapping = await get_merchant_id_by_boss_id(bid)
        if not merchant_mapping:
            logger.warning(f"未找到商户映射关系: bid={bid}")
            return None

        # 转换日期格式为时间戳
        start_timestamp = convert_date_to_timestamp(start_date, False)
        end_timestamp = convert_date_to_timestamp(end_date, True)

        merchant_id = merchant_mapping['id']
        logger.info(f"开始查询企微客户关注总量: bid={bid}, merchant_id={merchant_id}, 日期范围: {start_date}-{end_date}")

        sql = """
        SELECT COUNT(*) as cnt
        FROM fsl_contact_follow
        WHERE merchant_id = %s
        AND bind_time BETWEEN %s AND %s
        """

        result = await db.execute_basic_info_one(sql, (merchant_id, start_timestamp, end_timestamp))

        if result:
            count = result['cnt']
            logger.info(f"企微客户关注总量查询成功: bid={bid}, count={count}")
            return count
        else:
            logger.warning(f"企微客户关注总量查询无结果: bid={bid}")
            return 0

    except Exception as e:
        logger.error(f"查询企微客户关注总量时发生异常: bid={bid}, 错误信息: {str(e)}")
        logger.error(f"异常详情:", exc_info=True)
        return None


async def get_wechat_customer_delete_count(bid: str, start_date: str, end_date: str) -> Optional[int]:
    """
    获取企微客户删除好友数

    功能说明：
    - 查询指定时间范围内的客户删除好友数量
    - 统计fsl_staff_delete_log表中的记录总数
    - 自动将YYYYMMDD格式的日期转换为时间戳

    参数说明：
    - bid (str): 前端传递的业务标识符，对应数据库中的boss_id
    - start_date (str): 开始日期，YYYYMMDD格式（如：20240101）
    - end_date (str): 结束日期，YYYYMMDD格式（如：20240131）

    返回值说明：
    - Optional[int]: 客户删除好友数量，查询失败时返回None
    """
    try:
        # 先获取merchant_id
        merchant_mapping = await get_merchant_id_by_boss_id(bid)
        if not merchant_mapping:
            logger.warning(f"未找到商户映射关系: bid={bid}")
            return None

        # 转换日期格式为时间戳
        start_timestamp = convert_date_to_timestamp(start_date, False)
        end_timestamp = convert_date_to_timestamp(end_date, True)

        merchant_id = merchant_mapping['id']
        logger.info(f"开始查询企微客户删除好友数: bid={bid}, merchant_id={merchant_id}, 日期范围: {start_date}-{end_date}")

        sql = """
        SELECT COUNT(*) as cnt
        FROM fsl_staff_delete_log
        WHERE merchant_id = %s
        AND create_time BETWEEN %s AND %s
        """

        result = await db.execute_basic_info_one(sql, (merchant_id, start_timestamp, end_timestamp))

        if result:
            count = result['cnt']
            logger.info(f"企微客户删除好友数查询成功: bid={bid}, count={count}")
            return count
        else:
            logger.warning(f"企微客户删除好友数查询无结果: bid={bid}")
            return 0

    except Exception as e:
        logger.error(f"查询企微客户删除好友数时发生异常: bid={bid}, 错误信息: {str(e)}")
        logger.error(f"异常详情:", exc_info=True)
        return None


