/**
 * 认证相关API
 */
import axios from 'axios'

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:8000/api',
  timeout: 30000
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    if (error.response) {
      // 处理401未授权
      if (error.response.status === 401) {
        // 清除本地存储的认证信息
        localStorage.removeItem('access_token')
        localStorage.removeItem('refresh_token')
        localStorage.removeItem('user_info')
        
        // 跳转到登录页
        if (window.location.pathname !== '/login') {
          window.location.href = '/login'
        }
      }
    }
    return Promise.reject(error)
  }
)

/**
 * 认证API
 */
export const authAPI = {
  /**
   * 用户登录
   * @param {Object} data - 登录数据
   * @param {string} data.username - 用户名或邮箱
   * @param {string} data.password - 密码
   * @param {boolean} data.rememberMe - 记住我
   */
  login(data) {
    return request.post('/auth/login', {
      username: data.username,
      password: data.password,
      remember_me: data.rememberMe || false
    })
  },

  /**
   * 用户登出
   * @param {boolean} allDevices - 是否登出所有设备
   */
  logout(allDevices = false) {
    return request.post('/auth/logout', {
      all_devices: allDevices
    })
  },

  /**
   * 刷新Token
   * @param {string} refreshToken - 刷新Token
   */
  refreshToken(refreshToken) {
    return request.post('/auth/refresh', {
      refresh_token: refreshToken
    })
  },

  /**
   * 获取当前用户信息
   */
  getCurrentUser() {
    return request.get('/auth/me')
  },

  /**
   * 检查是否存在超级管理员
   */
  checkSuperAdmin() {
    return request.get('/auth/check-super-admin')
  },

  /**
   * 初始化超级管理员
   * @param {Object} data - 初始化数据
   * @param {string} data.username - 用户名
   * @param {string} data.password - 密码
   * @param {string} data.email - 邮箱
   * @param {string} data.real_name - 真实姓名
   * @param {string} data.init_key - 初始化密钥（可选）
   */
  initSuperAdmin(data) {
    return request.post('/auth/init-super-admin', data)
  },

  /**
   * 验证Token有效性
   */
  verifyToken() {
    return request.get('/auth/verify')
  }
}

// 同时导出为 authApi，以保持向后兼容
export const authApi = authAPI

// 导出request实例，供其他API模块使用
export default request