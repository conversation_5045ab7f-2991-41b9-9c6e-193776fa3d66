"""
认证数据库初始化模块
负责创建和初始化认证相关的数据库表
"""
import os
import logging
from pathlib import Path
from typing import Optional
import aiomysql

logger = logging.getLogger(__name__)

class AuthDatabaseInitializer:
    """认证数据库初始化器"""
    
    def __init__(self, db_connection_pool):
        """
        初始化数据库初始化器
        
        Args:
            db_connection_pool: task_management数据库连接池
        """
        self.db_pool = db_connection_pool
        self.schema_file = Path(__file__).parent / "complete_schema.sql"
        
    async def init_database(self) -> bool:
        """
        初始化认证数据库
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            logger.info("开始初始化认证数据库...")
            
            # 读取SQL脚本
            if not self.schema_file.exists():
                logger.error(f"数据库schema文件不存在: {self.schema_file}")
                return False
                
            with open(self.schema_file, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # 分割SQL语句（按分号分割，但要考虑存储过程等特殊情况）
            sql_statements = self._split_sql_statements(sql_content)
            
            # 执行SQL语句
            async with self.db_pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    for sql in sql_statements:
                        sql = sql.strip()
                        if sql and not sql.startswith('--'):  # 跳过空语句和注释
                            try:
                                await cursor.execute(sql)
                                logger.debug(f"执行SQL成功: {sql[:100]}...")
                            except aiomysql.Error as e:
                                # 忽略表已存在的错误
                                if "already exists" in str(e):
                                    logger.debug(f"表已存在，跳过: {sql[:50]}...")
                                else:
                                    logger.error(f"执行SQL失败: {e}")
                                    logger.error(f"失败的SQL: {sql[:200]}...")
                                    raise
                    
                    # 提交事务
                    await conn.commit()
                    
            logger.info("认证数据库初始化成功")
            
            # 检查表是否创建成功
            await self._verify_tables_created()
            
            return True
            
        except Exception as e:
            logger.error(f"认证数据库初始化失败: {e}")
            return False
    
    def _split_sql_statements(self, sql_content: str) -> list:
        """
        分割SQL语句
        
        Args:
            sql_content: SQL脚本内容
            
        Returns:
            list: SQL语句列表
        """
        # 移除注释行
        lines = sql_content.split('\n')
        cleaned_lines = []
        in_comment = False
        
        for line in lines:
            line = line.strip()
            
            # 处理多行注释
            if '/*' in line:
                in_comment = True
            if '*/' in line:
                in_comment = False
                continue
                
            # 跳过注释行和空行
            if not in_comment and line and not line.startswith('--'):
                cleaned_lines.append(line)
        
        # 重新组合并按分号分割
        cleaned_sql = ' '.join(cleaned_lines)
        statements = cleaned_sql.split(';')
        
        # 过滤空语句
        return [stmt.strip() for stmt in statements if stmt.strip()]
    
    async def _verify_tables_created(self) -> bool:
        """
        验证表是否创建成功
        
        Returns:
            bool: 验证是否成功
        """
        required_tables = [
            'member_report_users',
            'member_report_sessions',
            'member_report_audit_logs',
            'member_report_bid_configs',
            'member_report_user_bid_permissions'
        ]
        
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    # 获取当前数据库名
                    await cursor.execute("SELECT DATABASE()")
                    db_name = (await cursor.fetchone())[0]
                    
                    # 检查每个表是否存在
                    for table in required_tables:
                        await cursor.execute(
                            """
                            SELECT COUNT(*) 
                            FROM information_schema.tables 
                            WHERE table_schema = %s AND table_name = %s
                            """,
                            (db_name, table)
                        )
                        count = (await cursor.fetchone())[0]
                        
                        if count > 0:
                            logger.info(f"✓ 表 {table} 已创建")
                        else:
                            logger.warning(f"✗ 表 {table} 未创建")
                            
            return True
            
        except Exception as e:
            logger.error(f"验证表创建失败: {e}")
            return False
    
    async def check_super_admin_exists(self) -> bool:
        """
        检查是否存在超级管理员
        
        Returns:
            bool: 是否存在超级管理员
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """
                        SELECT COUNT(*) 
                        FROM member_report_users 
                        WHERE role = 'super_admin'
                        """
                    )
                    count = (await cursor.fetchone())[0]
                    return count > 0
                    
        except Exception as e:
            logger.error(f"检查超级管理员失败: {e}")
            return False
    
    async def get_table_info(self) -> dict:
        """
        获取表信息
        
        Returns:
            dict: 表信息字典
        """
        info = {}
        
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 获取用户表统计
                    await cursor.execute(
                        """
                        SELECT 
                            COUNT(*) as total_users,
                            SUM(CASE WHEN role = 'super_admin' THEN 1 ELSE 0 END) as super_admins,
                            SUM(CASE WHEN role = 'admin' THEN 1 ELSE 0 END) as admins,
                            SUM(CASE WHEN role = 'user' THEN 1 ELSE 0 END) as users,
                            SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users
                        FROM member_report_users
                        """
                    )
                    info['users'] = await cursor.fetchone()
                    
                    # 获取会话表统计
                    await cursor.execute(
                        """
                        SELECT 
                            COUNT(*) as total_sessions,
                            SUM(CASE WHEN is_active = 1 AND expires_at > NOW() THEN 1 ELSE 0 END) as active_sessions
                        FROM member_report_sessions
                        """
                    )
                    info['sessions'] = await cursor.fetchone()
                    
        except Exception as e:
            logger.error(f"获取表信息失败: {e}")
            
        return info


async def init_auth_database(db):
    """
    初始化认证数据库的便捷函数
    
    Args:
        db: 数据库实例
        
    Returns:
        bool: 是否初始化成功
    """
    try:
        # 使用task_management数据库连接池（ai_yuce数据库）
        initializer = AuthDatabaseInitializer(db.task_management_pool)
        
        # 初始化数据库
        success = await initializer.init_database()
        
        if success:
            # 检查是否需要初始化超级管理员
            has_super_admin = await initializer.check_super_admin_exists()
            
            if not has_super_admin:
                logger.warning("未检测到超级管理员账户，请访问 /api/auth/init-super-admin 进行初始化")
            else:
                logger.info("超级管理员账户已存在")
                
            # 获取并显示表统计信息
            table_info = await initializer.get_table_info()
            if table_info:
                logger.info(f"数据库统计信息: {table_info}")
                
        return success
        
    except Exception as e:
        logger.error(f"初始化认证数据库失败: {e}")
        return False