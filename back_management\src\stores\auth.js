import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authAPI } from '@/api/auth'
import { ElMessage } from 'element-plus'
import router from '@/router.js'

export const useAuthStore = defineStore('auth', () => {
  const user = ref(null)
  const accessToken = ref(localStorage.getItem('admin_access_token') || '')
  const refreshToken = ref(localStorage.getItem('admin_refresh_token') || '')
  const loading = ref(false)

  const isLoggedIn = computed(() => !!accessToken.value)
  const isAdmin = computed(() => user.value?.role === 'admin' || user.value?.role === 'super_admin')
  const isSuperAdmin = computed(() => user.value?.role === 'super_admin')
  const username = computed(() => user.value?.username || '')
  const userRole = computed(() => user.value?.role || '')

  // 初始化认证状态
  async function initializeAuth() {
    // 检查并清理无效的token
    if (accessToken.value === 'null' || accessToken.value === 'undefined' || accessToken.value === '') {
      console.log('清理无效的access token')
      accessToken.value = ''
      localStorage.removeItem('admin_access_token')
    }
    
    if (refreshToken.value === 'null' || refreshToken.value === 'undefined' || refreshToken.value === '') {
      console.log('清理无效的refresh token')
      refreshToken.value = ''
      localStorage.removeItem('admin_refresh_token')
    }
    
    if (accessToken.value) {
      try {
        await getCurrentUser()
      } catch (error) {
        console.error('初始化认证失败:', error)
        logout()
      }
    }
  }

  // 登录
  async function login(credentials) {
    loading.value = true
    try {
      const response = await authAPI.login(credentials)
      console.log('登录响应:', response)
      
      // 处理嵌套的响应格式
      if (response.success && response.token) {
        accessToken.value = response.token.access_token
        refreshToken.value = response.token.refresh_token || ''
        user.value = response.user
        
        localStorage.setItem('admin_access_token', response.token.access_token)
        // 只有当refresh_token存在时才存储
        if (response.token.refresh_token) {
          localStorage.setItem('admin_refresh_token', response.token.refresh_token)
        } else {
          localStorage.removeItem('admin_refresh_token')
        }
        
        // 只有管理员才能登录后台
        if (!isAdmin.value) {
          throw new Error('只有管理员才能访问后台管理系统')
        }
        
        ElMessage.success('登录成功')
        router.push('/')
        return true
      } else if (!response.success) {
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      console.error('登录错误:', error)
      // 优先使用后端返回的message字段
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.detail || 
                          error.message || 
                          '登录失败'
      ElMessage.error(errorMessage)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 登出
  async function logout() {
    try {
      if (accessToken.value) {
        await authAPI.logout()
      }
    } catch (error) {
      console.error('登出失败:', error)
    } finally {
      accessToken.value = ''
      refreshToken.value = ''
      user.value = null
      localStorage.removeItem('admin_access_token')
      localStorage.removeItem('admin_refresh_token')
      router.push('/login')
    }
  }

  // 获取当前用户信息
  async function getCurrentUser() {
    try {
      const response = await authAPI.getCurrentUser()
      user.value = response
      return response
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  }

  // 刷新令牌
  async function refreshAccessToken() {
    // 检查是否有有效的refresh token
    if (!refreshToken.value || refreshToken.value === 'null' || refreshToken.value === 'undefined') {
      console.log('没有有效的refresh token，跳过刷新')
      logout()
      return null
    }
    
    try {
      const response = await authAPI.refreshToken(refreshToken.value)
      accessToken.value = response.access_token
      localStorage.setItem('admin_access_token', response.access_token)
      return response.access_token
    } catch (error) {
      console.error('刷新令牌失败:', error)
      logout()
      throw error
    }
  }

  return {
    user,
    accessToken,
    refreshToken,
    loading,
    isLoggedIn,
    isAdmin,
    isSuperAdmin,
    username,
    userRole,
    initializeAuth,
    login,
    logout,
    getCurrentUser,
    refreshAccessToken
  }
})