from fastapi import APIRouter, HTTPException, Depends, Request
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
import logging

from core.models import QueryParams, ResponseModel
from core.database import db
from auth.middleware.auth_middleware import get_current_user, check_bid_permission, check_sid_permission
# from auth.services.audit_service import AuditService  # 现在使用中间件统一处理审计日志
from .MemberDataQuery import member_data_service

logger = logging.getLogger(__name__)

router = APIRouter()


def get_date_range_for_period(period_type: str) -> tuple:
    """根据报表类型计算日期范围"""
    today = datetime.now().date()
    
    if period_type == "week":
        # 获取本周的开始和结束日期（周一到周日）
        start = today - timedelta(days=today.weekday())
        end = start + timedelta(days=6)
    elif period_type == "month":
        # 获取本月的开始和结束日期
        start = today.replace(day=1)
        if today.month == 12:
            end = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            end = today.replace(month=today.month + 1, day=1) - timedelta(days=1)
    elif period_type == "quarter":
        # 获取本季度的开始和结束日期
        quarter = (today.month - 1) // 3
        start_month = quarter * 3 + 1
        start = today.replace(month=start_month, day=1)
        end_month = start_month + 2
        if end_month > 12:
            end = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            if end_month == 12:
                end = today.replace(month=12, day=31)
            else:
                end = today.replace(month=end_month + 1, day=1) - timedelta(days=1)
    elif period_type == "halfyear":
        # 获取半年的开始和结束日期
        if today.month <= 6:
            start = today.replace(month=1, day=1)
            end = today.replace(month=6, day=30)
        else:
            start = today.replace(month=7, day=1)
            end = today.replace(month=12, day=31)
    else:
        raise ValueError(f"不支持的报表类型: {period_type}")
    
    return start.strftime("%Y-%m-%d"), end.strftime("%Y-%m-%d")


async def process_inquiry_request(
    query_params: QueryParams,
    period_type: str,
    current_user: dict,
    request: Request = None
) -> ResponseModel:
    """处理查询请求的通用方法"""
    try:
        # 验证BID权限
        if query_params.bid:
            has_permission = await check_bid_permission(
                query_params.bid, "read", current_user
            )
            if not has_permission:
                raise HTTPException(
                    status_code=403,
                    detail=f"您没有访问品牌 {query_params.bid} 的权限"
                )
        
        # 验证SID权限（如果提供了SID）
        if query_params.bid and query_params.sid:
            has_sid_permission = await check_sid_permission(
                query_params.bid, query_params.sid, current_user
            )
            if not has_sid_permission:
                raise HTTPException(
                    status_code=403,
                    detail=f"您没有访问门店 {query_params.sid} 的权限"
                )
        
        # 自动设置日期范围
        if not query_params.start_date or not query_params.end_date:
            start_date, end_date = get_date_range_for_period(period_type)
            query_params.start_date = start_date
            query_params.end_date = end_date
            logger.info(f"{period_type}报表自动设置日期范围: {start_date} 到 {end_date}")
        
        # 调用数据服务获取所有会员数据
        data = await member_data_service.get_all_member_data(query_params)
        
        # 返回响应
        return ResponseModel(
            code=200,
            message=f"获取{period_type}报数据成功",
            data={
                "period_type": period_type,
                "start_date": query_params.start_date,
                "end_date": query_params.end_date,
                "data": data.model_dump(by_alias=True) if data else None
            }
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取{period_type}报数据异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.post("/week", response_model=ResponseModel)
async def get_week_report(
    query_params: QueryParams,
    request: Request,
    current_user: dict = Depends(get_current_user)
):
    """获取周报数据"""
    return await process_inquiry_request(query_params, "week", current_user, request)


@router.post("/month", response_model=ResponseModel)
async def get_month_report(
    query_params: QueryParams,
    request: Request,
    current_user: dict = Depends(get_current_user)
):
    """获取月报数据"""
    return await process_inquiry_request(query_params, "month", current_user, request)


@router.post("/quarter", response_model=ResponseModel)
async def get_quarter_report(
    query_params: QueryParams,
    request: Request,
    current_user: dict = Depends(get_current_user)
):
    """获取季报数据"""
    return await process_inquiry_request(query_params, "quarter", current_user, request)


@router.post("/halfyear", response_model=ResponseModel)
async def get_halfyear_report(
    query_params: QueryParams,
    request: Request,
    current_user: dict = Depends(get_current_user)
):
    """获取半年报数据"""
    return await process_inquiry_request(query_params, "halfyear", current_user, request)


@router.post("/sid", response_model=ResponseModel)
async def get_data_by_sid(
    query_params: QueryParams,
    request: Request,
    current_user: dict = Depends(get_current_user)
):
    """根据SID查询数据"""
    try:
        # SID是必需的
        if not query_params.sid:
            raise HTTPException(
                status_code=400,
                detail="SID参数是必需的"
            )
        
        # 验证权限
        if query_params.bid and query_params.sid:
            has_sid_permission = await check_sid_permission(
                query_params.bid, query_params.sid, current_user
            )
            if not has_sid_permission:
                raise HTTPException(
                    status_code=403,
                    detail=f"您没有访问门店 {query_params.sid} 的权限"
                )
        
        # 获取数据
        data = await member_data_service.get_all_member_data(query_params)
        
        return ResponseModel(
            code=200,
            message="获取门店数据成功",
            data={
                "sid": query_params.sid,
                "data": data.model_dump(by_alias=True) if data else None
            }
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"根据SID查询数据异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.post("/sid/batch", response_model=ResponseModel)
async def get_batch_data_by_sid(
    params: Dict[str, Any],
    current_user: dict = Depends(get_current_user)
):
    """批量SID查询"""
    try:
        # 获取SID列表
        sid_list = params.get("sid_list", [])
        if not sid_list:
            raise HTTPException(
                status_code=400,
                detail="sid_list参数是必需的"
            )
        
        # 获取其他参数
        bid = params.get("bid")
        start_date = params.get("start_date")
        end_date = params.get("end_date")
        
        # 批量查询每个SID的数据
        results = []
        for sid in sid_list:
            try:
                # 创建查询参数
                query_params = QueryParams(
                    bid=bid,
                    sid=sid,
                    start_date=start_date,
                    end_date=end_date
                )
                
                # 验证权限
                if bid and sid:
                    has_sid_permission = await check_sid_permission(
                        bid, sid, current_user
                    )
                    if not has_sid_permission:
                        logger.warning(f"用户没有访问门店 {sid} 的权限")
                        continue
                
                # 获取数据
                data = await member_data_service.get_all_member_data(query_params)
                results.append({
                    "sid": sid,
                    "data": data.model_dump(by_alias=True) if data else None
                })
            except Exception as e:
                logger.error(f"查询SID {sid} 数据失败: {str(e)}")
                results.append({
                    "sid": sid,
                    "error": str(e)
                })
        
        return ResponseModel(
            code=200,
            message="批量查询完成",
            data={
                "total": len(sid_list),
                "success": len([r for r in results if "data" in r]),
                "results": results
            }
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"批量SID查询异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.get("/sid/{sid}/validate", response_model=ResponseModel)
async def validate_sid(
    sid: str,
    bid: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """验证SID是否存在及有效"""
    try:
        # 验证权限
        if bid and sid:
            has_sid_permission = await check_sid_permission(
                bid, sid, current_user
            )
            if not has_sid_permission:
                return ResponseModel(
                    code=403,
                    message="没有权限访问此门店",
                    data={
                        "sid": sid,
                        "valid": False,
                        "reason": "no_permission"
                    }
                )
        
        # 这里可以添加实际的SID验证逻辑
        # 比如检查数据库中是否存在该SID等
        
        return ResponseModel(
            code=200,
            message="SID验证成功",
            data={
                "sid": sid,
                "valid": True,
                "bid": bid
            }
        )
    except Exception as e:
        logger.error(f"验证SID异常: {str(e)}")
        return ResponseModel(
            code=500,
            message=f"验证失败: {str(e)}",
            data={
                "sid": sid,
                "valid": False,
                "reason": str(e)
            }
        )