# -*- coding: utf-8 -*-
"""
PPT报告自定义时间范围端点
支持自选时间范围的报告生成
"""

import logging
import datetime
import asyncio
from typing import Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field

from core.models import ResponseModel
from auth.middleware.auth_middleware import get_current_user
from .task_management import (
    TaskModel, TaskStatus, TaskPriority,
    get_task_queue, TaskManager
)

logger = logging.getLogger(__name__)

# 创建路由器（需要认证）
router = APIRouter(dependencies=[Depends(get_current_user)])

class CustomRangeRequest(BaseModel):
    """自定义时间范围请求模型"""
    bid: str = Field(..., description="品牌ID")
    sid: Optional[str] = Field(None, description="门店ID")
    start_date: str = Field(..., description="开始日期 (YYYYMMDD 或 YYYY-MM-DD)")
    end_date: str = Field(..., description="结束日期 (YYYYMMDD 或 YYYY-MM-DD)")
    cashier_system: Optional[str] = Field("0", description="收银系统类型：0-无收银系统，1-品智收银")
    merchant_id: Optional[str] = Field(None, description="商户ID（品智收银门店拼音名称）")
    output_filename: Optional[str] = Field(None, description="输出文件名（可选）")
    async_mode: bool = Field(False, description="是否异步执行（True=立即返回任务ID，False=等待完成）")

def normalize_date(date_str: str) -> str:
    """
    规范化日期格式为 YYYYMMDD
    
    Args:
        date_str: 输入日期字符串（支持 YYYY-MM-DD 或 YYYYMMDD）
        
    Returns:
        YYYYMMDD 格式的日期字符串
    """
    if not date_str:
        return date_str
    
    # 移除所有非数字字符
    date_digits = ''.join(filter(str.isdigit, date_str))
    
    # 确保是8位数字
    if len(date_digits) == 8:
        return date_digits
    
    # 尝试解析为日期
    try:
        # 尝试 YYYY-MM-DD 格式
        if '-' in date_str:
            dt = datetime.datetime.strptime(date_str, '%Y-%m-%d')
            return dt.strftime('%Y%m%d')
    except:
        pass
    
    # 返回原值（让后续处理报错）
    return date_str

@router.post("/generate/custom", response_model=ResponseModel)
async def generate_custom_range_report(request: CustomRangeRequest):
    """
    生成自定义时间范围的会员数据报告PPT
    
    支持任意时间范围的报告生成，适用于：
    - 特定活动期间的数据分析
    - 自定义周期的业务回顾
    - 跨月/跨季度的数据对比
    
    Args:
        request: 自定义时间范围请求
        
    Returns:
        ResponseModel: 统一响应格式
    """
    try:
        logger.info(f"收到自定义时间范围PPT生成请求: {request}")
        
        # 规范化日期格式
        start_date = normalize_date(request.start_date)
        end_date = normalize_date(request.end_date)
        
        # 验证日期有效性
        try:
            start_dt = datetime.datetime.strptime(start_date, '%Y%m%d')
            end_dt = datetime.datetime.strptime(end_date, '%Y%m%d')
            
            if start_dt > end_dt:
                return ResponseModel(
                    code=400,
                    message="开始日期不能晚于结束日期",
                    data=None
                )
            
            # 检查时间跨度（最多365天）
            days_diff = (end_dt - start_dt).days
            if days_diff > 365:
                return ResponseModel(
                    code=400,
                    message="时间跨度不能超过365天",
                    data=None
                )
                
        except ValueError as e:
            return ResponseModel(
                code=400,
                message=f"日期格式错误: {str(e)}",
                data=None
            )
        
        # 生成文件名
        if not request.output_filename:
            request.output_filename = f"自定义报告_{request.bid}_{start_date}-{end_date}"
        
        # 确保文件名包含.pptx扩展名
        task_name = request.output_filename
        if not task_name.endswith('.pptx'):
            task_name = f"{task_name}.pptx"
        
        # 创建任务模型
        task = TaskModel(
            task_type="custom",  # 使用 custom 查询类型
            task_name=task_name,
            bid=request.bid,
            sid=request.sid,
            task_start_ftime=start_date,
            task_end_ftime=end_date,
            priority=TaskPriority.NORMAL.value,
            report_type="custom_range_report",
            source="api_custom",
            estimated_duration=10  # 自定义范围可能需要更长时间
        )
        
        # 添加到任务队列
        task_queue = get_task_queue()
        created_task = await task_queue.add_task(task)
        
        logger.info(f"自定义范围任务创建成功: ID={created_task.id}, UUID={created_task.task_uuid}")
        
        # 如果是异步模式，立即返回任务信息
        if request.async_mode:
            return ResponseModel(
                code=200,
                message="任务已创建，正在后台处理",
                data={
                    "task_id": created_task.id,
                    "task_uuid": created_task.task_uuid,
                    "status": created_task.status,
                    "estimated_duration": created_task.estimated_duration,
                    "check_status_url": f"/api/ppt-report/task/{created_task.id}/status"
                }
            )
        
        # 同步模式：等待任务完成
        task_manager = TaskManager()
        max_wait_time = 600  # 10分钟
        wait_interval = 3  # 每3秒检查一次
        total_waited = 0
        
        while total_waited < max_wait_time:
            await asyncio.sleep(wait_interval)
            total_waited += wait_interval
            
            # 查询任务状态
            current_task = await task_manager.get_task_by_id(created_task.id)
            
            if not current_task:
                logger.error(f"任务 {created_task.id} 丢失")
                return ResponseModel(
                    code=500,
                    message="任务执行异常",
                    data=None
                )
            
            # 记录进度
            if total_waited % 10 == 0:  # 每10秒记录一次
                logger.info(f"任务 {created_task.id} 状态: {current_task.status}, 进度: {current_task.progress}%")
            
            # 任务完成
            if current_task.status == TaskStatus.COMPLETED.value:
                logger.info(f"自定义范围任务 {created_task.id} 完成")
                
                return ResponseModel(
                    code=200,
                    message="PPT生成成功",
                    data={
                        "task_id": current_task.id,
                        "downloadUrl": current_task.ppt_oss_url,
                        "dataDownloadUrl": current_task.excel_oss_url,
                        "file_name": current_task.task_name,
                        "file_size": current_task.file_PPT_size,
                        "time_range": f"{start_date} 至 {end_date}",
                        "days": days_diff + 1
                    }
                )
            
            # 任务失败
            elif current_task.status == TaskStatus.FAILED.value:
                logger.error(f"任务 {created_task.id} 失败: {current_task.error_info}")
                return ResponseModel(
                    code=500,
                    message=f"PPT生成失败: {current_task.error_info}",
                    data={"task_id": current_task.id}
                )
            
            # 任务被取消
            elif current_task.status == TaskStatus.CANCELLED.value:
                logger.warning(f"任务 {created_task.id} 被取消")
                return ResponseModel(
                    code=400,
                    message="任务被取消",
                    data={"task_id": current_task.id}
                )
        
        # 超时
        logger.error(f"任务 {created_task.id} 执行超时")
        return ResponseModel(
            code=408,
            message="任务执行超时，请使用异步模式或稍后查询任务状态",
            data={
                "task_id": created_task.id,
                "check_status_url": f"/api/ppt-report/task/{created_task.id}/status"
            }
        )
        
    except Exception as e:
        logger.error(f"创建自定义范围PPT任务异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"创建任务失败: {str(e)}")


@router.get("/generate/presets", response_model=ResponseModel)
async def get_time_range_presets():
    """
    获取预设的时间范围选项
    
    Returns:
        ResponseModel: 包含预设时间范围的响应
    """
    try:
        today = datetime.datetime.now()
        
        # 计算各种预设时间范围
        presets = [
            {
                "name": "本周",
                "query_type": "week",
                "start_date": (today - datetime.timedelta(days=today.weekday())).strftime('%Y%m%d'),
                "end_date": today.strftime('%Y%m%d')
            },
            {
                "name": "上周",
                "query_type": "week",
                "start_date": (today - datetime.timedelta(days=today.weekday() + 7)).strftime('%Y%m%d'),
                "end_date": (today - datetime.timedelta(days=today.weekday() + 1)).strftime('%Y%m%d')
            },
            {
                "name": "本月",
                "query_type": "month",
                "start_date": today.replace(day=1).strftime('%Y%m%d'),
                "end_date": today.strftime('%Y%m%d')
            },
            {
                "name": "上月",
                "query_type": "month",
                "start_date": (today.replace(day=1) - datetime.timedelta(days=1)).replace(day=1).strftime('%Y%m%d'),
                "end_date": (today.replace(day=1) - datetime.timedelta(days=1)).strftime('%Y%m%d')
            },
            {
                "name": "本季度",
                "query_type": "quarter",
                "start_date": datetime.datetime(today.year, ((today.month - 1) // 3) * 3 + 1, 1).strftime('%Y%m%d'),
                "end_date": today.strftime('%Y%m%d')
            },
            {
                "name": "近30天",
                "query_type": "custom",
                "start_date": (today - datetime.timedelta(days=30)).strftime('%Y%m%d'),
                "end_date": today.strftime('%Y%m%d')
            },
            {
                "name": "近90天",
                "query_type": "custom",
                "start_date": (today - datetime.timedelta(days=90)).strftime('%Y%m%d'),
                "end_date": today.strftime('%Y%m%d')
            },
            {
                "name": "今年至今",
                "query_type": "custom",
                "start_date": datetime.datetime(today.year, 1, 1).strftime('%Y%m%d'),
                "end_date": today.strftime('%Y%m%d')
            }
        ]
        
        return ResponseModel(
            code=200,
            message="获取预设时间范围成功",
            data={
                "presets": presets,
                "current_date": today.strftime('%Y-%m-%d')
            }
        )
        
    except Exception as e:
        logger.error(f"获取预设时间范围失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取预设失败: {str(e)}")