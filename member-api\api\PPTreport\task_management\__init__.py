"""
任务管理模块
用于管理PPT报告生成任务的调度、执行和状态跟踪
"""

from .database_init import init_task_database, check_and_create_tables
from .models import TaskModel, TaskStatus, TaskPriority
from .task_manager import TaskManager
from .task_queue import TaskQueue, get_task_queue, init_task_queue, shutdown_task_queue
from .task_executor import PPTTaskExecutor, ppt_task_executor
from .task_scheduler import (
    TaskScheduler,
    get_task_scheduler,
    init_task_scheduler,
    shutdown_task_scheduler
)

__all__ = [
    'init_task_database',
    'check_and_create_tables',
    'TaskModel',
    'TaskStatus',
    'TaskPriority',
    'TaskManager',
    'TaskQueue',
    'get_task_queue',
    'init_task_queue',
    'shutdown_task_queue',
    'PPTTaskExecutor',
    'ppt_task_executor',
    'TaskScheduler',
    'get_task_scheduler',
    'init_task_scheduler',
    'shutdown_task_scheduler'
]