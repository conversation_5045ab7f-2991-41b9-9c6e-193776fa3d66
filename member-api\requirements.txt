# 核心框架
fastapi>=0.104.0
uvicorn[standard]==0.22.0

# 数据库相关
aiomysql>=0.2.0
asyncpg==0.29.0

# 配置和环境
python-dotenv>=1.0.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# 认证和安全
bcrypt==4.1.1
PyJWT==2.8.0
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4

# HTTP客户端
aiohttp==3.9.1

# 文件处理
python-multipart==0.0.6
aiofiles>=23.2.0

# 图表生成依赖
matplotlib>=3.7.0
pandas>=2.0.0
numpy>=1.24.0

# PPT处理依赖
python-pptx>=0.6.21

# AI服务
openai>=1.6.0
dashscope>=1.10.0

# 对象存储
oss2>=2.18.0

# 日志处理
loguru>=0.7.2

# GUI框架（如果需要）
nicegui==1.4.5