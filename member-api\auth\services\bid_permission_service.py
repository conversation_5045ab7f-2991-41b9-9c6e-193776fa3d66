"""
BID权限管理服务
基于新的权限关联表管理用户的BID访问权限
"""
import logging
import json
from typing import List, Dict, Any, Optional
from datetime import datetime
import aiomysql

logger = logging.getLogger(__name__)


class BidPermissionService:
    """BID权限管理服务"""
    
    def __init__(self, db_pool):
        self.db_pool = db_pool
    
    async def get_user_bid_permissions(self, username: str) -> List[Dict[str, Any]]:
        """
        获取用户的所有BID权限
        
        Args:
            username: 用户名
            
        Returns:
            用户的BID权限列表
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    sql = """
                    SELECT 
                        ubp.*,
                        bc.description as bid_description,
                        bc.sid_list as available_sids
                    FROM member_report_user_bid_permissions ubp
                    LEFT JOIN member_report_bid_configs bc ON ubp.bid = bc.bid
                    WHERE ubp.username = %s 
                      AND ubp.status = 'active'
                      AND (ubp.expire_at IS NULL OR ubp.expire_at > NOW())
                    """
                    await cursor.execute(sql, (username,))
                    results = await cursor.fetchall()
                    
                    # 解析JSON字段
                    for row in results:
                        if row['permissions']:
                            row['permissions'] = json.loads(row['permissions'])
                        if row['sid_filter']:
                            row['sid_filter'] = json.loads(row['sid_filter'])
                        if row['available_sids']:
                            row['available_sids'] = json.loads(row['available_sids'])
                    
                    return results
                    
        except Exception as e:
            logger.error(f"获取用户BID权限失败: {e}")
            return []
    
    async def check_user_bid_permission(
        self, 
        username: str, 
        bid: str, 
        permission: str = "read"
    ) -> bool:
        """
        检查用户是否有特定BID的权限
        
        Args:
            username: 用户名
            bid: BID代码
            permission: 需要的权限类型 (read/write/admin)
            
        Returns:
            是否有权限
        """
        try:
            # 首先检查用户角色，超级管理员拥有所有权限
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 检查用户角色
                    sql = "SELECT role FROM member_report_users WHERE username = %s"
                    await cursor.execute(sql, (username,))
                    user = await cursor.fetchone()
                    
                    if user and user['role'] == 'super_admin':
                        return True
                    
                    # 检查具体的BID权限
                    sql = """
                    SELECT permissions 
                    FROM member_report_user_bid_permissions
                    WHERE username = %s 
                      AND (bid = %s OR bid = '*')
                      AND status = 'active'
                      AND (expire_at IS NULL OR expire_at > NOW())
                    """
                    await cursor.execute(sql, (username, bid))
                    result = await cursor.fetchone()
                    
                    if result and result['permissions']:
                        permissions = json.loads(result['permissions'])
                        return '*' in permissions or permission in permissions
                    
                    return False
                    
        except Exception as e:
            logger.error(f"检查BID权限失败: {e}")
            return False
    
    async def check_user_sid_permission(
        self, 
        username: str, 
        bid: str, 
        sid: str
    ) -> bool:
        """
        检查用户是否有特定SID的访问权限
        
        Args:
            username: 用户名
            bid: BID代码
            sid: SID代码
            
        Returns:
            是否有权限
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 检查用户角色
                    sql = "SELECT role FROM member_report_users WHERE username = %s"
                    await cursor.execute(sql, (username,))
                    user = await cursor.fetchone()
                    
                    if user and user['role'] == 'super_admin':
                        return True
                    
                    # 获取用户的BID权限配置
                    sql = """
                    SELECT sid_filter 
                    FROM member_report_user_bid_permissions
                    WHERE username = %s 
                      AND (bid = %s OR bid = '*')
                      AND status = 'active'
                      AND (expire_at IS NULL OR expire_at > NOW())
                    """
                    await cursor.execute(sql, (username, bid))
                    result = await cursor.fetchone()
                    
                    if not result:
                        return False
                    
                    # 如果sid_filter为NULL，表示可以访问所有SID
                    if result['sid_filter'] is None:
                        return True
                    
                    # 解析sid_filter
                    sid_filter = json.loads(result['sid_filter'])
                    
                    # 如果是空列表，表示没有任何SID权限
                    if isinstance(sid_filter, list) and len(sid_filter) == 0:
                        return False
                    
                    # 检查是否包含通配符或具体SID
                    if isinstance(sid_filter, list):
                        return '*' in sid_filter or sid in sid_filter
                    
                    return False
                    
        except Exception as e:
            logger.error(f"检查SID权限失败: {e}")
            return False
    
    async def grant_user_bid_permission(
        self,
        username: str,
        bid: str,
        permissions: List[str],
        sid_filter: Optional[List[str]] = None,
        granted_by: str = None,
        grant_reason: str = None,
        expire_days: Optional[int] = None
    ) -> bool:
        """
        授予用户BID权限
        
        Args:
            username: 用户名
            bid: BID代码
            permissions: 权限列表 ["read", "write", "admin"]
            sid_filter: SID过滤器，None表示所有，[]表示无，["sid1","sid2"]表示特定SID
            granted_by: 授权人
            grant_reason: 授权原因
            expire_days: 过期天数，None表示永久
            
        Returns:
            是否成功
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    sql = """
                    INSERT INTO member_report_user_bid_permissions 
                    (username, bid, permissions, sid_filter, granted_by, grant_reason, expire_at, status)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, 'active')
                    ON DUPLICATE KEY UPDATE
                        permissions = VALUES(permissions),
                        sid_filter = VALUES(sid_filter),
                        granted_by = VALUES(granted_by),
                        grant_reason = VALUES(grant_reason),
                        expire_at = VALUES(expire_at),
                        status = 'active',
                        updated_at = NOW()
                    """
                    
                    # 处理过期时间
                    expire_at_value = None
                    if expire_days:
                        from datetime import datetime, timedelta
                        expire_at_value = datetime.now() + timedelta(days=expire_days)
                    
                    await cursor.execute(sql, (
                        username,
                        bid,
                        json.dumps(permissions),
                        json.dumps(sid_filter) if sid_filter is not None else None,
                        granted_by,
                        grant_reason,
                        expire_at_value
                    ))
                    await conn.commit()
                    
                    logger.info(f"成功授予用户 {username} 对BID {bid} 的权限")
                    return True
                    
        except Exception as e:
            logger.error(f"授予BID权限失败: {e}")
            return False
    
    async def revoke_user_bid_permission(
        self,
        username: str,
        bid: str
    ) -> bool:
        """
        撤销用户的BID权限
        
        Args:
            username: 用户名
            bid: BID代码
            
        Returns:
            是否成功
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    sql = """
                    UPDATE member_report_user_bid_permissions 
                    SET status = 'inactive', updated_at = NOW()
                    WHERE username = %s AND bid = %s
                    """
                    await cursor.execute(sql, (username, bid))
                    await conn.commit()
                    
                    logger.info(f"成功撤销用户 {username} 对BID {bid} 的权限")
                    return True
                    
        except Exception as e:
            logger.error(f"撤销BID权限失败: {e}")
            return False
    
    async def revoke_all_user_permissions(self, username: str) -> bool:
        """
        撤销用户的所有BID权限
        
        Args:
            username: 用户名
            
        Returns:
            是否成功
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    sql = """
                    UPDATE member_report_user_bid_permissions 
                    SET status = 'inactive', updated_at = NOW()
                    WHERE username = %s
                    """
                    await cursor.execute(sql, (username,))
                    await conn.commit()
                    
                    logger.info(f"成功撤销用户 {username} 的所有权限")
                    return True
                    
        except Exception as e:
            logger.error(f"撤销所有权限失败: {e}")
            return False
    
    async def get_bid_users(self, bid: str) -> List[Dict[str, Any]]:
        """
        获取有特定BID权限的所有用户
        
        Args:
            bid: BID代码
            
        Returns:
            用户列表
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    sql = """
                    SELECT 
                        ubp.*,
                        u.real_name,
                        u.role,
                        u.department
                    FROM member_report_user_bid_permissions ubp
                    LEFT JOIN member_report_users u ON ubp.username = u.username
                    WHERE ubp.bid = %s 
                      AND ubp.status = 'active'
                      AND (ubp.expire_at IS NULL OR ubp.expire_at > NOW())
                    ORDER BY ubp.created_at DESC
                    """
                    await cursor.execute(sql, (bid,))
                    results = await cursor.fetchall()
                    
                    # 解析JSON字段
                    for row in results:
                        if row['permissions']:
                            row['permissions'] = json.loads(row['permissions'])
                        if row['sid_filter']:
                            row['sid_filter'] = json.loads(row['sid_filter'])
                    
                    return results
                    
        except Exception as e:
            logger.error(f"获取BID用户列表失败: {e}")
            return []
    
    async def update_user_permissions_from_old_format(self, username: str) -> bool:
        """
        从旧的JSON格式迁移用户权限到新表
        
        Args:
            username: 用户名
            
        Returns:
            是否成功
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 获取用户的旧权限配置
                    sql = """
                    SELECT bid_permissions, sid_filter 
                    FROM member_report_users 
                    WHERE username = %s
                    """
                    await cursor.execute(sql, (username,))
                    user = await cursor.fetchone()
                    
                    if not user or not user['bid_permissions']:
                        return True
                    
                    bid_permissions = json.loads(user['bid_permissions'])
                    sid_filter = json.loads(user['sid_filter']) if user['sid_filter'] else {}
                    
                    # 迁移每个BID权限
                    for bid_perm in bid_permissions:
                        bid = bid_perm.get('bid')
                        permissions = bid_perm.get('permissions', ['read'])
                        
                        # 获取对应的SID过滤器
                        sids = None
                        if bid in sid_filter:
                            sids = sid_filter[bid]
                        
                        # 插入到新表
                        await self.grant_user_bid_permission(
                            username=username,
                            bid=bid,
                            permissions=permissions,
                            sid_filter=sids,
                            granted_by='system',
                            grant_reason='从旧权限系统迁移'
                        )
                    
                    logger.info(f"成功迁移用户 {username} 的权限配置")
                    return True
                    
        except Exception as e:
            logger.error(f"迁移用户权限失败: {e}")
            return False