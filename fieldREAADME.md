# 本项目通过bid获取的数据信息

**输入：**

商户bid（必填）

门店sid（选填）

时间选项（周报、月报、季报、半年报）

生成报告（是/否，目前默认为否，同时不显示）



**输出：**

| 数据来源     | 参数                                   | 数据库表                    | 数据库获取来源                                               | 计算逻辑                                   | 计算备注                         |
| ------------ | -------------------------------------- | --------------------------- | ------------------------------------------------------------ | ------------------------------------------ | -------------------------------- |
| 会员基础模块 | 会员总数量                             | dprpt_welife_user_log       | all_user                                                     | 累计会员量                                 | 取范围内最后一天数值             |
| 会员基础模块 | 会员净存量                             | dprpt_welife_user_log       | all_user - all_unregistered_user                             | 累计会员量 - 取消关注会员量                | 取范围内最后一天数值进行加减操作 |
| 会员基础模块 | 新增会员数量                           | dprpt_welife_user_log       | new_user                                                     | 新增的注册用户数                           | 加和                             |
| 会员基础模块 | 取关会员数量                           | dprpt_welife_user_log       | new_unregistered_user                                        | 取消关注会员量                             | 加和                             |
| 会员基础模块 | 取关占比                               | dprpt_welife_user_log       | new_unregistered_user / new_user                             | 取消关注会员量 / 新增的注册用户数          | 加和后进行除法                   |
| 会员基础模块 | 有消费的会员数                         | dprpt_welife_user_log       | new_user_consomer                                            | 新增消费会员量                             | 加和                             |
| 会员基础模块 | 有储值的会员数                         | dprpt_welife_user_log       | new_user_charger                                             | 新增储值会员量                             | 加和                             |
| 会员基础模块 | 累计消费的会员数                       | dprpt_welife_user_log       | all_user_consomer                                            | 累计新增消费会员量                         | 取范围内最后一天数值             |
| 会员基础模块 | 累计储值的会员数                       | dprpt_welife_user_log       | all_user_charger                                             | 累计新增储值会员量                         | 取范围内最后一天数值             |
| 会员基础模块 | 新增完善会员量                         | dprpt_welife_users_stat     | uUserInfoNum                                            | 新增完善四项基础资料会员量                 | 加和                             |
| 会员基础模块 | 新增会员完善率                         | dprpt_welife_users_stat     | uUserInfoNum / uIncreNum                                | 新增完善四项基础资料会员量/ 新增会员量     | 加和后进行除法                   |
| 会员基础模块 | 完善会员总数                           | dprpt_welife_users_stat     | uCardInfoNum                                                 | 期末四项基础资料会员存量                   | 取范围内最后一天数值             |
| 会员基础模块 | 会员1次消费总人数                      | dprpt_welife_users_stat     | uConsume1Num                                                 | 期末历史消费1次会员数量                    | 取范围内最后一天数值             |
| 会员基础模块 | 会员2次消费以上总人数                  | dprpt_welife_users_stat     | uConsume2Num                                                 | 期末历史消费2次（含）以上会员数量          | 取范围内最后一天数值             |
| 会员消费模块 | 消费总金额（非实收）                   | dprpt_welife_consume_log    | consume_amount + overdue_amount - cancel_amount              | 正常消费 + 逾期消费 - 取消消费             | 分别进行加和后进行加减法         |
| 会员消费模块 | 会员消费人数                           | dprpt_welife_consume_log    | consume_amount_uv + overdue_uv                               | 正常 UV + 逾期 UV（取消的用户不减）        | 分别进行加和后进行加减法         |
| 会员消费模块 | 会员总消费笔数                         | dprpt_welife_consume_log    | consume_amount_pv + overdue_pv - cancel_amount_pv            | 正常消费 PV + 逾期 PV - 取消 PV            | 分别进行加和后进行加减法         |
| 会员消费模块 | 会员实收金额                           | dprpt_welife_consume_log    | consume_cash - cancel_cash                                   | 现金支付 - 取消现金支付                    | 分别进行加和后进行加减法         |
| 会员消费模块 | 现金支付会员数                         | dprpt_welife_consume_log    | consume_cash_uv                                              | 使用现金支付的用户数（取消不减）           | 加和                             |
| 会员消费模块 | 会员现金消费笔数                       | dprpt_welife_consume_log    | consume_cash_pv - cancel_cash_pv                             | 现金支付 PV - 取消现金支付 PV              | 分别进行加和后进行加减法         |
| 会员消费模块 | 会员储值实收金额                       | dprpt_welife_consume_log    | consume_prepay - cancel_prepay + overdue_amount              | 储值预存 - 取消储值预存 + 逾期金额         | 分别进行加和后进行加减法         |
| 会员消费模块 | 储值支付会员数                         | dprpt_welife_consume_log    | consume_prepay_uv + overdue_uv                               | 正常储值 UV + 逾期 UV                      | 分别进行加和后进行加减法         |
| 会员消费模块 | 会员储值消费笔数                       | dprpt_welife_consume_log    | consume_prepay_pv - cancel_prepay_pv + overdue_pv            | 储值 PV - 取消储值 PV + 逾期 PV            | 分别进行加和后进行加减法         |
| 会员消费模块 | 会员总实收金额                         | dprpt_welife_consume_log    | consume_cash - cancel_cash + consume_prepay - cancel_prepay + overdue_amount |                                            | 分别进行加和后进行加减法         |
| 会员消费模块 | 会员人均贡献                           | dprpt_welife_consume_log    | 消费总金额（非实收）/会员消费人数                            | 消费总金额（非实收）/会员消费人数          | 计算后除                         |
| 会员消费模块 | 券带动总交易金额                       | wedatas_welife_coupon_log   | trade_amount - cancel_trade_amount                           | 交易总金额 - 撤销总金额                    | 分别进行加和后进行加减法         |
| 会员充值模块 | 期间会员充值笔数                       | dprpt_welife_charge_log_bid | charge_amount_pv - cancel_charge_amount_pv                   | 充值笔数 - 取消充值笔数                    | 分别进行加和后进行加减法         |
| 会员充值模块 | 期间充值实收总金额                     | dprpt_welife_charge_log_bid | charge_cash - cancel_charge_cash                             | 实收金额 - 撤销储值实收金额                | 分别进行加和后进行加减法         |
| 会员充值模块 | 期间消耗储值实收总金额                 | dprpt_welife_consume_log    | consume_prepay - cancel_prepay + overdue_amount              | 储值预存 - 取消储值预存 + 逾期金额         | 分别进行加和后进行加减法         |
| 会员充值模块 | 存储留存率                             |                             | (期间储值总金额-期间消费使用的储值金额)/期间储值总金额       |                                            |                                  |
| 券交易模块   | 券名称                                 | wedatas_welife_coupon_log   | couponname                                                   | 券名称                                     |                                  |
| 券交易模块   | 活动类型（营销活动名称对应的）         | wedatas_welife_coupon_log   | atype                                                        | 活动类型                                   |                                  |
| 券交易模块   | 券发放量(张)（营销活动名称对应的）     | wedatas_welife_coupon_log   | coupon_send - cancel_coupon_send                             | 券发放数量 - 撤销券发放数量                |                                  |
| 券交易模块   | 券使用量(张)（营销活动名称对应的）     | wedatas_welife_coupon_log   | coupon_used - cancel_coupon_used                             | 券使用数量 - 撤销使用券数量                |                                  |
| 券交易模块   | 券使用率(%)（营销活动名称对应的）      | wedatas_welife_coupon_log   | 券使用量/券发放量                                            |                                            |                                  |
| 券交易模块   | 券抵扣金额(元)（营销活动名称对应的）   | wedatas_welife_coupon_log   | (coupon_used - cancel_coupon_used) * camount                 | (券使用数量 - 撤销使用券数量) * 券抵扣金额 |                                  |
| 券交易模块   | 带动储值消费(元)（营销活动名称对应的） | wedatas_welife_coupon_log   | trade_prepay - cancel_trade_prepay                           | 交易预存 - 撤销预存                        |                                  |
| 券交易模块   | 带动现金消费(元)（营销活动名称对应的） | wedatas_welife_coupon_log   | trade_cash - cancel_trade_cash                               | 交易现金 - cancel_trade_cash               |                                  |
| 券交易模块   | 带动总交易金额                         | wedatas_welife_coupon_log   | trade_amount - cancel_trade_amount                           | 交易总金额 - 撤销总金额                    |                                  |



**展现形式：**

**季报&半年报五列：**

字段名

真实值

环比变化（上一个同周期）

同期变化（去年同周期）

字段来源/计算方法

**周报&月报七列：**

字段名

真实值

环比变化三列（前三个月/周分别对比）

同期变化（去年同周期）

字段来源/计算方法