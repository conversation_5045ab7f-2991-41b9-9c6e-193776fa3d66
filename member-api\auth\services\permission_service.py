"""
权限验证服务
"""
from typing import List, Dict, Optional, Any
from datetime import datetime
import json

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, update
from fastapi import HTTPException, status

from ..models.user import User
from ..models.bid_config import BidConfig


class PermissionService:
    """权限验证服务类"""
    
    @staticmethod
    async def check_bid_permission(
        db: AsyncSession,
        user_id: int,
        bid: str,
        required_permission: str = "read"
    ) -> bool:
        """
        检查用户对指定BID的权限
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            bid: BID代码
            required_permission: 所需权限类型 (read/write/admin)
            
        Returns:
            bool: 是否有权限
        """
        # 获取用户信息
        result = await db.execute(
            select(User).where(
                and_(
                    User.id == user_id,
                    User.deleted_at.is_(None)
                )
            )
        )
        user = result.scalar_one_or_none()
        
        if not user:
            return False
            
        # 超级管理员拥有所有权限
        if user.role == 'super_admin':
            return True
            
        # 检查用户状态
        if user.status != 'active':
            return False
            
        # 检查BID权限配置
        if not user.bid_permissions:
            return False
            
        try:
            bid_permissions = json.loads(user.bid_permissions) if isinstance(user.bid_permissions, str) else user.bid_permissions
            
            # 查找对应的BID权限
            for bid_perm in bid_permissions:
                if bid_perm.get('bid') == bid:
                    permissions = bid_perm.get('permissions', [])
                    
                    # 检查权限级别
                    if required_permission == "read" and "read" in permissions:
                        return True
                    elif required_permission == "write" and "write" in permissions:
                        return True
                    elif required_permission == "admin" and "admin" in permissions:
                        return True
                        
            return False
            
        except (json.JSONDecodeError, TypeError):
            return False
    
    @staticmethod
    async def check_sid_permission(
        db: AsyncSession,
        user_id: int,
        bid: str,
        sid: str
    ) -> bool:
        """
        检查用户对指定SID的权限
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            bid: BID代码
            sid: SID代码
            
        Returns:
            bool: 是否有权限
        """
        # 首先检查BID权限
        if not await PermissionService.check_bid_permission(db, user_id, bid, "read"):
            return False
            
        # 获取用户信息
        result = await db.execute(
            select(User).where(
                and_(
                    User.id == user_id,
                    User.deleted_at.is_(None)
                )
            )
        )
        user = result.scalar_one_or_none()
        
        if not user:
            return False
            
        # 超级管理员拥有所有权限
        if user.role == 'super_admin':
            return True
            
        # 检查SID过滤器
        if not user.sid_filter:
            # 没有SID过滤器意味着可以访问该BID下的所有SID
            return True
            
        try:
            sid_filter = json.loads(user.sid_filter) if isinstance(user.sid_filter, str) else user.sid_filter
            
            # 检查对应BID的SID列表
            if bid in sid_filter:
                allowed_sids = sid_filter[bid]
                
                # "*" 表示可以访问所有SID
                if "*" in allowed_sids:
                    return True
                    
                # 检查具体的SID
                if sid in allowed_sids:
                    return True
                    
            return False
            
        except (json.JSONDecodeError, TypeError):
            # 如果解析失败，默认允许（因为已经有BID权限）
            return True
    
    @staticmethod
    async def check_quota(
        db: AsyncSession,
        user_id: int,
        quota_type: str = "daily_query"
    ) -> bool:
        """
        检查用户配额是否超限
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            quota_type: 配额类型 (daily_query/monthly_report)
            
        Returns:
            bool: 配额是否充足
        """
        # 获取用户信息
        result = await db.execute(
            select(User).where(
                and_(
                    User.id == user_id,
                    User.deleted_at.is_(None)
                )
            )
        )
        user = result.scalar_one_or_none()
        
        if not user:
            return False
            
        # 超级管理员无配额限制
        if user.role == 'super_admin':
            return True
            
        # 检查用户状态
        if user.status != 'active':
            return False
            
        try:
            # 获取配额限制（现在是整数）
            quotas = user.quotas if user.quotas else 0
            use_quotas = user.use_quotas if user.use_quotas else 0
            
            # 检查是否需要重置配额（每日/每月）
            today = datetime.now().date()
            last_reset = use_quotas.get('last_reset_date')
            
            if last_reset:
                last_reset_date = datetime.strptime(last_reset, '%Y-%m-%d').date()
                
                # 每日配额重置
                if quota_type == "daily_query" and last_reset_date < today:
                    use_quotas['daily_query_count'] = 0
                    use_quotas['last_reset_date'] = today.strftime('%Y-%m-%d')
                    
                # 每月配额重置
                elif quota_type == "monthly_report":
                    if last_reset_date.month != today.month or last_reset_date.year != today.year:
                        use_quotas['monthly_report_count'] = 0
                        use_quotas['last_reset_date'] = today.strftime('%Y-%m-%d')
            else:
                # 初始化配额使用
                use_quotas = {
                    'daily_query_count': 0,
                    'monthly_report_count': 0,
                    'last_reset_date': today.strftime('%Y-%m-%d')
                }
            
            # 获取配额限制和使用量
            if quota_type == "daily_query":
                limit = quotas.get('daily_query_limit', 0)
                used = use_quotas.get('daily_query_count', 0)
            elif quota_type == "monthly_report":
                limit = quotas.get('monthly_report_limit', 0)
                used = use_quotas.get('monthly_report_count', 0)
            else:
                return False
            
            # 0表示无限制
            if limit == 0:
                return True
                
            # 检查是否超限
            return used < limit
            
        except (json.JSONDecodeError, TypeError, ValueError):
            return False
    
    @staticmethod
    async def increment_quota_usage(
        db: AsyncSession,
        user_id: int,
        quota_type: str = "daily_query",
        increment: int = 1
    ) -> bool:
        """
        增加配额使用量
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            quota_type: 配额类型
            increment: 增加量
            
        Returns:
            bool: 操作是否成功
        """
        # 获取用户信息
        result = await db.execute(
            select(User).where(
                and_(
                    User.id == user_id,
                    User.deleted_at.is_(None)
                )
            )
        )
        user = result.scalar_one_or_none()
        
        if not user:
            return False
        
        # 超级管理员不计算配额
        if user.role == 'super_admin':
            return True
        
        try:
            # 解析当前使用量
            use_quotas = json.loads(user.use_quotas) if isinstance(user.use_quotas, str) else user.use_quotas or {}
            
            # 确保有今天的日期
            today = datetime.now().date().strftime('%Y-%m-%d')
            if 'last_reset_date' not in use_quotas:
                use_quotas['last_reset_date'] = today
            
            # 增加对应配额使用量
            if quota_type == "daily_query":
                use_quotas['daily_query_count'] = use_quotas.get('daily_query_count', 0) + increment
            elif quota_type == "monthly_report":
                use_quotas['monthly_report_count'] = use_quotas.get('monthly_report_count', 0) + increment
            else:
                return False
            
            # 更新数据库
            await db.execute(
                update(User).where(User.id == user_id).values(
                    use_quotas=json.dumps(use_quotas)
                )
            )
            await db.commit()
            
            return True
            
        except Exception as e:
            await db.rollback()
            return False
    
    @staticmethod
    async def get_user_permissions(
        db: AsyncSession,
        user_id: int
    ) -> Dict[str, Any]:
        """
        获取用户的所有权限信息
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            
        Returns:
            Dict: 权限信息字典
        """
        # 获取用户信息
        result = await db.execute(
            select(User).where(
                and_(
                    User.id == user_id,
                    User.deleted_at.is_(None)
                )
            )
        )
        user = result.scalar_one_or_none()
        
        if not user:
            return {}
        
        try:
            bid_permissions = json.loads(user.bid_permissions) if isinstance(user.bid_permissions, str) else user.bid_permissions or []
            sid_filter = json.loads(user.sid_filter) if isinstance(user.sid_filter, str) else user.sid_filter or {}
            quotas = json.loads(user.quotas) if isinstance(user.quotas, str) else user.quotas or {}
            use_quotas = json.loads(user.use_quotas) if isinstance(user.use_quotas, str) else user.use_quotas or {}
            
            return {
                "role": user.role,
                "status": user.status,
                "bid_permissions": bid_permissions,
                "sid_filter": sid_filter,
                "quotas": quotas,
                "use_quotas": use_quotas,
                "is_super_admin": user.role == 'super_admin',
                "is_admin": user.role in ['super_admin', 'admin']
            }
            
        except (json.JSONDecodeError, TypeError):
            return {
                "role": user.role,
                "status": user.status,
                "bid_permissions": [],
                "sid_filter": {},
                "quotas": {},
                "use_quotas": {},
                "is_super_admin": user.role == 'super_admin',
                "is_admin": user.role in ['super_admin', 'admin']
            }
    
    @staticmethod
    async def validate_api_access(
        db: AsyncSession,
        user_id: int,
        endpoint: str,
        method: str = "GET"
    ) -> bool:
        """
        验证API访问权限
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            endpoint: API端点
            method: HTTP方法
            
        Returns:
            bool: 是否有权限访问
        """
        # 获取用户信息
        result = await db.execute(
            select(User).where(
                and_(
                    User.id == user_id,
                    User.deleted_at.is_(None)
                )
            )
        )
        user = result.scalar_one_or_none()
        
        if not user:
            return False
        
        # 检查用户状态
        if user.status != 'active':
            return False
        
        # 超级管理员可以访问所有API
        if user.role == 'super_admin':
            return True
        
        # 管理员可以访问管理相关API
        if user.role == 'admin':
            admin_endpoints = ['/admin/', '/users/', '/bid-configs/']
            for admin_endpoint in admin_endpoints:
                if admin_endpoint in endpoint:
                    return True
        
        # 普通用户只能访问基础API
        user_endpoints = ['/auth/', '/query/', '/report/']
        for user_endpoint in user_endpoints:
            if user_endpoint in endpoint:
                # 写操作需要额外权限
                if method in ['POST', 'PUT', 'DELETE']:
                    # 需要检查具体的BID写权限
                    return False
                return True
        
        return False