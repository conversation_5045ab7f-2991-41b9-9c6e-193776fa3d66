"""
认证服务
处理用户登录、登出、Token管理等
"""
import json
import logging
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
import aiomysql

from ..schemas.auth import LoginRequest, TokenResponse, LoginResponse, SuperAdminInit
from ..schemas.user import UserResponse, UserRole
from ..utils.password import verify_password, hash_password
from ..utils.jwt_handler import create_token_pair, verify_token, refresh_access_token
from .audit_service import AuditService

logger = logging.getLogger(__name__)


class AuthService:
    """认证服务类"""
    
    def __init__(self, db_pool):
        """
        初始化认证服务
        
        Args:
            db_pool: 数据库连接池
        """
        self.db_pool = db_pool
        self.audit_service = AuditService(db_pool)
    
    async def login(
        self, 
        login_data: LoginRequest,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> LoginResponse:
        """
        用户登录
        
        Args:
            login_data: 登录请求数据
            ip_address: 客户端IP地址
            user_agent: 用户代理
            
        Returns:
            LoginResponse: 登录响应
        """
        try:
            # 查询用户
            user = await self._get_user_by_username(login_data.username)
            
            if not user:
                # 尝试通过邮箱查询
                user = await self._get_user_by_email(login_data.username)
            
            if not user:
                # 记录失败的登录尝试
                await self.audit_service.log_login(
                    username=login_data.username,
                    success=False,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    error_message="用户不存在"
                )
                return LoginResponse(
                    success=False,
                    message="用户名或密码错误",
                    user=None,
                    token=None
                )
            
            # 检查用户状态
            if user['status'] != 'active':
                # 记录失败的登录尝试
                await self.audit_service.log_login(
                    username=user['username'],
                    user_id=user['id'],
                    success=False,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    error_message="账户状态异常: " + user['status']
                )
                return LoginResponse(
                    success=False,
                    message="账户已被禁用或未激活",
                    user=None,
                    token=None
                )
            
            # 检查账户是否过期
            if user.get('expire_time'):
                from datetime import datetime
                expire_time = user['expire_time']
                if isinstance(expire_time, str):
                    expire_time = datetime.strptime(expire_time, "%Y-%m-%d %H:%M:%S")
                if expire_time < datetime.now():
                    await self.audit_service.log_login(
                        username=user['username'],
                        user_id=user['id'],
                        success=False,
                        ip_address=ip_address,
                        user_agent=user_agent,
                        error_message="账户已过期"
                    )
                    return LoginResponse(
                        success=False,
                        message="账户已过期，请联系管理员续期",
                        user=None,
                        token=None
                    )
            
            # 验证密码
            if not verify_password(login_data.password, user['password']):
                # 更新失败登录次数
                await self._update_failed_login(user['id'])
                # 记录失败的登录尝试
                await self.audit_service.log_login(
                    username=user['username'],
                    user_id=user['id'],
                    success=False,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    error_message="密码错误"
                )
                return LoginResponse(
                    success=False,
                    message="用户名或密码错误",
                    user=None,
                    token=None
                )
            
            # 生成Token
            token_data = {
                "user_id": user['id'],
                "username": user['username'],
                "role": user['role']
            }
            tokens = create_token_pair(token_data)
            
            # 更新登录信息
            await self._update_login_info(user['id'])
            
            # 记录成功的登录
            await self.audit_service.log_login(
                username=user['username'],
                user_id=user['id'],
                success=True,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            # 创建会话记录
            if login_data.remember_me:
                await self._create_session(user['id'], tokens['access_token'], tokens['refresh_token'])
            
            # 获取新格式的权限数据（如果存在）
            from ..services.bid_permission_service import BidPermissionService
            bid_permission_service = BidPermissionService(self.db_pool)
            user_bid_permissions = await bid_permission_service.get_user_bid_permissions(user['username'])
            
            # 构建用户响应
            user_response = UserResponse(
                id=user['id'],
                username=user['username'],
                email=user['email'],
                real_name=user['real_name'],
                phone=user['phone'],
                department=user['department'],
                role=user['role'],
                account_type=user.get('account_type', 'merchant_user'),
                expire_time=user['expire_time'].strftime("%Y-%m-%d %H:%M:%S") if user.get('expire_time') else None,
                status=user['status'],
                quotas=user['quotas'] if user['quotas'] else 0,
                use_quotas=user['use_quotas'] if user['use_quotas'] else 0,
                user_bid_permissions=user_bid_permissions if user_bid_permissions else [],  # 新格式权限
                avatar_url=user['avatar_url'],
                last_login_time=user['last_login_time'].strftime("%Y-%m-%d %H:%M:%S") if user['last_login_time'] else None,
                login_count=user['login_count'],
                created_at=user['created_at'].strftime("%Y-%m-%d %H:%M:%S"),
                updated_at=user['updated_at'].strftime("%Y-%m-%d %H:%M:%S")
            )
            
            # 构建Token响应
            token_response = TokenResponse(
                access_token=tokens['access_token'],
                refresh_token=tokens['refresh_token'] if login_data.remember_me else None,
                token_type=tokens['token_type'],
                expires_in=tokens['expires_in']
            )
            
            return LoginResponse(
                success=True,
                message="登录成功",
                user=user_response,
                token=token_response
            )
            
        except Exception as e:
            logger.error(f"登录失败: {e}")
            return LoginResponse(
                success=False,
                message="登录失败，请稍后重试",
                user=None,
                token=None
            )
    
    async def logout(self, token: str, all_devices: bool = False) -> bool:
        """
        用户登出
        
        Args:
            token: 用户Token
            all_devices: 是否登出所有设备
            
        Returns:
            bool: 是否成功
        """
        try:
            # 验证Token
            payload = verify_token(token)
            if not payload:
                return False
            
            user_id = payload.get('user_id')
            
            if all_devices:
                # 使所有会话失效
                await self._invalidate_all_sessions(user_id)
            else:
                # 使当前会话失效
                await self._invalidate_session(token)
            
            return True
            
        except Exception as e:
            logger.error(f"登出失败: {e}")
            return False
    
    async def refresh_token(self, refresh_token: str) -> Optional[TokenResponse]:
        """
        刷新Token
        
        Args:
            refresh_token: 刷新Token
            
        Returns:
            TokenResponse: 新的Token响应
        """
        try:
            # 验证刷新Token
            payload = verify_token(refresh_token, token_type="refresh")
            if not payload:
                return None
            
            # 检查会话是否有效
            session = await self._get_session_by_refresh_token(refresh_token)
            if not session or not session['is_active']:
                return None
            
            # 获取用户信息
            user = await self._get_user_by_id(payload['user_id'])
            if not user or user['status'] != 'active':
                return None
            
            # 生成新的Token对
            token_data = {
                "user_id": user['id'],
                "username": user['username'],
                "role": user['role'],
                "bid_permissions": json.loads(user['bid_permissions']) if user['bid_permissions'] else None
            }
            tokens = create_token_pair(token_data)
            
            # 更新会话
            await self._update_session_tokens(
                session['id'],
                tokens['access_token'],
                tokens['refresh_token']
            )
            
            return TokenResponse(
                access_token=tokens['access_token'],
                refresh_token=tokens['refresh_token'],
                token_type=tokens['token_type'],
                expires_in=tokens['expires_in']
            )
            
        except Exception as e:
            logger.error(f"刷新Token失败: {e}")
            return None
    
    async def init_super_admin(self, init_data: SuperAdminInit) -> LoginResponse:
        """
        初始化超级管理员
        
        Args:
            init_data: 初始化数据
            
        Returns:
            LoginResponse: 登录响应
        """
        try:
            # 检查是否已存在超级管理员
            if await self._check_super_admin_exists():
                return LoginResponse(
                    success=False,
                    message="超级管理员已存在",
                    user=None,
                    token=None
                )
            
            # TODO: 验证初始化密钥（如果配置了）
            # if init_data.init_key != settings.SUPER_ADMIN_INIT_KEY:
            #     return LoginResponse(...)
            
            # 创建超级管理员
            password = hash_password(init_data.password)
            
            async with self.db_pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    sql = """
                    INSERT INTO member_report_users 
                    (username, email, password, real_name, role, status, 
                     created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, NOW(), NOW())
                    """
                    
                    await cursor.execute(sql, (
                        init_data.username,
                        init_data.email,
                        password,
                        init_data.real_name,
                        'super_admin',
                        'active'
                    ))
                    
                    user_id = cursor.lastrowid
                    await conn.commit()
            
            # 生成Token
            token_data = {
                "user_id": user_id,
                "username": init_data.username,
                "role": "super_admin",
                "bid_permissions": [{"bid": "*", "permissions": ["*"], "sid_list": ["*"]}]
            }
            tokens = create_token_pair(token_data)
            
            # 构建响应
            user_response = UserResponse(
                id=user_id,
                username=init_data.username,
                email=init_data.email,
                real_name=init_data.real_name,
                role=UserRole.SUPER_ADMIN,
                status="active",
                user_bid_permissions=[{"bid": "*", "permissions": ["*"], "sid_list": ["*"]}],  # 超级管理员默认所有权限
                login_count=0,
                created_at=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                updated_at=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            )
            
            token_response = TokenResponse(
                access_token=tokens['access_token'],
                refresh_token=tokens['refresh_token'],
                token_type=tokens['token_type'],
                expires_in=tokens['expires_in']
            )
            
            logger.info(f"超级管理员初始化成功: {init_data.username}")
            
            return LoginResponse(
                success=True,
                message="超级管理员初始化成功",
                user=user_response,
                token=token_response
            )
            
        except Exception as e:
            logger.error(f"初始化超级管理员失败: {e}")
            return LoginResponse(
                success=False,
                message="初始化失败，请稍后重试",
                user=None,
                token=None
            )
    
    # ========== 私有方法 ==========
    
    async def _get_user_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """通过用户名获取用户"""
        async with self.db_pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                sql = """
                SELECT * FROM member_report_users 
                WHERE username = %s AND deleted_at IS NULL
                """
                await cursor.execute(sql, (username,))
                return await cursor.fetchone()
    
    async def _get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """通过邮箱获取用户"""
        async with self.db_pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                sql = """
                SELECT * FROM member_report_users 
                WHERE email = %s AND deleted_at IS NULL
                """
                await cursor.execute(sql, (email,))
                return await cursor.fetchone()
    
    async def _get_user_by_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        """通过ID获取用户"""
        async with self.db_pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                sql = """
                SELECT * FROM member_report_users 
                WHERE id = %s AND deleted_at IS NULL
                """
                await cursor.execute(sql, (user_id,))
                return await cursor.fetchone()
    
    async def _update_login_info(self, user_id: int):
        """更新登录信息"""
        async with self.db_pool.acquire() as conn:
            async with conn.cursor() as cursor:
                sql = """
                UPDATE member_report_users 
                SET last_login_time = NOW(), 
                    login_count = login_count + 1,
                    failed_login_count = 0
                WHERE id = %s
                """
                await cursor.execute(sql, (user_id,))
                await conn.commit()
    
    async def _update_failed_login(self, user_id: int):
        """更新失败登录信息"""
        async with self.db_pool.acquire() as conn:
            async with conn.cursor() as cursor:
                sql = """
                UPDATE member_report_users 
                SET failed_login_count = failed_login_count + 1,
                    last_failed_login_time = NOW()
                WHERE id = %s
                """
                await cursor.execute(sql, (user_id,))
                await conn.commit()
    
    async def _create_session(self, user_id: int, access_token: str, refresh_token: str):
        """创建会话记录"""
        async with self.db_pool.acquire() as conn:
            async with conn.cursor() as cursor:
                sql = """
                INSERT INTO member_report_sessions 
                (user_id, token, refresh_token, expires_at, refresh_expires_at, is_active)
                VALUES (%s, %s, %s, %s, %s, 1)
                """
                
                expires_at = datetime.utcnow() + timedelta(minutes=30)
                refresh_expires_at = datetime.utcnow() + timedelta(days=7)
                
                await cursor.execute(sql, (
                    user_id,
                    access_token,
                    refresh_token,
                    expires_at,
                    refresh_expires_at
                ))
                await conn.commit()
    
    async def _get_session_by_refresh_token(self, refresh_token: str) -> Optional[Dict[str, Any]]:
        """通过刷新Token获取会话"""
        async with self.db_pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                sql = """
                SELECT * FROM member_report_sessions 
                WHERE refresh_token = %s AND is_active = 1
                """
                await cursor.execute(sql, (refresh_token,))
                return await cursor.fetchone()
    
    async def _update_session_tokens(self, session_id: int, access_token: str, refresh_token: str):
        """更新会话Token"""
        async with self.db_pool.acquire() as conn:
            async with conn.cursor() as cursor:
                sql = """
                UPDATE member_report_sessions 
                SET token = %s, refresh_token = %s, 
                    expires_at = %s, refresh_expires_at = %s,
                    updated_at = NOW()
                WHERE id = %s
                """
                
                expires_at = datetime.utcnow() + timedelta(minutes=30)
                refresh_expires_at = datetime.utcnow() + timedelta(days=7)
                
                await cursor.execute(sql, (
                    access_token,
                    refresh_token,
                    expires_at,
                    refresh_expires_at,
                    session_id
                ))
                await conn.commit()
    
    async def _invalidate_session(self, token: str):
        """使会话失效"""
        async with self.db_pool.acquire() as conn:
            async with conn.cursor() as cursor:
                sql = """
                UPDATE member_report_sessions 
                SET is_active = 0 
                WHERE token = %s
                """
                await cursor.execute(sql, (token,))
                await conn.commit()
    
    async def _invalidate_all_sessions(self, user_id: int):
        """使用户的所有会话失效"""
        async with self.db_pool.acquire() as conn:
            async with conn.cursor() as cursor:
                sql = """
                UPDATE member_report_sessions 
                SET is_active = 0 
                WHERE user_id = %s
                """
                await cursor.execute(sql, (user_id,))
                await conn.commit()
    
    async def _check_super_admin_exists(self) -> bool:
        """检查是否存在超级管理员"""
        async with self.db_pool.acquire() as conn:
            async with conn.cursor() as cursor:
                sql = """
                SELECT COUNT(*) as count
                FROM member_report_users 
                WHERE role = 'super_admin' AND deleted_at IS NULL
                """
                await cursor.execute(sql)
                result = await cursor.fetchone()
                return result[0] > 0