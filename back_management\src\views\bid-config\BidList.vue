<template>
  <div class="bid-list-container">
    <h2 class="page-title">BID配置管理</h2>
    
    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" @submit.prevent="handleSearch">
        <el-form-item label="BID">
          <el-input
            v-model="searchForm.bid"
            placeholder="请输入BID"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        
        <el-form-item label="名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入名称"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="全部状态"
            clearable
          >
            <el-option label="启用" value="active" />
            <el-option label="禁用" value="inactive" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><RefreshRight /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增BID
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- BID列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="bidList"
        style="width: 100%"
        row-key="id"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="bid" label="BID" width="120">
          <template #default="{ row }">
            <el-tag type="primary">{{ row.bid }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="bid_name" label="名称" min-width="150" />
        <el-table-column prop="business_type" label="业态" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.business_type" :type="getBusinessTypeTag(row.business_type)">
              {{ getBusinessTypeLabel(row.business_type) }}
            </el-tag>
            <span v-else style="color: #909399">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column label="SID列表" min-width="200">
          <template #default="{ row }">
            <div class="sid-list">
              <el-tag
                v-for="sid in (row.sid_list || []).slice(0, 3)"
                :key="sid"
                size="small"
                style="margin-right: 5px"
              >
                {{ sid }}
              </el-tag>
              <el-button
                v-if="(row.sid_list || []).length > 3"
                type="primary"
                text
                size="small"
                @click="showSidList(row)"
              >
                +{{ row.sid_list.length - 3 }}更多
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="320" fixed="right">
          <template #default="{ row }">
            <div style="display: flex; flex-wrap: nowrap; gap: 5px;">
              <el-button
                type="primary"
                size="small"
                text
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button
                type="info"
                size="small"
                text
                @click="handleViewStats(row)"
              >
                统计
              </el-button>
              <el-button
                :type="row.status === 'active' ? 'danger' : 'success'"
                size="small"
                text
                @click="handleToggleStatus(row)"
              >
                {{ row.status === 'active' ? '禁用' : '启用' }}
              </el-button>
              <el-button
                type="danger"
                size="small"
                text
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        style="margin-top: 20px"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </el-card>
    
    <!-- BID编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @closed="resetForm"
    >
      <el-form
        ref="bidFormRef"
        :model="bidForm"
        :rules="bidRules"
        label-width="100px"
      >
        <el-form-item label="BID" prop="bid">
          <el-input
            v-model="bidForm.bid"
            placeholder="请输入BID"
            :disabled="isEdit"
          />
        </el-form-item>
        
        <el-form-item label="名称" prop="name">
          <el-input
            v-model="bidForm.name"
            placeholder="请输入名称"
          />
        </el-form-item>
        
        <el-form-item label="业态" prop="business_type">
          <el-select
            v-model="bidForm.business_type"
            placeholder="请选择业态类型"
          >
            <el-option label="快餐" value="fast_food" />
            <el-option label="休闲餐" value="casual_dining" />
            <el-option label="正餐" value="fine_dining" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="bidForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述"
          />
        </el-form-item>
        
        <el-form-item label="SID列表" prop="sid_list">
          <el-select
            v-model="bidForm.sid_list"
            multiple
            filterable
            allow-create
            default-first-option
            placeholder="请输入或选择SID"
            style="width: 100%"
          >
            <el-option
              v-for="sid in sidOptions"
              :key="sid"
              :label="sid"
              :value="sid"
            />
          </el-select>
          <div style="margin-top: 5px; font-size: 12px; color: #909399">
            可以直接输入新的SID并按回车添加
          </div>
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="bidForm.status">
            <el-radio label="active">启用</el-radio>
            <el-radio label="inactive">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- SID列表对话框 -->
    <el-dialog
      v-model="sidDialogVisible"
      title="SID列表"
      width="500px"
    >
      <div class="sid-detail-list">
        <el-tag
          v-for="sid in currentSidList"
          :key="sid"
          size="large"
          style="margin: 5px"
        >
          {{ sid }}
        </el-tag>
      </div>
    </el-dialog>
    
    <!-- 统计对话框 -->
    <el-dialog
      v-model="statsDialogVisible"
      title="BID使用统计"
      width="600px"
    >
      <div v-if="currentStats">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="BID">
            {{ currentStats.bid }}
          </el-descriptions-item>
          <el-descriptions-item label="名称">
            {{ currentStats.bid_name }}
          </el-descriptions-item>
          <el-descriptions-item label="总配额">
            {{ currentStats.quota_limit || '无限制' }}
          </el-descriptions-item>
          <el-descriptions-item label="已使用">
            {{ currentStats.quota_used || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="使用率">
            <el-progress
              :percentage="getQuotaPercentage(currentStats)"
              :status="getQuotaStatus(currentStats)"
            />
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="currentStats.status === 'active' ? 'success' : 'danger'">
              {{ currentStats.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="今日查询">
            {{ currentStats.today_queries || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="本月查询">
            {{ currentStats.month_queries || 0 }}
          </el-descriptions-item>
        </el-descriptions>
        
        <el-divider />
        
        <h4>最近使用趋势</h4>
        <div style="text-align: center; padding: 20px; color: #909399">
          <el-icon :size="48"><DataAnalysis /></el-icon>
          <p>暂无图表数据</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { bidAPI } from '@/api/bid'
import { ElMessage, ElMessageBox } from 'element-plus'

// 搜索表单
const searchForm = reactive({
  bid: '',
  name: '',
  status: ''
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// BID列表
const bidList = ref([])
const loading = ref(false)

// 对话框
const dialogVisible = ref(false)
const dialogTitle = ref('新增BID')
const isEdit = ref(false)
const bidFormRef = ref()

// BID表单
const bidForm = reactive({
  id: null,
  bid: '',
  name: '',
  business_type: 'fast_food',
  description: '',
  sid_list: [],
  status: 'active'
})

// 表单验证规则
const bidRules = {
  bid: [
    { required: true, message: '请输入BID', trigger: 'blur' },
    { min: 1, max: 50, message: 'BID长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' },
    { min: 1, max: 100, message: '名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  business_type: [
    { required: true, message: '请选择业态类型', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// SID选项
const sidOptions = ref([])

// SID列表对话框
const sidDialogVisible = ref(false)
const currentSidList = ref([])

// 统计对话框
const statsDialogVisible = ref(false)
const currentStats = ref(null)

// 获取BID列表
const fetchBidList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      limit: pagination.pageSize,
      ...searchForm
    }
    
    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === '') {
        delete params[key]
      }
    })
    
    const response = await bidAPI.getBidConfigs(params)
    bidList.value = response.items || []
    pagination.total = response.total || 0
    
    // 收集所有SID作为选项
    const allSids = new Set()
    bidList.value.forEach(bid => {
      (bid.sid_list || []).forEach(sid => allSids.add(sid))
    })
    sidOptions.value = Array.from(allSids)
  } catch (error) {
    ElMessage.error('获取BID列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchBidList()
}

// 重置搜索
const handleReset = () => {
  searchForm.bid = ''
  searchForm.name = ''
  searchForm.status = ''
  handleSearch()
}

// 新增BID
const handleAdd = () => {
  isEdit.value = false
  dialogTitle.value = '新增BID'
  dialogVisible.value = true
}

// 编辑BID
const handleEdit = (row) => {
  isEdit.value = true
  dialogTitle.value = '编辑BID'
  Object.assign(bidForm, {
    id: row.id,
    bid: row.bid,
    name: row.bid_name || '',  // 使用bid_name字段
    business_type: row.business_type || null,
    description: row.description || '',
    sid_list: row.sid_list || [],
    status: row.status
  })
  dialogVisible.value = true
}

// 提交表单
const handleSubmit = async () => {
  const valid = await bidFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  try {
    if (isEdit.value) {
      await bidAPI.updateBidConfig(bidForm.id, {
        bid_name: bidForm.name,
        business_type: bidForm.business_type,
        description: bidForm.description,
        sid_list: bidForm.sid_list,
        status: bidForm.status
      })
      ElMessage.success('更新成功')
    } else {
      // 创建时需要转换字段名称
      const createData = {
        bid: bidForm.bid,
        bid_name: bidForm.name,
        business_type: bidForm.business_type,
        description: bidForm.description,
        sid_list: bidForm.sid_list
      }
      await bidAPI.createBidConfig(createData)
      ElMessage.success('创建成功')
    }
    dialogVisible.value = false
    fetchBidList()
  } catch (error) {
    ElMessage.error(error.response?.data?.detail || '操作失败')
  }
}

// 重置表单
const resetForm = () => {
  bidFormRef.value?.resetFields()
  Object.assign(bidForm, {
    id: null,
    bid: '',
    name: '',
    business_type: 'fast_food',
    description: '',
    sid_list: [],
    status: 'active'
  })
}

// 显示SID列表
const showSidList = (row) => {
  currentSidList.value = row.sid_list || []
  sidDialogVisible.value = true
}

// 查看统计
const handleViewStats = async (row) => {
  try {
    const stats = await bidAPI.getBidStatistics(row.id)
    currentStats.value = {
      ...row,
      ...stats,
      today_queries: stats.today_queries || 0,  // 使用实际数据或0
      month_queries: stats.month_queries || 0   // 使用实际数据或0
    }
    statsDialogVisible.value = true
  } catch (error) {
    ElMessage.error('获取统计数据失败')
  }
}

// 切换BID状态
const handleToggleStatus = async (row) => {
  const newStatus = row.status === 'active' ? 'inactive' : 'active'
  const action = newStatus === 'active' ? '启用' : '禁用'
  
  try {
    await ElMessageBox.confirm(
      `确定要${action}BID ${row.bid} 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await bidAPI.updateBidStatus(row.id, newStatus)
    ElMessage.success(`${action}成功`)
    fetchBidList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`${action}失败`)
    }
  }
}

// 删除BID
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除BID ${row.bid} 吗？删除后数据不可恢复！`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await bidAPI.deleteBidConfig(row.id)
    ElMessage.success('删除成功')
    fetchBidList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 分页大小改变
const handleSizeChange = (val) => {
  pagination.pageSize = val
  fetchBidList()
}

// 页码改变
const handlePageChange = (val) => {
  pagination.page = val
  fetchBidList()
}

// 获取配额百分比
const getQuotaPercentage = (row) => {
  if (!row.quota_limit || row.quota_limit === 0) return 0
  return Math.min(100, Math.round((row.quota_used || 0) / row.quota_limit * 100))
}

// 获取配额状态
const getQuotaStatus = (row) => {
  const percentage = getQuotaPercentage(row)
  if (percentage >= 90) return 'exception'
  if (percentage >= 70) return 'warning'
  return 'success'
}

// 格式化日期时间
const formatDateTime = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

// 获取业态标签
const getBusinessTypeLabel = (type) => {
  const typeMap = {
    'fast_food': '快餐',
    'casual_dining': '休闲餐',
    'fine_dining': '正餐'
  }
  return typeMap[type] || type
}

// 获取业态标签样式
const getBusinessTypeTag = (type) => {
  const tagMap = {
    'fast_food': 'success',
    'casual_dining': 'warning',
    'fine_dining': 'danger'
  }
  return tagMap[type] || 'info'
}

onMounted(() => {
  fetchBidList()
})
</script>

<style scoped>
.bid-list-container {
  padding: 20px;
}

.page-title {
  margin: 0 0 20px 0;
  font-size: 24px;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  min-height: 500px;
}

.sid-list {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.sid-detail-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>