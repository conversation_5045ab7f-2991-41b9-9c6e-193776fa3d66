# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
ENV/
env/
.venv
.ENV
.env
*.venv

# IDE
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# Vue/Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
.npm
.pnpm-store/

# Vue build files
/dist
/dist-ssr
*.local

# Editor directories and files
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 环境配置文件
.env
.env.local
.env.production
.env.development
*.env

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 测试覆盖率
coverage/
*.lcov
.nyc_output

# 系统文件
Thumbs.db
.DS_Store

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 临时文件
*.tmp
*.temp
.temp/
.tmp/

# 上传文件目录
uploads/
static/uploads/
public/uploads/

# 生成的文件
generated/
output/
exports/

# 配置文件（包含敏感信息）
config/local.json
config/production.json
settings.local.json

# SSL证书
*.pem
*.key
*.crt
*.cer

# 备份文件
*.bak
*.backup
backup/

# 缓存
.cache/
.parcel-cache/
.sass-cache/

# Mac系统文件
.DS_Store
.AppleDouble
.LSOverride

# Windows系统文件
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# JetBrains IDEs
.idea/
*.iml
*.iws
*.ipr
out/

# Visual Studio Code
.vscode/
.history/
*.code-workspace

# Jupyter Notebook
.ipynb_checkpoints/

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytest
.pytest_cache/

# 项目特定忽略
member-api/logs/
member-api/temp/
back_management/dist/
vue-element/dist/
*.pid
*.seed
*.pid.lock