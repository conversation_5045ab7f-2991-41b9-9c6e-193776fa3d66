"""
数据库初始化模块
负责检查和创建任务管理相关的数据库表
"""

import logging
from typing import Optional
from core.config import settings
from core.database import db

logger = logging.getLogger(__name__)


class TaskDatabaseInitializer:
    """任务数据库初始化器"""
    
    def __init__(self):
        self.is_beta = settings.ENVIRONMENT.lower() in ['beta', 'test', 'testing']
        self.table_suffix = '_beta' if self.is_beta else ''
        self.table_name = f'member_report_tasks{self.table_suffix}'
        
    async def check_database_connection(self) -> bool:
        """检查能否连接到task_management数据库"""
        try:
            async with db.get_task_management_connection() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("SELECT 1")
                    result = await cursor.fetchone()
                    if result:
                        logger.info(f"成功连接到数据库 {settings.TASK_MANAGEMENT_DB_NAME}")
                        return True
                    return False
        except Exception as e:
            logger.error(f"无法连接到数据库 {settings.TASK_MANAGEMENT_DB_NAME}: {e}")
            logger.error("请确保：")
            logger.error("1. 数据库服务正在运行")
            logger.error("2. 数据库配置正确（主机、端口、用户名、密码）")
            logger.error("3. 数据库已经创建")
            logger.error("4. 用户有访问权限")
            return False
    
    async def check_table_exists(self) -> bool:
        """检查任务表是否存在"""
        try:
            async with db.get_task_management_connection() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT COUNT(*) FROM information_schema.tables "
                        "WHERE table_schema = %s AND table_name = %s",
                        (settings.TASK_MANAGEMENT_DB_NAME, self.table_name)
                    )
                    result = await cursor.fetchone()
                    exists = result[0] > 0 if result else False
                    
                    if exists:
                        logger.info(f"表 {self.table_name} 已存在")
                    else:
                        logger.info(f"表 {self.table_name} 不存在，需要创建")
                    
                    return exists
        except Exception as e:
            logger.error(f"检查表存在性失败: {e}")
            return False
    
    def get_create_table_sql(self) -> str:
        """获取创建表的SQL语句"""
        return f"""
        CREATE TABLE IF NOT EXISTS `{self.table_name}` (
            -- 基础字段
            `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '任务ID，主键，唯一标识任务',
            `task_uuid` VARCHAR(36) UNIQUE NOT NULL COMMENT '任务UUID，全局唯一任务标识符',
            `user_id` VARCHAR(50) COMMENT '创建用户标识，存储username',
            `bid` VARCHAR(50) COMMENT 'BID代码，任务所属的业务标识',
            `sid` VARCHAR(255) COMMENT 'SID标识，子业务标识，用于细粒度权限控制',
            
            -- 任务信息
            `task_type` VARCHAR(50) COMMENT '任务类型，区分不同的任务处理类型',
            `task_name` VARCHAR(200) COMMENT '任务名称，PPT输出文件名称',
            `file_excel_size` BIGINT DEFAULT 0 COMMENT '生成表格大小，表格文件大小（用于统计）',
            `file_PPT_size` BIGINT DEFAULT 0 COMMENT '生成文件大小，PPT文件大小（用于统计）',
            `priority` TINYINT DEFAULT 1 COMMENT '任务优先级，范围1-5，默认1',
            
            -- 文件和结果
            `result_file_url` TEXT COMMENT '结果文件URL，OSS存储的分析结果文件地址',
            `ppt_oss_url` TEXT COMMENT 'PPT文件URL，生成的PPT演示文件地址',
            `excel_oss_url` TEXT COMMENT 'Excel文件URL，生成的Excel报表文件地址',
            `report_type` VARCHAR(50) COMMENT '报告类型，生成报告的格式类型',
            
            -- 状态和进度
            `status` ENUM('pending','processing','completed','failed','cancelled') 
                DEFAULT 'pending' COMMENT '任务状态，当前执行状态',
            `progress` TINYINT DEFAULT 0 COMMENT '任务进度，任务完成百分比，0-100',
            `current_step` VARCHAR(100) COMMENT '当前步骤，当前正在执行的处理步骤',
            
            -- 统计信息
            `task_start_ftime` VARCHAR(20) COMMENT '报告生成起始时间，例如：20250312',
            `task_end_ftime` VARCHAR(20) COMMENT '报告生成截至时间，例如：20250412',
            `tokens_used` INT DEFAULT 0 COMMENT 'Token消耗，AI处理消耗的Token数量',
            
            -- 时间字段
            `estimated_duration` INT COMMENT '预计时长，任务预计执行时间（分钟）',
            `actual_duration` INT COMMENT '实际时长，任务实际执行时间（分钟）',
            `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，任务创建时间戳',
            `started_at` DATETIME COMMENT '开始时间，任务开始执行时间戳',
            `completed_at` DATETIME COMMENT '完成时间，任务完成时间戳',
            
            -- 错误处理
            `error_info` JSON COMMENT '错误信息，错误详情和堆栈信息',
            `retry_count` TINYINT DEFAULT 0 COMMENT '重试次数，当前已重试次数',
            `max_retry_count` TINYINT DEFAULT 3 COMMENT '最大重试次数，允许的最大重试次数',
            
            -- 扩展功能
            `parent_task_id` INT COMMENT '父任务ID，关联的父任务',
            `notification_config` JSON COMMENT '通知配置，任务完成后的通知设置',
            `source` VARCHAR(50) COMMENT '任务来源，任务创建来源追踪',
            
            -- 索引
            INDEX `idx_task_uuid` (`task_uuid`),
            INDEX `idx_user_id` (`user_id`),
            INDEX `idx_bid` (`bid`),
            INDEX `idx_status` (`status`),
            INDEX `idx_priority_status` (`priority`, `status`),
            INDEX `idx_created_at` (`created_at`),
            INDEX `idx_parent_task_id` (`parent_task_id`)
            
            -- 外键约束（如果需要的话，可以取消注释）
            -- FOREIGN KEY (`parent_task_id`) REFERENCES `{self.table_name}` (`id`) ON DELETE SET NULL
            
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci 
        COMMENT='会员报告任务管理表'
        """
    
    async def create_table(self) -> bool:
        """创建任务表"""
        try:
            async with db.get_task_management_connection() as conn:
                async with conn.cursor() as cursor:
                    # 创建表
                    create_sql = self.get_create_table_sql()
                    await cursor.execute(create_sql)
                    await conn.commit()
                    
                    logger.info(f"表 {self.table_name} 创建成功")
                    
                    # 验证表创建成功
                    await cursor.execute(f"DESCRIBE `{self.table_name}`")
                    columns = await cursor.fetchall()
                    logger.info(f"表 {self.table_name} 包含 {len(columns)} 个字段")
                    
                    return True
                    
        except Exception as e:
            logger.error(f"创建表失败: {e}")
            return False
    
    async def initialize(self) -> bool:
        """执行完整的初始化流程"""
        try:
            logger.info(f"开始初始化任务管理表 (环境: {'Beta' if self.is_beta else '生产'})")
            
            # 1. 检查数据库连接
            if not await self.check_database_connection():
                logger.error("无法连接到任务管理数据库")
                return False
            
            # 2. 检查并创建表
            if not await self.check_table_exists():
                logger.info(f"表 {self.table_name} 不存在，开始创建...")
                if not await self.create_table():
                    logger.error("表创建失败")
                    return False
            else:
                logger.info(f"表 {self.table_name} 已存在，跳过创建")
            
            logger.info("任务管理表初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"任务管理表初始化失败: {e}")
            return False


# 全局初始化函数
async def init_task_database():
    """初始化任务管理数据库"""
    initializer = TaskDatabaseInitializer()
    return await initializer.initialize()


async def check_and_create_tables():
    """检查并创建表（向后兼容）"""
    return await init_task_database()