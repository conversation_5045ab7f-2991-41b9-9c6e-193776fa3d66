# ==============================================================================
# Vue前端应用配置
# ==============================================================================

# API基础URL配置
# 开发环境：http://127.0.0.1:8000/api
# 生产环境（域名）：https://api.yourdomain.com/api
# 生产环境（IP）：http://公网IP:8000/api
# 注意：使用公网IP时，格式为 http://公网IP:端口/api
VITE_API_BASE_URL=http://127.0.0.1:8000/api

# 应用名称
VITE_APP_TITLE=会员报表系统

# ==============================================================================
# 部署说明
# ==============================================================================
# 1. 复制此文件为 .env 并修改配置
# 2. 开发环境使用默认配置即可
# 3. 生产环境部署时：
#    - 修改 VITE_API_BASE_URL 为实际的API服务器地址
#    - 如使用域名：https://api.yourdomain.com/api
#    - 如使用IP：http://123.456.789.0:8000/api
# 4. 构建命令：npm run build
# 5. 部署dist目录到Web服务器（如Nginx）