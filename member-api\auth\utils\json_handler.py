"""
JSON字段处理工具
用于安全地处理数据库中的JSON字段
"""
import json
import logging
from typing import Any, Optional, Dict, List, Union
from datetime import datetime, date
from decimal import Decimal

logger = logging.getLogger(__name__)


class JSONHandler:
    """JSON字段处理器"""
    
    @staticmethod
    def safe_loads(json_str: Optional[Union[str, dict, list]]) -> Optional[Union[dict, list]]:
        """
        安全地解析JSON字符串
        
        Args:
            json_str: JSON字符串或已解析的对象
            
        Returns:
            解析后的Python对象，失败返回None
        """
        if json_str is None:
            return None
            
        # 如果已经是dict或list，直接返回
        if isinstance(json_str, (dict, list)):
            return json_str
            
        # 如果是空字符串，返回None
        if isinstance(json_str, str) and not json_str.strip():
            return None
            
        try:
            return json.loads(json_str)
        except (json.JSONDecodeError, TypeError) as e:
            logger.error(f"JSON解析失败: {e}, 原始数据: {json_str[:100] if json_str else 'None'}")
            return None
    
    @staticmethod
    def safe_dumps(obj: Optional[Union[dict, list]], default=None) -> Optional[str]:
        """
        安全地序列化Python对象为JSON字符串
        
        Args:
            obj: Python对象
            default: 默认值函数
            
        Returns:
            JSON字符串，失败返回None
        """
        if obj is None:
            return None
            
        # 如果是字符串，先尝试解析验证是否是有效JSON
        if isinstance(obj, str):
            try:
                json.loads(obj)
                return obj  # 已经是有效的JSON字符串
            except (json.JSONDecodeError, TypeError):
                # 不是有效的JSON，作为普通字符串处理
                return json.dumps(obj, default=default or JSONHandler._json_default)
                
        try:
            return json.dumps(
                obj, 
                ensure_ascii=False,  # 支持中文
                default=default or JSONHandler._json_default
            )
        except (TypeError, ValueError) as e:
            logger.error(f"JSON序列化失败: {e}")
            return None
    
    @staticmethod
    def _json_default(obj):
        """
        JSON序列化的默认处理函数
        
        Args:
            obj: 需要序列化的对象
            
        Returns:
            可序列化的对象
        """
        if isinstance(obj, (datetime, date)):
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            return float(obj)
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        else:
            return str(obj)
    
    @staticmethod
    def merge_json(base: Optional[Union[str, dict]], update: Optional[Union[str, dict]]) -> Optional[str]:
        """
        合并两个JSON对象
        
        Args:
            base: 基础JSON
            update: 要合并的JSON
            
        Returns:
            合并后的JSON字符串
        """
        base_obj = JSONHandler.safe_loads(base) or {}
        update_obj = JSONHandler.safe_loads(update) or {}
        
        if not isinstance(base_obj, dict) or not isinstance(update_obj, dict):
            logger.error("合并JSON失败：两个参数都必须是字典类型")
            return JSONHandler.safe_dumps(base_obj)
            
        # 深度合并
        merged = JSONHandler._deep_merge(base_obj, update_obj)
        return JSONHandler.safe_dumps(merged)
    
    @staticmethod
    def _deep_merge(dict1: dict, dict2: dict) -> dict:
        """
        深度合并两个字典
        
        Args:
            dict1: 基础字典
            dict2: 要合并的字典
            
        Returns:
            合并后的字典
        """
        result = dict1.copy()
        
        for key, value in dict2.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = JSONHandler._deep_merge(result[key], value)
            else:
                result[key] = value
                
        return result
    
    @staticmethod
    def validate_json_schema(json_obj: Union[str, dict], schema: dict) -> bool:
        """
        验证JSON对象是否符合指定的模式
        
        Args:
            json_obj: JSON对象
            schema: 期望的模式
            
        Returns:
            是否有效
        """
        obj = JSONHandler.safe_loads(json_obj)
        if obj is None:
            return False
            
        # 简单的模式验证（可以扩展为使用jsonschema库）
        return JSONHandler._validate_schema(obj, schema)
    
    @staticmethod
    def _validate_schema(obj: Any, schema: Any) -> bool:
        """
        递归验证对象模式
        
        Args:
            obj: 要验证的对象
            schema: 模式定义
            
        Returns:
            是否有效
        """
        if isinstance(schema, type):
            return isinstance(obj, schema)
        elif isinstance(schema, dict):
            if not isinstance(obj, dict):
                return False
            for key, value_schema in schema.items():
                if key not in obj:
                    return False
                if not JSONHandler._validate_schema(obj[key], value_schema):
                    return False
            return True
        elif isinstance(schema, list):
            if not isinstance(obj, list):
                return False
            if len(schema) > 0:
                # 验证列表中的每个元素
                for item in obj:
                    if not JSONHandler._validate_schema(item, schema[0]):
                        return False
            return True
        else:
            return obj == schema
    
    @staticmethod
    def extract_path(json_obj: Union[str, dict], path: str, default=None) -> Any:
        """
        从JSON对象中提取指定路径的值
        
        Args:
            json_obj: JSON对象
            path: 路径，如 "user.profile.name"
            default: 默认值
            
        Returns:
            提取的值
        """
        obj = JSONHandler.safe_loads(json_obj)
        if obj is None:
            return default
            
        try:
            keys = path.split('.')
            result = obj
            for key in keys:
                if isinstance(result, dict):
                    result = result.get(key)
                elif isinstance(result, list) and key.isdigit():
                    index = int(key)
                    result = result[index] if index < len(result) else None
                else:
                    return default
                    
                if result is None:
                    return default
                    
            return result
        except Exception as e:
            logger.error(f"提取JSON路径失败: {e}, 路径: {path}")
            return default
    
    @staticmethod
    def set_path(json_obj: Union[str, dict], path: str, value: Any) -> Optional[str]:
        """
        设置JSON对象中指定路径的值
        
        Args:
            json_obj: JSON对象
            path: 路径
            value: 要设置的值
            
        Returns:
            更新后的JSON字符串
        """
        obj = JSONHandler.safe_loads(json_obj) or {}
        if not isinstance(obj, dict):
            logger.error("设置JSON路径失败：对象必须是字典类型")
            return JSONHandler.safe_dumps(obj)
            
        try:
            keys = path.split('.')
            current = obj
            
            # 导航到目标位置的父节点
            for key in keys[:-1]:
                if key not in current:
                    current[key] = {}
                current = current[key]
                
            # 设置值
            current[keys[-1]] = value
            
            return JSONHandler.safe_dumps(obj)
        except Exception as e:
            logger.error(f"设置JSON路径失败: {e}, 路径: {path}")
            return JSONHandler.safe_dumps(obj)