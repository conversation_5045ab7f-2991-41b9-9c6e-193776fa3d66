/**
 * 认证状态管理
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref(null)
  const token = ref(null)
  const refreshToken = ref(null)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const userRole = computed(() => user.value?.role || null)
  const isSuperAdmin = computed(() => userRole.value === 'super_admin')
  const isAdmin = computed(() => ['super_admin', 'admin'].includes(userRole.value))
  
  // 获取用户权限
  const bidPermissions = computed(() => user.value?.bid_permissions || [])
  const sidFilter = computed(() => user.value?.sid_filter || {})

  // 初始化：从localStorage恢复状态
  const initializeAuth = () => {
    const storedToken = localStorage.getItem('access_token')
    const storedRefreshToken = localStorage.getItem('refresh_token')
    const storedUser = localStorage.getItem('user_info')
    
    // 清理无效的token
    if (storedToken && (storedToken === 'null' || storedToken === 'undefined' || storedToken === '')) {
      console.log('清理无效的access token')
      localStorage.removeItem('access_token')
    } else if (storedToken) {
      token.value = storedToken
    }
    
    if (storedRefreshToken && (storedRefreshToken === 'null' || storedRefreshToken === 'undefined' || storedRefreshToken === '')) {
      console.log('清理无效的refresh token')
      localStorage.removeItem('refresh_token')
    } else if (storedRefreshToken) {
      refreshToken.value = storedRefreshToken
    }
    
    if (storedUser && storedUser !== 'null' && storedUser !== 'undefined') {
      try {
        user.value = JSON.parse(storedUser)
      } catch (e) {
        console.error('解析用户信息失败:', e)
        localStorage.removeItem('user_info')
      }
    }
  }

  // 设置用户信息
  const setUser = (userInfo) => {
    user.value = userInfo
    if (userInfo) {
      localStorage.setItem('user_info', JSON.stringify(userInfo))
    } else {
      localStorage.removeItem('user_info')
    }
  }

  // 设置Token
  const setToken = (tokenData) => {
    if (tokenData) {
      token.value = tokenData.access_token
      refreshToken.value = tokenData.refresh_token
      
      localStorage.setItem('access_token', tokenData.access_token)
      if (tokenData.refresh_token) {
        localStorage.setItem('refresh_token', tokenData.refresh_token)
      }
    } else {
      token.value = null
      refreshToken.value = null
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
    }
  }

  // 清除认证信息
  const clearAuth = () => {
    user.value = null
    token.value = null
    refreshToken.value = null
    
    localStorage.removeItem('user_info')
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
  }

  // 退出登录
  const logout = () => {
    clearAuth()
  }

  // 检查BID权限
  const hasBidPermission = (bid, permission = 'read') => {
    if (isSuperAdmin.value) return true
    
    const permissions = bidPermissions.value
    for (const perm of permissions) {
      if (perm.bid === '*' || perm.bid === bid) {
        const perms = perm.permissions || []
        if (perms.includes('*') || perms.includes(permission)) {
          return true
        }
      }
    }
    
    return false
  }

  // 检查SID权限
  const hasSidPermission = (bid, sid) => {
    if (isSuperAdmin.value) return true
    
    const filter = sidFilter.value
    
    // 检查特定BID下的SID权限
    if (filter[bid]) {
      const sids = filter[bid]
      if (sids.includes('*') || sids.includes(sid)) {
        return true
      }
    }
    
    // 检查通配符BID
    if (filter['*']) {
      const sids = filter['*']
      if (sids.includes('*') || sids.includes(sid)) {
        return true
      }
    }
    
    return false
  }

  // 获取可访问的BID列表
  const getAccessibleBids = () => {
    if (isSuperAdmin.value) return ['*']
    
    const bids = new Set()
    bidPermissions.value.forEach(perm => {
      bids.add(perm.bid)
    })
    
    return Array.from(bids)
  }

  // 获取BID下可访问的SID列表
  const getAccessibleSids = (bid) => {
    if (isSuperAdmin.value) return ['*']
    
    const filter = sidFilter.value
    const sids = new Set()
    
    if (filter[bid]) {
      filter[bid].forEach(sid => sids.add(sid))
    }
    
    if (filter['*']) {
      filter['*'].forEach(sid => sids.add(sid))
    }
    
    return Array.from(sids)
  }

  return {
    // 状态
    user,
    token,
    refreshToken,
    
    // 计算属性
    isLoggedIn,
    userRole,
    isSuperAdmin,
    isAdmin,
    bidPermissions,
    sidFilter,
    
    // 方法
    initializeAuth,
    setUser,
    setToken,
    clearAuth,
    logout,
    hasBidPermission,
    hasSidPermission,
    getAccessibleBids,
    getAccessibleSids
  }
})

// 导出store定义，不再自动创建实例
export default useAuthStore