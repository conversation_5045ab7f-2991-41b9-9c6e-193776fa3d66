# -*- coding: utf-8 -*-
"""
Excel报告生成器
负责从CSV文件中聚合数据并生成包含同比环比分析的Excel文件
"""

import pandas as pd
import logging
import json
import os
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import datetime
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows

logger = logging.getLogger(__name__)


class ExcelGenerator:
    """Excel报告生成器"""
    
    def __init__(self, session_dir: Path):
        """
        初始化Excel生成器
        
        Args:
            session_dir: 会话目录，包含所有CSV文件
        """
        self.session_dir = Path(session_dir)
        self.workbook = None
        self.csv_data = {}
        self.metadata = {}
        
        logger.info(f"初始化Excel生成器 - session_dir: {session_dir}")
    
    def load_csv_data(self) -> Dict[str, pd.DataFrame]:
        """
        加载所有CSV文件数据
        
        Returns:
            Dict: 文件名到DataFrame的映射
        """
        csv_files = list(self.session_dir.glob("*_data.csv"))
        
        for csv_file in csv_files:
            try:
                # 读取CSV文件
                df = pd.read_csv(csv_file, encoding='utf-8-sig')
                file_key = csv_file.stem.replace('_data', '')
                self.csv_data[file_key] = df
                
                # 元数据不再从JSON文件加载，直接使用CSV数据
                
                logger.info(f"加载CSV数据成功: {csv_file.name}, 行数: {len(df)}")
                
            except Exception as e:
                logger.error(f"加载CSV文件失败 {csv_file}: {e}")
        
        logger.info(f"共加载 {len(self.csv_data)} 个CSV文件")
        return self.csv_data
    
    async def generate_excel_with_comparison(self, output_filename: str) -> str:
        """
        生成包含同比环比分析的Excel文件
        
        Args:
            output_filename: 输出文件名
            
        Returns:
            str: 生成的Excel文件路径
        """
        try:
            # 加载所有CSV数据
            self.load_csv_data()
            
            if not self.csv_data:
                logger.warning("没有找到任何CSV数据文件")
                return ""
            
            # 创建工作簿
            self.workbook = Workbook()
            
            # 删除默认的工作表
            if 'Sheet' in self.workbook.sheetnames:
                self.workbook.remove(self.workbook['Sheet'])
            
            # 导入同比环比计算器
            from .ComparisonCalculator import ComparisonCalculator
            calculator = ComparisonCalculator()
            
            # 按类型组织数据并创建工作表
            sheet_configs = self._organize_data_by_type()
            
            for sheet_name, config in sheet_configs.items():
                self._create_worksheet(sheet_name, config, calculator)
            
            # 保存Excel文件
            output_path = self.session_dir / output_filename
            self.workbook.save(output_path)
            
            logger.info(f"Excel文件生成成功: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"生成Excel文件失败: {e}")
            import traceback
            traceback.print_exc()
            return ""
    
    def _organize_data_by_type(self) -> Dict[str, Dict]:
        """
        按类型组织数据，准备创建工作表
        合并去年和今年的数据到同一个工作表
        
        Returns:
            Dict: 工作表配置
        """
        sheet_configs = {}
        
        # 新增会员数据 - 合并去年和今年
        last_year = self.csv_data.get('new_member_add_last_year')
        this_year = self.csv_data.get('new_member_add_this_year')
        if last_year is not None or this_year is not None:
            sheet_configs['新增会员分析'] = {
                'last_year_data': last_year,
                'this_year_data': this_year,
                'value_columns': ['新增会员', '取关会员', '取关占比'],
                'include_comparison': True
            }
        
        # 会员消费数据 - 合并去年和今年
        last_year = self.csv_data.get('member_consumption_last_year')
        this_year = self.csv_data.get('member_consumption_this_year')
        if last_year is not None or this_year is not None:
            sheet_configs['会员消费分析'] = {
                'last_year_data': last_year,
                'this_year_data': this_year,
                'value_columns': ['消费金额', '储值消费金额', '现金消费金额'],
                'include_comparison': True
            }
        
        # 平均消费数据 - 合并去年和今年
        last_year = self.csv_data.get('avg_consumption_last_year')
        this_year = self.csv_data.get('avg_consumption_this_year')
        if last_year is not None or this_year is not None:
            sheet_configs['平均消费分析'] = {
                'last_year_data': last_year,
                'this_year_data': this_year,
                'value_columns': ['现金消费笔数', '储值消费笔数', '消费人数', '总消费笔数', '单均消费', '人均贡献'],
                'include_comparison': True
            }
        
        # 消费数量数据 - 合并去年和今年
        last_year = self.csv_data.get('consumption_num_last_year')
        this_year = self.csv_data.get('consumption_num_this_year')
        if last_year is not None or this_year is not None:
            sheet_configs['消费数量分析'] = {
                'last_year_data': last_year,
                'this_year_data': this_year,
                'value_columns': ['消费人数', '订单数量', '充值笔数', '消费频次'],
                'include_comparison': True
            }
        
        # 会员充值数据 - 合并去年和今年
        last_year = self.csv_data.get('member_charge_last_year')
        this_year = self.csv_data.get('member_charge_this_year')
        if last_year is not None or this_year is not None:
            sheet_configs['会员充值分析'] = {
                'last_year_data': last_year,
                'this_year_data': this_year,
                'value_columns': ['充值金额', '消耗储值', '留存率'],
                'include_comparison': True
            }
        
        # 首次充值消费人数 - 合并去年和今年
        last_year = self.csv_data.get('first_member_number_last_year')
        this_year = self.csv_data.get('first_member_number_this_year')
        if last_year is not None or this_year is not None:
            sheet_configs['首次充值消费人数'] = {
                'last_year_data': last_year,
                'this_year_data': this_year,
                'value_columns': ['首次充值人数', '再次充值人数', '首次消费人数', '再次消费人数'],
                'include_comparison': True
            }
        
        # 首次充值消费金额 - 合并去年和今年
        last_year = self.csv_data.get('first_charge_last_year')
        this_year = self.csv_data.get('first_charge_this_year')
        if last_year is not None or this_year is not None:
            sheet_configs['首次充值消费金额'] = {
                'last_year_data': last_year,
                'this_year_data': this_year,
                'value_columns': ['首次充值金额', '再次充值金额', '首次消费金额', '再次消费金额'],
                'include_comparison': True
            }
        
        # 等级分析数据（不需要同比环比）
        if 'level_consumption' in self.csv_data:
            sheet_configs['会员等级消费'] = {
                'data': self.csv_data.get('level_consumption'),
                'include_comparison': False
            }
        
        if 'level_order' in self.csv_data:
            sheet_configs['会员等级订单'] = {
                'data': self.csv_data.get('level_order'),
                'include_comparison': False
            }
        
        if 'level_number' in self.csv_data:
            sheet_configs['会员等级人数'] = {
                'data': self.csv_data.get('level_number'),
                'include_comparison': False
            }
        
        # 充值档位分析
        if 'charge_distribution' in self.csv_data:
            sheet_configs['充值档位分布'] = {
                'data': self.csv_data.get('charge_distribution'),
                'include_comparison': False
            }
        
        if 'charge_frequency_distribution' in self.csv_data:
            sheet_configs['充值频次分布'] = {
                'data': self.csv_data.get('charge_frequency_distribution'),
                'include_comparison': False
            }
        
        # 积分变化分析 - 合并去年和今年
        last_year = self.csv_data.get('credit_change_last_year')
        this_year = self.csv_data.get('credit_change_this_year')
        if last_year is not None or this_year is not None:
            sheet_configs['积分变化分析'] = {
                'last_year_data': last_year,
                'this_year_data': this_year,
                'value_columns': ['积分获得', '积分使用'],
                'include_comparison': True
            }
        
        if 'credit_distribution' in self.csv_data:
            sheet_configs['积分分布'] = {
                'data': self.csv_data.get('credit_distribution'),
                'include_comparison': False
            }
        
        return sheet_configs
    
    def _create_worksheet(self, sheet_name: str, config: Dict, calculator):
        """
        创建工作表
        
        Args:
            sheet_name: 工作表名称
            config: 工作表配置
            calculator: 同比环比计算器
        """
        try:
            ws = self.workbook.create_sheet(title=sheet_name)
            
            if config.get('include_comparison', False):
                # 需要同比环比的数据 - 合并显示
                self._create_combined_comparison_worksheet(ws, config, calculator)
            else:
                # 不需要同比环比的数据
                self._create_simple_worksheet(ws, config)
            
            # 格式化工作表
            self._format_worksheet(ws)
            
            logger.info(f"创建工作表成功: {sheet_name}")
            
        except Exception as e:
            logger.error(f"创建工作表失败 {sheet_name}: {e}")
    
    def _create_combined_comparison_worksheet(self, ws, config: Dict, calculator):
        """
        创建包含同比环比的合并工作表（纵向布局）
        第一行为时间（月份），从上往下依次为数据，包含年份区分
        
        Args:
            ws: 工作表对象
            config: 配置信息
            calculator: 计算器
        """
        last_year_data = config.get('last_year_data')
        this_year_data = config.get('this_year_data')
        value_columns = config.get('value_columns', [])
        
        # 获取年份（从数据中提取或使用默认值）
        import datetime
        current_year = datetime.datetime.now().year
        last_year = current_year - 1
        
        # 准备月份标题行
        months = ['1月', '2月', '3月', '4月', '5月', '6月', 
                  '7月', '8月', '9月', '10月', '11月', '12月']
        
        # 第一行：时间标题
        header_row = ['指标'] + months
        ws.append(header_row)
        
        # 为每个数值列创建数据行
        for col in value_columns:
            # 获取去年和今年的数据
            last_year_values = []
            this_year_values = []
            
            for i in range(12):
                # 去年数据
                last_val = 0
                if last_year_data is not None and not last_year_data.empty and i < len(last_year_data):
                    try:
                        if col in last_year_data.columns:
                            last_val = last_year_data.iloc[i][col]
                        else:
                            eng_col = self._get_english_column_name(col)
                            if eng_col and eng_col in last_year_data.columns:
                                last_val = last_year_data.iloc[i][eng_col]
                    except Exception as e:
                        logger.debug(f"获取去年数据失败: {e}")
                        last_val = 0
                last_year_values.append(last_val)
                
                # 今年数据
                this_val = 0
                if this_year_data is not None and not this_year_data.empty and i < len(this_year_data):
                    try:
                        if col in this_year_data.columns:
                            this_val = this_year_data.iloc[i][col]
                        else:
                            eng_col = self._get_english_column_name(col)
                            if eng_col and eng_col in this_year_data.columns:
                                this_val = this_year_data.iloc[i][eng_col]
                    except Exception as e:
                        logger.debug(f"获取今年数据失败: {e}")
                        this_val = 0
                this_year_values.append(this_val)
            
            # 添加去年数据行
            last_year_row = [f'{last_year}年{col}'] + last_year_values
            ws.append(last_year_row)
            
            # 添加今年数据行
            this_year_row = [f'{current_year}年{col}'] + this_year_values
            ws.append(this_year_row)
            
            # 计算并添加同比行（今年vs去年）
            yoy_row = [f'{col}同比(%)']
            for i in range(12):
                if last_year_values[i] != 0:
                    yoy = ((this_year_values[i] - last_year_values[i]) / last_year_values[i]) * 100
                    yoy_row.append(f"{yoy:.2f}%")
                else:
                    yoy_row.append("N/A" if this_year_values[i] == 0 else "新增")
            ws.append(yoy_row)
            
            # 计算并添加今年环比行（本月vs上月）
            this_year_mom_row = [f'{current_year}年{col}环比(%)']
            for i in range(12):
                if i == 0:
                    this_year_mom_row.append("-")
                else:
                    if this_year_values[i-1] != 0:
                        mom = ((this_year_values[i] - this_year_values[i-1]) / this_year_values[i-1]) * 100
                        this_year_mom_row.append(f"{mom:.2f}%")
                    else:
                        this_year_mom_row.append("N/A" if this_year_values[i] == 0 else "新增")
            ws.append(this_year_mom_row)
            
            # 计算并添加去年环比行（本月vs上月）
            last_year_mom_row = [f'{last_year}年{col}环比(%)']
            for i in range(12):
                if i == 0:
                    last_year_mom_row.append("-")
                else:
                    if last_year_values[i-1] != 0:
                        mom = ((last_year_values[i] - last_year_values[i-1]) / last_year_values[i-1]) * 100
                        last_year_mom_row.append(f"{mom:.2f}%")
                    else:
                        last_year_mom_row.append("N/A" if last_year_values[i] == 0 else "新增")
            ws.append(last_year_mom_row)
            
            # 添加空行分隔不同指标
            ws.append([])
    
    def _get_english_column_name(self, chinese_name: str) -> str:
        """
        获取中文列名对应的英文列名
        
        Args:
            chinese_name: 中文列名
            
        Returns:
            str: 英文列名
        """
        mapping = {
            '新增会员': 'new_members',
            '取关会员': 'new_unfollow_members',
            '取关占比': 'unfollow_rate',
            '消费金额': 'total_actual_amount',
            '储值消费金额': 'prepay_actual_amount',
            '现金消费金额': 'actual_amount',
            '现金消费笔数': 'cash_consume_count',
            '储值消费笔数': 'prepay_consume_count',
            '消费人数': 'consume_users',
            '总消费笔数': 'total_consume_count',
            '单均消费': 'avg_consume_amount',
            '人均贡献': 'avg_contribution',
            '订单数量': 'total_consume_count',
            '充值笔数': 'charge_count',
            '消费频次': 'consume_frequency',
            '充值金额': 'charge_amount',
            '消耗储值': 'period_charge_amount_unused',
            '留存率': 'retention_rate',
            '首次充值人数': 'first_charge_count',
            '再次充值人数': 'repeat_charge_count',
            '首次消费人数': 'first_consume_count',
            '再次消费人数': 'repeat_consume_count',
            '首次充值金额': 'first_charge_amount',
            '再次充值金额': 'repeat_charge_amount',
            '首次消费金额': 'first_consume_amount',
            '再次消费金额': 'repeat_consume_amount',
            '积分获得': 'credit_reward',
            '积分使用': 'credit_consume'
        }
        return mapping.get(chinese_name, chinese_name)
    
    def _create_simple_worksheet(self, ws, config: Dict):
        """
        创建简单工作表（不需要同比环比）
        
        Args:
            ws: 工作表对象
            config: 配置信息
        """
        data = config.get('data')
        
        if data is not None and not data.empty:
            # 将列名转换为中文
            chinese_columns = self._translate_columns(data.columns)
            data.columns = chinese_columns
            
            # 直接将DataFrame写入工作表
            for row in dataframe_to_rows(data, index=False, header=True):
                ws.append(row)
    
    def _translate_columns(self, columns: List[str]) -> List[str]:
        """
        将英文列名转换为中文
        
        Args:
            columns: 原始列名列表
            
        Returns:
            List[str]: 中文列名列表
        """
        translation = {
            'level': '会员等级',
            'amount': '金额',
            'percentage': '百分比',
            'count': '数量',
            'members': '人数',
            'orders': '订单数',
            'range': '档位',
            'frequency': '频次'
        }
        
        result = []
        for col in columns:
            # 尝试翻译
            translated = col
            for eng, chn in translation.items():
                if eng in col.lower():
                    translated = translated.replace(eng, chn)
            result.append(translated)
        
        return result
    
    def _format_worksheet(self, ws):
        """
        格式化工作表
        
        Args:
            ws: 工作表对象
        """
        # 设置标题行样式
        header_font = Font(bold=True, color="FFFFFF", size=11)
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
        
        # 设置边框
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 应用样式到标题行
        for cell in ws[1]:
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = thin_border
        
        # 设置列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if cell.value:
                        # 中文字符算2个长度
                        length = sum(2 if ord(c) > 127 else 1 for c in str(cell.value))
                        max_length = max(max_length, length)
                except:
                    pass
            
            adjusted_width = min(max_length * 1.2 + 2, 30)
            ws.column_dimensions[column_letter].width = adjusted_width
        
        # 设置数据区域的对齐和边框
        for row in ws.iter_rows(min_row=2):
            for cell in row:
                cell.alignment = Alignment(horizontal="center", vertical="center")
                cell.border = thin_border
                
                # 数字格式化
                if isinstance(cell.value, (int, float)):
                    if '金额' in str(ws.cell(1, cell.column).value):
                        cell.number_format = '#,##0.00'
                    elif '百分比' in str(ws.cell(1, cell.column).value) or '占比' in str(ws.cell(1, cell.column).value):
                        cell.number_format = '0.00%'