import request from './request'

/**
 * BID申请审批相关API
 */

/**
 * 获取待审批的BID申请列表
 */
export function getPendingApplications(params) {
  return request({
    url: '/admin/bid-approvals/pending',
    method: 'get',
    params
  })
}

/**
 * 批准BID申请
 */
export function approveApplication(bid, data) {
  return request({
    url: `/admin/bid-approvals/${bid}/approve`,
    method: 'post',
    data
  })
}

/**
 * 拒绝BID申请
 */
export function rejectApplication(bid, data) {
  return request({
    url: `/admin/bid-approvals/${bid}/reject`,
    method: 'post',
    data
  })
}

/**
 * 获取审批历史
 */
export function getApprovalHistory(params) {
  return request({
    url: '/admin/bid-approvals/history',
    method: 'get',
    params
  })
}

export default {
  getPendingApplications,
  approveApplication,
  rejectApplication,
  getApprovalHistory
}