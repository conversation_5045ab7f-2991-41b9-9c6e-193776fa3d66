# 会员报表系统部署指南

## 🚀 快速启动

### 1. 环境准备

确保您的开发环境已安装：
- Node.js (版本 16.0 或更高)
- npm 或 yarn 包管理器
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 2. 项目安装

```bash
# 克隆项目
git clone <repository-url>

# 进入项目目录
cd member_report_test/vue-element

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 3. 访问系统

开发服务器启动后，在浏览器中访问：
- 本地地址：`http://localhost:5173`
- 网络地址：`http://[您的IP]:5173`

## 🔧 开发环境配置

### 项目结构检查

确保项目结构如下：
```
member_report_test/
├── constant.py                    # 数据常量定义
├── vue-element/                   # 前端项目目录
│   ├── src/
│   │   ├── components/
│   │   │   ├── modules/           # 数据模块组件
│   │   │   └── reports/           # 报告分析组件
│   │   ├── views/
│   │   │   ├── MemberDataQuery.vue
│   │   │   └── MemberDataReport.vue
│   │   ├── data/
│   │   │   └── simpledata.js     # 后端开发数据结构
│   │   ├── App.vue
│   │   ├── main.js
│   │   └── router.js
│   ├── package.json
│   └── README.md
└── member-api/                    # 后端API目录
```

### 常见问题解决

#### 1. 依赖安装失败
```bash
# 清除npm缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install
```

#### 2. 端口冲突
如果5173端口被占用，修改`vite.config.js`：
```javascript
export default {
  server: {
    port: 3000  // 改为其他端口
  }
}
```

#### 3. 路由404问题
确保开发服务器配置了history模式支持：
```javascript
// vite.config.js
export default {
  server: {
    historyApiFallback: true
  }
}
```

## 📦 生产环境部署

### 1. 构建生产版本

```bash
# 构建项目
npm run build

# 构建完成后，dist目录包含所有静态文件
```

### 2. 部署到静态服务器

#### 使用 Nginx

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://your-backend-server;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

#### 使用 Apache

```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /path/to/dist
    
    <Directory /path/to/dist>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>
    
    # 支持Vue Router历史模式
    RewriteEngine On
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule . /index.html [L]
</VirtualHost>
```

### 3. 部署到CDN

对于大规模部署，推荐使用CDN：

1. 将`dist`目录内容上传到CDN
2. 配置CDN回源规则
3. 设置缓存策略

```javascript
// 生产环境配置示例
export default {
  base: 'https://your-cdn.com/member-report/',
  build: {
    assetsDir: 'assets',
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      }
    }
  }
}
```

## 🐳 Docker部署

### 1. 创建Dockerfile

```dockerfile
# 构建阶段
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

# 生产阶段
FROM nginx:alpine

# 复制构建结果
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 2. 创建nginx.conf

```nginx
events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        location / {
            try_files $uri $uri/ /index.html;
        }

        location /api {
            proxy_pass http://backend:8000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
}
```

### 3. 构建和运行

```bash
# 构建Docker镜像
docker build -t member-report-frontend .

# 运行容器
docker run -p 80:80 member-report-frontend
```

## 🔧 环境变量配置

### 开发环境 (.env.development)

```env
VITE_API_BASE_URL=http://localhost:8000
VITE_APP_TITLE=会员报表系统 (开发环境)
VITE_ENABLE_MOCK=true
```

### 生产环境 (.env.production)

```env
VITE_API_BASE_URL=https://api.your-domain.com
VITE_APP_TITLE=会员报表系统
VITE_ENABLE_MOCK=false
```

## 📊 性能优化

### 1. 代码分割

```javascript
// 路由懒加载
const routes = [
  {
    path: '/query',
    component: () => import('./views/MemberDataQuery.vue')
  },
  {
    path: '/report',
    component: () => import('./views/MemberDataReport.vue')
  }
]
```

### 2. 资源压缩

```javascript
// vite.config.js
import { defineConfig } from 'vite'
import { resolve } from 'path'

export default defineConfig({
  build: {
    rollupOptions: {
      external: ['echarts'],
      output: {
        globals: {
          echarts: 'echarts'
        }
      }
    }
  }
})
```

### 3. 缓存策略

```javascript
// Service Worker缓存配置
const CACHE_NAME = 'member-report-v1.0.0'
const urlsToCache = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css'
]
```

## 🚨 监控和日志

### 1. 错误监控

```javascript
// 全局错误处理
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error)
  // 发送错误到监控服务
})

// Vue错误处理
app.config.errorHandler = (err, instance, info) => {
  console.error('Vue error:', err, info)
  // 发送错误到监控服务
}
```

### 2. 性能监控

```javascript
// 性能监控
const observer = new PerformanceObserver((list) => {
  for (const entry of list.getEntries()) {
    console.log('Performance:', entry.name, entry.duration)
  }
})
observer.observe({ entryTypes: ['navigation', 'paint'] })
```

## 🔒 安全配置

### 1. CSP配置

```html
<meta http-equiv="Content-Security-Policy" 
      content="default-src 'self'; script-src 'self' 'unsafe-eval'; style-src 'self' 'unsafe-inline';">
```

### 2. HTTPS配置

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    # 其他SSL配置...
}
```

## 📈 扩展部署

### 1. 负载均衡

```nginx
upstream frontend {
    server frontend1:80;
    server frontend2:80;
    server frontend3:80;
}

server {
    listen 80;
    location / {
        proxy_pass http://frontend;
    }
}
```

### 2. 微服务架构

```yaml
# docker-compose.yml
version: '3.8'
services:
  frontend:
    build: .
    ports:
      - "80:80"
    depends_on:
      - backend
  
  backend:
    image: member-report-backend
    ports:
      - "8000:8000"
    
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
```

## 🔍 故障排除

### 常见问题

1. **白屏问题**：检查控制台错误，通常是路由配置问题
2. **API调用失败**：检查后端服务状态和CORS配置
3. **图表不显示**：检查ECharts依赖和DOM元素
4. **样式异常**：检查CSS文件加载和Element Plus配置

### 调试工具

- Vue DevTools
- Chrome DevTools
- Network面板
- Performance面板

---

## 📞 技术支持

如有部署问题，请联系技术团队：
- 邮箱：<EMAIL>
- 文档：查看项目README.md
- 问题反馈：提交Issue到项目仓库 