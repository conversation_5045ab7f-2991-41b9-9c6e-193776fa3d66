"""
密码加密和验证工具
使用bcrypt进行密码哈希
"""
import logging
from passlib.context import CryptContext

logger = logging.getLogger(__name__)

# 创建密码上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class PasswordHandler:
    """密码处理器"""
    
    @staticmethod
    def hash_password(password: str) -> str:
        """
        对密码进行哈希处理
        
        Args:
            password: 原始密码
            
        Returns:
            str: 哈希后的密码
        """
        try:
            return pwd_context.hash(password)
        except Exception as e:
            logger.error(f"密码哈希失败: {e}")
            raise ValueError("密码加密失败")
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """
        验证密码是否正确
        
        Args:
            plain_password: 原始密码
            hashed_password: 哈希后的密码
            
        Returns:
            bool: 密码是否匹配
        """
        try:
            return pwd_context.verify(plain_password, hashed_password)
        except Exception as e:
            logger.error(f"密码验证失败: {e}")
            return False
    
    @staticmethod
    def is_password_strong(password: str) -> tuple[bool, str]:
        """
        检查密码强度
        
        Args:
            password: 要检查的密码
            
        Returns:
            tuple: (是否强密码, 提示信息)
        """
        if len(password) < 6:
            return False, "密码长度至少6位"
        
        if len(password) < 8:
            return True, "密码强度：弱"
        
        # 检查是否包含数字
        has_digit = any(c.isdigit() for c in password)
        # 检查是否包含字母
        has_letter = any(c.isalpha() for c in password)
        # 检查是否包含大写字母
        has_upper = any(c.isupper() for c in password)
        # 检查是否包含小写字母
        has_lower = any(c.islower() for c in password)
        # 检查是否包含特殊字符
        has_special = any(not c.isalnum() for c in password)
        
        strength_score = sum([has_digit, has_letter, has_upper, has_lower, has_special])
        
        if strength_score >= 4:
            return True, "密码强度：强"
        elif strength_score >= 3:
            return True, "密码强度：中"
        else:
            return True, "密码强度：弱"
    
    @staticmethod
    def generate_random_password(length: int = 12) -> str:
        """
        生成随机密码
        
        Args:
            length: 密码长度
            
        Returns:
            str: 随机密码
        """
        import string
        import secrets
        
        # 确保密码包含各种字符类型
        alphabet = string.ascii_letters + string.digits + string.punctuation
        
        # 生成密码
        while True:
            password = ''.join(secrets.choice(alphabet) for _ in range(length))
            
            # 确保密码满足基本要求
            if (any(c.islower() for c in password) and
                any(c.isupper() for c in password) and
                any(c.isdigit() for c in password) and
                any(c in string.punctuation for c in password)):
                return password


# 创建全局密码处理器实例
password_handler = PasswordHandler()

# 导出便捷函数
hash_password = password_handler.hash_password
verify_password = password_handler.verify_password
is_password_strong = password_handler.is_password_strong
generate_random_password = password_handler.generate_random_password