// 样本数据生成器
export const SampleDataGenerator = {
  // 获取日期限制
  getDateLimits(queryType) {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    switch(queryType) {
      case 'week':
        // 周分析只能选择8天前的数据
        const weekLimit = new Date(today);
        weekLimit.setDate(weekLimit.getDate() - 8);
        return {
          maxDate: weekLimit,
          minDate: new Date(weekLimit.getFullYear() - 1, weekLimit.getMonth(), weekLimit.getDate()),
          label: '周分析最早可选择8天前的数据'
        };
      
      case 'month':
        // 月分析只能选择满一月前的数据
        const monthLimit = new Date(today);
        monthLimit.setMonth(monthLimit.getMonth() - 1);
        return {
          maxDate: monthLimit,
          minDate: new Date(monthLimit.getFullYear() - 1, monthLimit.getMonth(), monthLimit.getDate()),
          label: '月分析最早可选择满一月前的数据'
        };
      
      case 'quarter':
        // 季度分析只能选择满一季度前的数据
        const quarterLimit = new Date(today);
        quarterLimit.setMonth(quarterLimit.getMonth() - 3);
        return {
          maxDate: quarterLimit,
          minDate: new Date(quarterLimit.getFullYear() - 1, quarterLimit.getMonth(), quarterLimit.getDate()),
          label: '季度分析最早可选择满一季度前的数据'
        };
      
      case 'halfyear':
        // 半年分析只能选择满半年前的数据
        const halfyearLimit = new Date(today);
        halfyearLimit.setMonth(halfyearLimit.getMonth() - 6);
        return {
          maxDate: halfyearLimit,
          minDate: new Date(halfyearLimit.getFullYear() - 1, halfyearLimit.getMonth(), halfyearLimit.getDate()),
          label: '半年分析最早可选择满半年前的数据'
        };
      
      default:
        return {
          maxDate: today,
          minDate: new Date(today.getFullYear() - 2, today.getMonth(), today.getDate()),
          label: '请选择有效的查询类型'
        };
    }
  },

  // 生成会员基础模块数据
  generateMemberBaseData(queryType = 'week') {
    // 根据查询类型生成不同的对比数据
    let chainLabels = ['上期', '本期']
    
    return {
      totalMembers: {
        value: 15680,
        chainComparison: [queryType === 'quarter' ? 14200 : queryType === 'halfyear' ? 13500 : queryType === 'month' ? 15100 : 14100],
        chainChangeRate: [queryType === 'quarter' ? '10.42%' : queryType === 'halfyear' ? '16.15%' : queryType === 'month' ? '3.84%' : '11.21%'],
        chainLabels,
        yearOverYear: 12800,
        yearOverYearRate: '22.50%',
        unit: '人'
      },
      netMembers: {
        value: 14890,
        chainComparison: [queryType === 'quarter' ? 13100 : queryType === 'halfyear' ? 12800 : queryType === 'month' ? 14200 : 13400],
        chainChangeRate: [queryType === 'quarter' ? '13.66%' : queryType === 'halfyear' ? '16.33%' : queryType === 'month' ? '4.86%' : '11.12%'],
        chainLabels,
        yearOverYear: 12100,
        yearOverYearRate: '23.06%',
        unit: '人'
      },
      newMembers: {
        value: 1580,
        chainComparison: [queryType === 'quarter' ? 1420 : queryType === 'halfyear' ? 1350 : queryType === 'month' ? 1500 : 1420],
        chainChangeRate: [queryType === 'quarter' ? '11.27%' : queryType === 'halfyear' ? '17.04%' : queryType === 'month' ? '5.33%' : '11.27%'],
        chainLabels,
        yearOverYear: 1200,
        yearOverYearRate: '31.67%',
        unit: '人'
      },
      unfollowMembers: {
        value: 158,
        chainComparison: [queryType === 'quarter' ? 142 : queryType === 'halfyear' ? 120 : queryType === 'month' ? 135 : 142],
        chainChangeRate: [queryType === 'quarter' ? '11.27%' : queryType === 'halfyear' ? '31.67%' : queryType === 'month' ? '17.04%' : '11.27%'],
        chainLabels,
        yearOverYear: 180,
        yearOverYearRate: '-12.22%',
        unit: '人'
      },
      unfollowRate: {
        value: 10.0,
        chainComparison: [queryType === 'quarter' ? 10.0 : queryType === 'halfyear' ? 10.0 : queryType === 'month' ? 10.0 : 10.0],
        chainChangeRate: [queryType === 'quarter' ? '0.00%' : queryType === 'halfyear' ? '0.00%' : queryType === 'month' ? '0.00%' : '0.00%'],
        chainLabels,
        yearOverYear: 15.0,
        yearOverYearRate: '-33.33%',
        unit: '%'
      },
      newConsumeMembers: {
        value: 890,
        chainComparison: [queryType === 'quarter' ? 850 : queryType === 'halfyear' ? 720 : queryType === 'month' ? 800 : 850],
        chainChangeRate: [queryType === 'quarter' ? '4.71%' : queryType === 'halfyear' ? '23.61%' : queryType === 'month' ? '11.25%' : '4.71%'],
        chainLabels,
        yearOverYear: 680,
        yearOverYearRate: '30.88%',
        unit: '人'
      },
      newChargeMembers: {
        value: 320,
        chainComparison: [queryType === 'quarter' ? 300 : queryType === 'halfyear' ? 250 : queryType === 'month' ? 280 : 300],
        chainChangeRate: [queryType === 'quarter' ? '6.67%' : queryType === 'halfyear' ? '28.00%' : queryType === 'month' ? '14.29%' : '6.67%'],
        chainLabels,
        yearOverYear: 280,
        yearOverYearRate: '14.29%',
        unit: '人'
      },
      totalConsumeMembers: {
        value: 8900,
        chainComparison: [queryType === 'quarter' ? 8300 : queryType === 'halfyear' ? 7200 : queryType === 'month' ? 7800 : 8300],
        chainChangeRate: [queryType === 'quarter' ? '7.23%' : queryType === 'halfyear' ? '23.61%' : queryType === 'month' ? '14.10%' : '7.23%'],
        chainLabels,
        yearOverYear: 7500,
        yearOverYearRate: '18.67%',
        unit: '人'
      },
      totalChargeMembers: {
        value: 3200,
        chainComparison: [queryType === 'quarter' ? 3100 : queryType === 'halfyear' ? 2800 : queryType === 'month' ? 2950 : 3100],
        chainChangeRate: [queryType === 'quarter' ? '3.23%' : queryType === 'halfyear' ? '14.29%' : queryType === 'month' ? '8.47%' : '3.23%'],
        chainLabels,
        yearOverYear: 2900,
        yearOverYearRate: '10.34%',
        unit: '人'
      },
      newCompleteMembers: {
        value: 450,
        chainComparison: [queryType === 'quarter' ? 435 : queryType === 'halfyear' ? 380 : queryType === 'month' ? 420 : 435],
        chainChangeRate: [queryType === 'quarter' ? '3.45%' : queryType === 'halfyear' ? '18.42%' : queryType === 'month' ? '7.14%' : '3.45%'],
        chainLabels,
        yearOverYear: 390,
        yearOverYearRate: '15.38%',
        unit: '人'
      },
      newCompleteRate: {
        value: 28.5,
        chainComparison: [queryType === 'quarter' ? 30.6 : queryType === 'halfyear' ? 31.7 : queryType === 'month' ? 31.1 : 30.6],
        chainChangeRate: [queryType === 'quarter' ? '-6.86%' : queryType === 'halfyear' ? '-10.09%' : queryType === 'month' ? '-8.36%' : '-6.86%'],
        chainLabels,
        yearOverYear: 32.5,
        yearOverYearRate: '-12.31%',
        unit: '%'
      },
      completePhoneMembers: {
        value: 5200,
        chainComparison: [queryType === 'quarter' ? 4900 : queryType === 'halfyear' ? 4500 : queryType === 'month' ? 5000 : 4900],
        chainChangeRate: [queryType === 'quarter' ? '6.12%' : queryType === 'halfyear' ? '15.56%' : queryType === 'month' ? '4.00%' : '6.12%'],
        chainLabels,
        yearOverYear: 4600,
        yearOverYearRate: '13.04%',
        unit: '人'
      },
      totalCompleteMembers: {
        value: 4200,
        chainComparison: [queryType === 'quarter' ? 4100 : queryType === 'halfyear' ? 3800 : queryType === 'month' ? 3950 : 4100],
        chainChangeRate: [queryType === 'quarter' ? '2.44%' : queryType === 'halfyear' ? '10.53%' : queryType === 'month' ? '6.33%' : '2.44%'],
        chainLabels,
        yearOverYear: 3900,
        yearOverYearRate: '7.69%',
        unit: '人'
      },
      consumeOnceMembers: {
        value: 2100,
        chainComparison: [queryType === 'quarter' ? 2000 : queryType === 'halfyear' ? 1800 : queryType === 'month' ? 1900 : 2000],
        chainChangeRate: [queryType === 'quarter' ? '5.00%' : queryType === 'halfyear' ? '16.67%' : queryType === 'month' ? '10.53%' : '5.00%'],
        chainLabels,
        yearOverYear: 1850,
        yearOverYearRate: '13.51%',
        unit: '人'
      },
      consumeMultipleMembers: {
        value: 6800,
        chainComparison: [queryType === 'quarter' ? 6300 : queryType === 'halfyear' ? 5400 : queryType === 'month' ? 5900 : 6300],
        chainChangeRate: [queryType === 'quarter' ? '7.94%' : queryType === 'halfyear' ? '25.93%' : queryType === 'month' ? '15.25%' : '7.94%'],
        chainLabels,
        yearOverYear: 5650,
        yearOverYearRate: '20.35%',
        unit: '人'
      }
    };
  },

  // 生成会员消费模块数据
  generateMemberConsumeData(queryType = 'week') {
    // 根据查询类型生成不同的对比数据
    let chainLabels = ['上期', '本期']
    
    return {
      totalAmount: {
        value: 580000,
        chainComparison: [queryType === 'quarter' ? 520000 : queryType === 'halfyear' ? 480000 : queryType === 'month' ? 560000 : 550000],
        chainChangeRate: [queryType === 'quarter' ? '11.54%' : queryType === 'halfyear' ? '20.83%' : queryType === 'month' ? '3.57%' : '5.45%'],
        chainLabels,
        yearOverYear: 480000,
        yearOverYearRate: '20.83%',
        unit: '元'
      },
      consumeUsers: {
        value: 2800,
        chainComparison: [queryType === 'quarter' ? 2500 : queryType === 'halfyear' ? 2200 : queryType === 'month' ? 2700 : 2650],
        chainChangeRate: [queryType === 'quarter' ? '12.00%' : queryType === 'halfyear' ? '27.27%' : queryType === 'month' ? '3.70%' : '5.66%'],
        chainLabels,
        yearOverYear: 2300,
        yearOverYearRate: '21.74%',
        unit: '人'
      },
      totalConsumeCount: {
        value: 4200,
        chainComparison: [queryType === 'quarter' ? 3950 : queryType === 'halfyear' ? 3300 : queryType === 'month' ? 3700 : 3950],
        chainChangeRate: [queryType === 'quarter' ? '6.33%' : queryType === 'halfyear' ? '27.27%' : queryType === 'month' ? '13.51%' : '6.33%'],
        chainLabels,
        yearOverYear: 3500,
        yearOverYearRate: '20.00%',
        unit: '笔'
      },
      actualAmount: {
        value: 120000,
        chainComparison: [queryType === 'quarter' ? 115000 : queryType === 'halfyear' ? 95000 : queryType === 'month' ? 110000 : 115000],
        chainChangeRate: [queryType === 'quarter' ? '4.35%' : queryType === 'halfyear' ? '26.32%' : queryType === 'month' ? '9.09%' : '4.35%'],
        chainLabels,
        yearOverYear: 100000,
        yearOverYearRate: '20.00%',
        unit: '元'
      },
      cashUsers: {
        value: 1800,
        chainComparison: [queryType === 'quarter' ? 1700 : queryType === 'halfyear' ? 1400 : queryType === 'month' ? 1600 : 1700],
        chainChangeRate: [queryType === 'quarter' ? '5.88%' : queryType === 'halfyear' ? '28.57%' : queryType === 'month' ? '12.50%' : '5.88%'],
        chainLabels,
        yearOverYear: 1500,
        yearOverYearRate: '20.00%',
        unit: '人'
      },
      cashConsumeCount: {
        value: 2100,
        chainComparison: [queryType === 'quarter' ? 1975 : queryType === 'halfyear' ? 1650 : queryType === 'month' ? 1850 : 1975],
        chainChangeRate: [queryType === 'quarter' ? '6.33%' : queryType === 'halfyear' ? '27.27%' : queryType === 'month' ? '13.51%' : '6.33%'],
        chainLabels,
        yearOverYear: 1750,
        yearOverYearRate: '20.00%',
        unit: '笔'
      },
      prepayAmount: {
        value: 460000,
        chainComparison: [queryType === 'quarter' ? 435000 : queryType === 'halfyear' ? 355000 : queryType === 'month' ? 410000 : 435000],
        chainChangeRate: [queryType === 'quarter' ? '5.75%' : queryType === 'halfyear' ? '29.58%' : queryType === 'month' ? '12.20%' : '5.75%'],
        chainLabels,
        yearOverYear: 380000,
        yearOverYearRate: '21.05%',
        unit: '元'
      },
      prepayActualAmount: {
        value: 380000,
        chainComparison: [queryType === 'quarter' ? 365000 : queryType === 'halfyear' ? 320000 : queryType === 'month' ? 350000 : 365000],
        chainChangeRate: [queryType === 'quarter' ? '4.11%' : queryType === 'halfyear' ? '18.75%' : queryType === 'month' ? '8.57%' : '4.11%'],
        chainLabels,
        yearOverYear: 330000,
        yearOverYearRate: '15.15%',
        unit: '元'
      },
      prepayUsers: {
        value: 1000,
        chainComparison: [queryType === 'quarter' ? 950 : queryType === 'halfyear' ? 800 : queryType === 'month' ? 900 : 950],
        chainChangeRate: [queryType === 'quarter' ? '5.26%' : queryType === 'halfyear' ? '25.00%' : queryType === 'month' ? '11.11%' : '5.26%'],
        chainLabels,
        yearOverYear: 800,
        yearOverYearRate: '25.00%',
        unit: '人'
      },
      prepayConsumeCount: {
        value: 2100,
        chainComparison: [queryType === 'quarter' ? 1975 : queryType === 'halfyear' ? 1650 : queryType === 'month' ? 1850 : 1975],
        chainChangeRate: [queryType === 'quarter' ? '6.33%' : queryType === 'halfyear' ? '27.27%' : queryType === 'month' ? '13.51%' : '6.33%'],
        chainLabels,
        yearOverYear: 1750,
        yearOverYearRate: '20.00%',
        unit: '笔'
      },
      totalActualAmount: {
        value: 500000,
        chainComparison: [queryType === 'quarter' ? 480000 : queryType === 'halfyear' ? 415000 : queryType === 'month' ? 460000 : 480000],
        chainChangeRate: [queryType === 'quarter' ? '4.17%' : queryType === 'halfyear' ? '20.48%' : queryType === 'month' ? '8.70%' : '4.17%'],
        chainLabels,
        yearOverYear: 430000,
        yearOverYearRate: '16.28%',
        unit: '元'
      },
      avgContribution: {
        value: 178.57,
        chainComparison: [queryType === 'quarter' ? 192.00 : queryType === 'halfyear' ? 188.64 : queryType === 'month' ? 170.37 : 181.13],
        chainChangeRate: [queryType === 'quarter' ? '-7.00%' : queryType === 'halfyear' ? '-5.34%' : queryType === 'month' ? '4.81%' : '-1.41%'],
        chainLabels,
        yearOverYear: 186.96,
        yearOverYearRate: '-4.49%',
        unit: '元'
      },
      couponTradeAmount: {
        value: 89000,
        chainComparison: [queryType === 'quarter' ? 84000 : queryType === 'halfyear' ? 72000 : queryType === 'month' ? 80000 : 84000],
        chainChangeRate: [queryType === 'quarter' ? '5.95%' : queryType === 'halfyear' ? '23.61%' : queryType === 'month' ? '11.25%' : '5.95%'],
        chainLabels,
        yearOverYear: 75000,
        yearOverYearRate: '18.67%',
        unit: '元'
      }
    };
  },

  // 生成会员充值模块数据
  generateMemberChargeData(queryType = 'week') {
    // 根据查询类型生成不同的对比数据
    let chainLabels = ['上期', '本期']
    
    return {
      chargeCount: {
        value: 480,
        chainComparison: [queryType === 'quarter' ? 420 : queryType === 'halfyear' ? 380 : queryType === 'month' ? 460 : 450],
        chainChangeRate: [queryType === 'quarter' ? '14.29%' : queryType === 'halfyear' ? '26.32%' : queryType === 'month' ? '4.35%' : '6.67%'],
        chainLabels,
        yearOverYear: 400,
        yearOverYearRate: '20.00%',
        unit: '笔'
      },
      chargeAmount: {
        value: 480000,
        chainComparison: [queryType === 'quarter' ? 450000 : queryType === 'halfyear' ? 380000 : queryType === 'month' ? 420000 : 450000],
        chainChangeRate: [queryType === 'quarter' ? '6.67%' : queryType === 'halfyear' ? '26.32%' : queryType === 'month' ? '14.29%' : '6.67%'],
        chainLabels,
        yearOverYear: 400000,
        yearOverYearRate: '20.00%',
        unit: '元'
      },
      consumePrepayAmount: {
        value: 460000,
        chainComparison: [queryType === 'quarter' ? 435000 : queryType === 'halfyear' ? 355000 : queryType === 'month' ? 410000 : 435000],
        chainChangeRate: [queryType === 'quarter' ? '5.75%' : queryType === 'halfyear' ? '29.58%' : queryType === 'month' ? '12.20%' : '5.75%'],
        chainLabels,
        yearOverYear: 380000,
        yearOverYearRate: '21.05%',
        unit: '元'
      },
      retentionRate: {
        value: 4.17,
        chainComparison: [queryType === 'quarter' ? 3.33 : queryType === 'halfyear' ? 6.58 : queryType === 'month' ? 2.38 : 3.33],
        chainChangeRate: [queryType === 'quarter' ? '25.23%' : queryType === 'halfyear' ? '-36.63%' : queryType === 'month' ? '75.21%' : '25.23%'],
        chainLabels,
        yearOverYear: 5.00,
        yearOverYearRate: '-16.60%',
        unit: '%'
      }
    };
  },

  // 生成券交易模块数据
  generateCouponTradeData() {
    return [
      {
        couponName: '满100减20优惠券',
        couponId: 'CPN001',
        couponSendCount: 2000,
        couponUsedCount: 1200,
        couponUsageRate: 60.0,
        couponDiscountAmount: 24000,
        drivePrepayAmount: 180000,
        driveCashAmount: 60000,
        driveTotalAmount: 240000
      },
      {
        couponName: '新用户专享券',
        couponId: 'CPN002',
        couponSendCount: 1500,
        couponUsedCount: 800,
        couponUsageRate: 53.3,
        couponDiscountAmount: 12000,
        drivePrepayAmount: 120000,
        driveCashAmount: 40000,
        driveTotalAmount: 160000
      },
      {
        couponName: '会员生日券',
        couponId: 'CPN003',
        couponSendCount: 800,
        couponUsedCount: 600,
        couponUsageRate: 75.0,
        couponDiscountAmount: 18000,
        drivePrepayAmount: 150000,
        driveCashAmount: 50000,
        driveTotalAmount: 200000
      },
      {
        couponName: '满200减50优惠券',
        couponId: 'CPN004',
        couponSendCount: 1200,
        couponUsedCount: 900,
        couponUsageRate: 75.0,
        couponDiscountAmount: 45000,
        drivePrepayAmount: 270000,
        driveCashAmount: 90000,
        driveTotalAmount: 360000
      },
      {
        couponName: '周末特惠券',
        couponId: 'CPN005',
        couponSendCount: 3000,
        couponUsedCount: 1800,
        couponUsageRate: 60.0,
        couponDiscountAmount: 27000,
        drivePrepayAmount: 216000,
        driveCashAmount: 72000,
        driveTotalAmount: 288000
      }
    ];
  },

  // 生成报告数据
  generateReportData() {
    return {
      overview: {
        totalMembers: 15680,
        newMembers: 1580,
        totalRevenue: 580000,
        couponRevenue: 89000,
        memberGrowthRate: 11.21,
        revenueGrowthRate: 20.83,
        retentionRate: 4.17,
        avgContribution: 207.14
      },
      insights: [
        '会员总数呈稳定增长趋势，环比增长11.21%',
        '新增会员中约56%进行了消费，会员转化率良好',
        '储值会员消费金额占比79.3%，储值推广效果显著',
        '券带动交易金额占总消费的15.3%，营销效果良好',
        '会员人均贡献保持稳定，略有下降需要关注'
      ],
      recommendations: [
        '加强新会员引导，提高完善率和首次消费率',
        '优化储值产品，提高储值留存率',
        '精准投放优惠券，提升券使用率',
        '开展会员分层运营，提升高价值会员贡献',
        '完善会员权益体系，降低取关率'
      ]
    };
  },

  // 品智收银数据生成器已移除 - 不再提供虚拟数据
  // 实际数据将通过API从PostgreSQL品质收银数据库获取
};

// 导出默认样本数据
const SampleData = {
  memberBase: SampleDataGenerator.generateMemberBaseData(),
  memberConsume: SampleDataGenerator.generateMemberConsumeData(),
  memberCharge: SampleDataGenerator.generateMemberChargeData(),
  couponTrade: SampleDataGenerator.generateCouponTradeData(),
  report: SampleDataGenerator.generateReportData(),
  dateLimits: SampleDataGenerator.getDateLimits
};

export default SampleData; 