# -*- coding: utf-8 -*-
"""
PPT图片获取服务
负责生成和管理PPT中使用的各种图片
"""

import logging
import os
from typing import Dict, Any, Optional
import asyncio

from core.models import QueryParams
from .picture.PictureSave import create_image_manager
from .picture.NewMembersPic import create_new_members_pic_generator

logger = logging.getLogger(__name__)

class PicAcquisitionService:
    """图片获取服务类"""

    def __init__(self):
        """初始化图片获取服务"""
        logger.info("图片获取服务初始化完成")

    def _extract_bid(self, query_params):
        """
        从查询参数中提取bid

        Args:
            query_params: 查询参数（可能是对象或字典）

        Returns:
            str: bid值
        """
        if hasattr(query_params, 'bid'):
            return query_params.bid
        elif isinstance(query_params, dict):
            return query_params.get('bid')
        else:
            raise ValueError("无法从查询参数中提取bid")

    async def generate_all_pictures(self, query_params) -> Dict[str, str]:
        """
        生成PPT所需的所有图片

        Args:
            query_params: 查询参数（对象或字典）

        Returns:
            Dict: 图片类型到路径的映射
        """
        try:
            bid = self._extract_bid(query_params)
            logger.info(f"开始生成PPT图片 - bid: {bid}")

            # 创建图片管理器
            image_manager = create_image_manager(bid)

            # 生成各类图片
            result = {}

            # 生成新增会员图片
            new_members_pics = await self._generate_new_members_pictures(query_params, image_manager)
            result.update(new_members_pics)

            # 生成会员消费图片
            consumption_pics = await self._generate_member_consumption_pictures(query_params, image_manager)
            result.update(consumption_pics)

            # 生成会员平均消费图片
            avg_consumption_pics = await self._generate_avg_consumption_pictures(query_params, image_manager)
            result.update(avg_consumption_pics)

            # 生成会员消费数量图片
            consumption_num_pics = await self._generate_consumption_num_pictures(query_params, image_manager)
            result.update(consumption_num_pics)

            # 生成会员充值消费图片
            member_charge_pics = await self._generate_member_charge_pictures(query_params, image_manager)
            result.update(member_charge_pics)

            # 生成首次充值和消费人数图片
            first_member_number_pics = await self._generate_first_member_number_pictures(query_params, image_manager)
            result.update(first_member_number_pics)

            # 生成首次充值和消费金额图片
            first_charge_sale_pics = await self._generate_first_charge_sale_pictures(query_params, image_manager)
            result.update(first_charge_sale_pics)

            # 生成会员等级消费分析图片
            level_consumption_pics = await self._generate_level_consumption_pictures(query_params, image_manager)
            result.update(level_consumption_pics)

            # 生成会员等级订单分析图片
            level_order_pics = await self._generate_level_order_pictures(query_params, image_manager)
            result.update(level_order_pics)

            # 生成会员等级人数分析图片
            level_number_pics = await self._generate_level_number_pictures(query_params, image_manager)
            result.update(level_number_pics)

            # 生成充值档位分布图片
            charge_distribution_pics = await self._generate_charge_distribution_pictures(query_params, image_manager)
            result.update(charge_distribution_pics)

            # 生成充值频次分布图片
            charge_frequency_pics = await self._generate_charge_frequency_pictures(query_params, image_manager)
            result.update(charge_frequency_pics)

            # 生成积分变化图片
            credit_change_pics = await self._generate_credit_change_pictures(query_params, image_manager)
            result.update(credit_change_pics)

            # 生成积分分布图片
            credit_distribution_pics = await self._generate_credit_distribution_pictures(query_params, image_manager)
            result.update(credit_distribution_pics)

            logger.info(f"PPT图片生成完成 - 共生成 {len(result)} 张图片")
            return result

        except Exception as e:
            logger.error(f"生成PPT图片失败: {e}")
            return {}

    async def _generate_new_members_pictures(self, query_params, image_manager) -> Dict[str, str]:
        """
        生成新增会员相关图片

        Args:
            query_params: 查询参数（对象或字典）
            image_manager: 图片管理器

        Returns:
            Dict: 新增会员图片路径映射
        """
        try:
            logger.info("开始生成新增会员图片")

            bid = self._extract_bid(query_params)
            # 创建新增会员图片生成器
            generator = create_new_members_pic_generator(bid, image_manager)

            # 生成图片
            result = await generator.generate_new_members_charts(query_params)

            logger.info(f"新增会员图片生成完成: {list(result.keys())}")
            return result

        except Exception as e:
            logger.error(f"生成新增会员图片失败: {e}")
            return {}

    async def _generate_member_consumption_pictures(self, query_params, image_manager) -> Dict[str, str]:
        """
        生成会员消费相关图片

        Args:
            query_params: 查询参数（对象或字典）
            image_manager: 图片管理器

        Returns:
            Dict: 图片路径字典
        """
        try:
            from .picture.MemberConsumptionPic import create_member_consumption_pic_generator

            bid = self._extract_bid(query_params)
            # 创建会员消费图片生成器
            generator = create_member_consumption_pic_generator(bid, image_manager)

            # 生成图片
            result = await generator.generate_member_consumption_charts(query_params)

            logger.info(f"会员消费图片生成完成: {list(result.keys())}")
            return result

        except Exception as e:
            logger.error(f"生成会员消费图片失败: {e}")
            return {}

    async def _generate_avg_consumption_pictures(self, query_params, image_manager) -> Dict[str, str]:
        """
        生成会员平均消费相关图片

        Args:
            query_params: 查询参数（对象或字典）
            image_manager: 图片管理器

        Returns:
            Dict: 图片路径字典
        """
        try:
            from .picture.AvgConsumptionPic import create_avg_consumption_pic_generator

            bid = self._extract_bid(query_params)
            # 创建会员平均消费图片生成器
            generator = create_avg_consumption_pic_generator(bid, image_manager)

            # 生成图片
            result = await generator.generate_avg_consumption_charts(query_params)

            logger.info(f"会员平均消费图片生成完成: {list(result.keys())}")
            return result

        except Exception as e:
            logger.error(f"生成会员平均消费图片失败: {e}")
            return {}

    async def _generate_consumption_num_pictures(self, query_params, image_manager) -> Dict[str, str]:
        """
        生成会员消费数量相关图片

        Args:
            query_params: 查询参数（对象或字典）
            image_manager: 图片管理器

        Returns:
            Dict: 图片路径字典
        """
        try:
            from .picture.ConsumptionNumPic import create_consumption_num_pic_generator

            bid = self._extract_bid(query_params)
            # 创建会员消费数量图片生成器
            generator = create_consumption_num_pic_generator(bid, image_manager)

            # 生成图片
            result = await generator.generate_consumption_num_charts(query_params)

            logger.info(f"会员消费数量图片生成完成: {list(result.keys())}")
            return result

        except Exception as e:
            logger.error(f"生成会员消费数量图片失败: {e}")
            return {}

    async def _generate_member_charge_pictures(self, query_params, image_manager) -> Dict[str, str]:
        """
        生成会员充值消费相关图片

        Args:
            query_params: 查询参数（对象或字典）
            image_manager: 图片管理器

        Returns:
            Dict: 图片路径字典
        """
        try:
            from .picture.MemberChargePic import create_member_charge_pic_generator

            bid = self._extract_bid(query_params)
            # 创建会员充值消费图片生成器
            generator = create_member_charge_pic_generator(bid, image_manager)

            # 生成图片
            result = await generator.generate_member_charge_charts(query_params)

            logger.info(f"会员充值消费图片生成完成: {list(result.keys())}")
            return result

        except Exception as e:
            logger.error(f"生成会员充值消费图片失败: {e}")
            return {}

    async def _generate_level_consumption_pictures(self, query_params, image_manager) -> Dict[str, str]:
        """
        生成会员等级消费分析相关图片

        Args:
            query_params: 查询参数（对象或字典）
            image_manager: 图片管理器

        Returns:
            Dict: 图片路径字典
        """
        try:
            from .picture.LevelConsumptionPic import create_level_consumption_pic_generator

            bid = self._extract_bid(query_params)
            # 创建会员等级消费分析图片生成器
            generator = create_level_consumption_pic_generator(bid, image_manager)

            # 生成图片
            result = await generator.generate_level_consumption_chart(query_params)

            logger.info(f"会员等级消费分析图片生成完成: {list(result.keys())}")
            return result

        except Exception as e:
            logger.error(f"生成会员等级消费分析图片失败: {e}")
            return {}

    async def _generate_level_order_pictures(self, query_params, image_manager) -> Dict[str, str]:
        """
        生成会员等级订单分析相关图片

        Args:
            query_params: 查询参数（对象或字典）
            image_manager: 图片管理器

        Returns:
            Dict: 图片路径字典
        """
        try:
            from .picture.LevelOrderPic import create_level_order_pic_generator

            bid = self._extract_bid(query_params)
            # 创建会员等级订单分析图片生成器
            generator = create_level_order_pic_generator(bid, image_manager)

            # 生成图片
            result = await generator.generate_level_order_chart(query_params)

            logger.info(f"会员等级订单分析图片生成完成: {list(result.keys())}")
            return result

        except Exception as e:
            logger.error(f"生成会员等级订单分析图片失败: {e}")
            return {}

    async def _generate_level_number_pictures(self, query_params, image_manager) -> Dict[str, str]:
        """
        生成会员等级人数分析相关图片

        Args:
            query_params: 查询参数（对象或字典）
            image_manager: 图片管理器

        Returns:
            Dict: 图片路径字典
        """
        try:
            from .picture.LevelNumberPic import create_level_number_pic_generator

            bid = self._extract_bid(query_params)
            # 创建会员等级人数分析图片生成器
            generator = create_level_number_pic_generator(bid, image_manager)

            # 生成图片
            result = await generator.generate_level_number_chart(query_params)

            logger.info(f"会员等级人数分析图片生成完成: {list(result.keys())}")
            return result

        except Exception as e:
            logger.error(f"生成会员等级人数分析图片失败: {e}")
            return {}

    async def _generate_charge_distribution_pictures(self, query_params, image_manager) -> Dict[str, str]:
        """
        生成充值档位分布相关图片

        Args:
            query_params: 查询参数（对象或字典）
            image_manager: 图片管理器

        Returns:
            Dict: 图片路径字典
        """
        try:
            from .picture.ChargeDistribution.ChargeDistributionPic import create_charge_distribution_pic_generator

            bid = self._extract_bid(query_params)
            # 创建充值档位分布图片生成器
            generator = create_charge_distribution_pic_generator(bid, image_manager)

            # 生成图片
            result = await generator.generate_charge_distribution_chart(query_params)

            logger.info(f"充值档位分布图片生成完成: {list(result.keys())}")
            return result

        except Exception as e:
            logger.error(f"生成充值档位分布图片失败: {e}")
            return {}

    async def _generate_charge_frequency_pictures(self, query_params, image_manager) -> Dict[str, str]:
        """
        生成充值频次分布相关图片

        Args:
            query_params: 查询参数（对象或字典）
            image_manager: 图片管理器

        Returns:
            Dict: 图片路径字典
        """
        try:
            from .picture.ChargeDistribution.ChargeFrequencyPic import create_charge_frequency_pic_generator

            bid = self._extract_bid(query_params)
            # 创建充值频次分布图片生成器
            generator = create_charge_frequency_pic_generator(bid, image_manager)

            # 生成图片
            result = await generator.generate_charge_frequency_chart(query_params)

            logger.info(f"充值频次分布图片生成完成: {list(result.keys())}")
            return result

        except Exception as e:
            logger.error(f"生成充值频次分布图片失败: {e}")
            return {}

    async def _generate_first_member_number_pictures(self, query_params, image_manager) -> Dict[str, str]:
        """
        生成首次充值和消费人数相关图片

        Args:
            query_params: 查询参数（对象或字典）
            image_manager: 图片管理器

        Returns:
            Dict: 图片路径字典
        """
        try:
            from .picture.FirstCharge.FirstMemberNumberPic import create_first_member_number_pic_generator

            logger.info("开始生成首次充值和消费人数图片")

            bid = self._extract_bid(query_params)
            # 创建首次充值和消费人数图片生成器
            generator = create_first_member_number_pic_generator(bid, image_manager)

            # 生成图片
            result = await generator.generate_first_member_number_charts(query_params)

            logger.info(f"首次充值和消费人数图片生成完成: {list(result.keys())}")
            return result

        except Exception as e:
            logger.error(f"生成首次充值和消费人数图片失败: {e}")
            return {}

    async def _generate_first_charge_sale_pictures(self, query_params, image_manager) -> Dict[str, str]:
        """
        生成首次充值和消费金额相关图片

        Args:
            query_params: 查询参数（对象或字典）
            image_manager: 图片管理器

        Returns:
            Dict: 图片路径字典
        """
        try:
            from .picture.FirstCharge.FirstChargeSalePic import create_first_charge_sale_pic_generator

            logger.info("开始生成首次充值和消费金额图片")

            bid = self._extract_bid(query_params)
            # 创建首次充值和消费金额图片生成器
            generator = create_first_charge_sale_pic_generator(bid, image_manager)

            # 生成图片
            result = await generator.generate_first_charge_sale_charts(query_params)

            logger.info(f"首次充值和消费金额图片生成完成: {list(result.keys())}")
            return result

        except Exception as e:
            logger.error(f"生成首次充值和消费金额图片失败: {e}")
            return {}

    async def _generate_credit_change_pictures(self, query_params, image_manager) -> Dict[str, str]:
        """
        生成积分变化相关图片

        Args:
            query_params: 查询参数（对象或字典）
            image_manager: 图片管理器

        Returns:
            Dict: 图片路径字典
        """
        try:
            from .picture.Credit.CreditChangePic import create_credit_change_pic_generator

            logger.info("开始生成积分变化图片")

            bid = self._extract_bid(query_params)
            # 创建积分变化图片生成器
            generator = create_credit_change_pic_generator(bid, image_manager)

            # 生成图片
            result = await generator.generate_credit_change_charts(query_params)

            logger.info(f"积分变化图片生成完成: {list(result.keys())}")
            return result

        except Exception as e:
            logger.error(f"生成积分变化图片失败: {e}")
            return {}

    async def _generate_credit_distribution_pictures(self, query_params, image_manager) -> Dict[str, str]:
        """
        生成积分分布相关图片

        Args:
            query_params: 查询参数（对象或字典）
            image_manager: 图片管理器

        Returns:
            Dict: 图片路径字典
        """
        try:
            from .picture.Credit.CreditDistributionPic import create_credit_distribution_pic_generator

            logger.info("开始生成积分分布图片")

            bid = self._extract_bid(query_params)
            # 创建积分分布图片生成器
            generator = create_credit_distribution_pic_generator(bid, image_manager)

            # 生成图片
            result = await generator.generate_credit_distribution_chart(query_params)

            logger.info(f"积分分布图片生成完成: {list(result.keys())}")
            return result

        except Exception as e:
            logger.error(f"生成积分分布图片失败: {e}")
            return {}

    async def get_picture_paths(self, query_params) -> Dict[str, str]:
        """
        获取图片路径（如果不存在则生成）

        Args:
            query_params: 查询参数（对象或字典）

        Returns:
            Dict: 图片类型到路径的映射
        """
        try:
            bid = self._extract_bid(query_params)
            # 获取基础图片路径配置
            from .PictureConstants import get_image_params
            existing_paths = get_image_params(bid)

            # 始终生成所有图片和AI分析（确保数据最新）
            logger.info("开始生成所有图片和AI分析（覆盖模式）")
            generated_pics = await self.generate_all_pictures(query_params)

            # 更新所有新增会员相关的图片路径
            new_member_pic_types = ['new_member_add_last_year', 'new_member_add_this_year']
            for pic_type in new_member_pic_types:
                if pic_type in generated_pics:
                    existing_paths[f'image_{pic_type}'] = generated_pics[pic_type]
                    logger.info(f"更新图片路径: image_{pic_type} -> {generated_pics[pic_type]}")

            # 更新所有会员消费相关的图片路径
            member_consumption_pic_types = ['member_consumption_last_year', 'member_consumption_this_year']
            for pic_type in member_consumption_pic_types:
                if pic_type in generated_pics:
                    existing_paths[f'image_{pic_type}'] = generated_pics[pic_type]
                    logger.info(f"更新图片路径: image_{pic_type} -> {generated_pics[pic_type]}")

            # 更新所有会员平均消费相关的图片路径
            avg_consumption_pic_types = ['avg_consumption_last_year', 'avg_consumption_this_year']
            for pic_type in avg_consumption_pic_types:
                if pic_type in generated_pics:
                    existing_paths[f'image_{pic_type}'] = generated_pics[pic_type]
                    logger.info(f"更新图片路径: image_{pic_type} -> {generated_pics[pic_type]}")

            # 更新所有会员消费数量相关的图片路径
            consumption_num_pic_types = ['consumption_num_last_year', 'consumption_num_this_year']
            for pic_type in consumption_num_pic_types:
                if pic_type in generated_pics:
                    existing_paths[f'image_{pic_type}'] = generated_pics[pic_type]
                    logger.info(f"更新图片路径: image_{pic_type} -> {generated_pics[pic_type]}")

            # 更新所有会员充值消费相关的图片路径
            member_charge_pic_types = ['member_charge_last_year', 'member_charge_this_year']
            for pic_type in member_charge_pic_types:
                if pic_type in generated_pics:
                    existing_paths[f'image_{pic_type}'] = generated_pics[pic_type]
                    logger.info(f"更新图片路径: image_{pic_type} -> {generated_pics[pic_type]}")

            # 更新所有首次充值和消费人数相关的图片路径
            first_member_number_pic_types = ['first_member_number_last_year', 'first_member_number_this_year']
            for pic_type in first_member_number_pic_types:
                if pic_type in generated_pics:
                    existing_paths[f'image_{pic_type}'] = generated_pics[pic_type]
                    logger.info(f"更新图片路径: image_{pic_type} -> {generated_pics[pic_type]}")

            # 更新所有首次充值和消费金额相关的图片路径
            first_charge_sale_pic_types = ['first_charge_last_year', 'first_charge_this_year']
            for pic_type in first_charge_sale_pic_types:
                if pic_type in generated_pics:
                    existing_paths[f'image_{pic_type}'] = generated_pics[pic_type]
                    logger.info(f"更新图片路径: image_{pic_type} -> {generated_pics[pic_type]}")

            # 更新会员等级消费分析相关的图片路径
            level_consumption_pic_types = ['level_consumption']
            for pic_type in level_consumption_pic_types:
                if pic_type in generated_pics:
                    existing_paths[f'image_{pic_type}'] = generated_pics[pic_type]
                    logger.info(f"更新图片路径: image_{pic_type} -> {generated_pics[pic_type]}")

            # 更新会员等级订单分析相关的图片路径
            level_order_pic_types = ['level_order']
            for pic_type in level_order_pic_types:
                if pic_type in generated_pics:
                    existing_paths[f'image_{pic_type}'] = generated_pics[pic_type]
                    logger.info(f"更新图片路径: image_{pic_type} -> {generated_pics[pic_type]}")

            # 更新会员等级人数分析相关的图片路径
            level_number_pic_types = ['level_number']
            for pic_type in level_number_pic_types:
                if pic_type in generated_pics:
                    existing_paths[f'image_{pic_type}'] = generated_pics[pic_type]
                    logger.info(f"更新图片路径: image_{pic_type} -> {generated_pics[pic_type]}")

            # 更新充值档位分布相关的图片路径
            charge_distribution_pic_types = ['charge_distribution']
            for pic_type in charge_distribution_pic_types:
                if pic_type in generated_pics:
                    existing_paths[f'image_{pic_type}'] = generated_pics[pic_type]
                    logger.info(f"更新图片路径: image_{pic_type} -> {generated_pics[pic_type]}")

            # 更新充值频次分布相关的图片路径
            charge_frequency_pic_types = ['charge_frequency_distribution']
            for pic_type in charge_frequency_pic_types:
                if pic_type in generated_pics:
                    existing_paths[f'image_{pic_type}'] = generated_pics[pic_type]
                    logger.info(f"更新图片路径: image_{pic_type} -> {generated_pics[pic_type]}")

            # 更新积分变化相关的图片路径
            credit_change_pic_types = ['credit_change_last_year', 'credit_change_this_year']
            for pic_type in credit_change_pic_types:
                if pic_type in generated_pics:
                    existing_paths[f'image_{pic_type}'] = generated_pics[pic_type]
                    logger.info(f"更新图片路径: image_{pic_type} -> {generated_pics[pic_type]}")

            # 更新积分分布相关的图片路径
            if 'credit_distribution' in generated_pics:
                existing_paths['image_credit_distribution'] = generated_pics['credit_distribution']
                logger.info(f"更新图片路径: image_credit_distribution -> {generated_pics['credit_distribution']}")

            # 添加AI分析参数（确保AI分析始终是最新的）
            ai_analysis_params = [
                "new_member_add_last_year_analysis_report",
                "new_member_add_this_year_analysis_report",
                "member_consumption_last_year_analysis_report",
                "member_consumption_this_year_analysis_report",
                "avg_consumption_last_year_analysis_report",
                "avg_consumption_this_year_analysis_report",
                "consumption_num_last_year_analysis_report",
                "consumption_num_this_year_analysis_report",
                "member_charge_last_year_analysis_report",
                "member_charge_this_year_analysis_report",
                "first_member_number_last_year_analysis_report",
                "first_member_number_this_year_analysis_report",
                "first_charge_last_year_analysis_report",
                "first_charge_this_year_analysis_report",
                "level_consumption_analysis_report",
                "level_order_analysis_report",
                "level_number_analysis_report",
                "charge_distribution_analysis_report",
                "charge_frequency_distribution_analysis_report",
                "credit_change_last_year_analysis_report",
                "credit_change_this_year_analysis_report",
                "credit_distribution_analysis_report"
            ]

            for ai_param in ai_analysis_params:
                if ai_param in generated_pics:
                    existing_paths[ai_param] = generated_pics[ai_param]
                    logger.info(f"添加AI分析参数: {ai_param}")

            logger.info(f"图片和AI分析生成完成，共返回 {len(existing_paths)} 个参数")
            return existing_paths

        except Exception as e:
            logger.error(f"获取图片路径失败: {e}")
            return {}


# 创建服务实例
pic_acquisition_service = PicAcquisitionService()