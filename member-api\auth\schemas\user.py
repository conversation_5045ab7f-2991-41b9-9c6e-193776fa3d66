"""
用户相关的Pydantic模型
"""
from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, EmailStr, validator
from enum import Enum


class UserRole(str, Enum):
    """用户角色枚举"""
    SUPER_ADMIN = "super_admin"
    ADMIN = "admin"
    USER = "user"


class UserStatus(str, Enum):
    """用户状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    BANNED = "banned"


class AccountType(str, Enum):
    """账户类别枚举"""
    REGULAR_AGENT = "regular_agent"  # 常规代理商
    DIRECT_AGENT = "direct_agent"  # 直营代理商
    AOQIWEI_OPERATION = "aoqiwei_operation"  # 奥琦玮直管运营
    MERCHANT_SUPER_ADMIN = "merchant_super_admin"  # 商户超级管理员
    MERCHANT_USER = "merchant_user"  # 商户普通号


class UserBase(BaseModel):
    """用户基础模型"""
    username: str = Field(..., min_length=1, max_length=50, description="用户名")
    email: Optional[EmailStr] = Field(None, description="邮箱地址")
    real_name: Optional[str] = Field(None, max_length=100, description="真实姓名")
    phone: Optional[str] = Field(None, max_length=20, description="联系电话")
    department: Optional[str] = Field(None, max_length=100, description="部门")
    account_type: Optional[AccountType] = Field(AccountType.MERCHANT_USER, description="账户类别")
    expire_time: Optional[datetime] = Field(None, description="账户到期时间")
    remark: Optional[str] = Field(None, description="备注信息")


class UserCreate(UserBase):
    """创建用户模型"""
    password: str = Field(..., min_length=6, description="密码")
    role: UserRole = Field(UserRole.USER, description="用户角色")
    duration_days: Optional[int] = Field(None, description="账户开通时长（天数）")
    bid_permissions: Optional[List[Dict[str, Any]]] = Field(None, description="BID权限配置")
    sid_filter: Optional[Dict[str, List[str]]] = Field(None, description="SID过滤器")
    quotas: Optional[int] = Field(0, description="PPT生成配额限制（0表示无限制）")
    
    @validator('password')
    def validate_password(cls, v):
        """验证密码强度"""
        if len(v) < 6:
            raise ValueError('密码长度至少6位')
        # 可以添加更多密码强度验证
        return v
    
    @validator('expire_time', pre=True, always=True)
    def set_expire_time(cls, v, values):
        """根据duration_days设置expire_time"""
        if 'duration_days' in values and values['duration_days']:
            from datetime import datetime, timedelta
            return datetime.now() + timedelta(days=values['duration_days'])
        return v


class UserUpdate(BaseModel):
    """更新用户模型"""
    email: Optional[EmailStr] = None
    real_name: Optional[str] = Field(None, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    department: Optional[str] = Field(None, max_length=100)
    role: Optional[UserRole] = None
    account_type: Optional[AccountType] = None
    expire_time: Optional[datetime] = None
    duration_days: Optional[int] = Field(None, description="延长账户时长（天数）")
    status: Optional[UserStatus] = None
    bid_permissions: Optional[List[Dict[str, Any]]] = None
    sid_filter: Optional[Dict[str, List[str]]] = None
    quotas: Optional[int] = None
    remark: Optional[str] = None


class UserPasswordUpdate(BaseModel):
    """更新密码模型"""
    old_password: str = Field(..., description="旧密码")
    new_password: str = Field(..., min_length=6, description="新密码")
    
    @validator('new_password')
    def validate_password(cls, v, values):
        """验证新密码"""
        if 'old_password' in values and v == values['old_password']:
            raise ValueError('新密码不能与旧密码相同')
        if len(v) < 6:
            raise ValueError('密码长度至少6位')
        return v


class UserInDB(UserBase):
    """数据库中的用户模型"""
    id: int
    role: UserRole
    status: UserStatus
    password: str
    quotas: Optional[int] = None
    use_quotas: Optional[int] = None
    avatar_url: Optional[str] = None
    last_login_time: Optional[datetime] = None
    login_count: int = 0
    failed_login_count: int = 0
    last_failed_login_time: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    deleted_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.strftime("%Y-%m-%d %H:%M:%S") if v else None
        }


class UserResponse(BaseModel):
    """用户响应模型（不包含敏感信息）"""
    id: int
    username: str
    email: Optional[str] = None
    real_name: Optional[str] = None
    phone: Optional[str] = None
    department: Optional[str] = None
    role: UserRole
    account_type: Optional[AccountType] = None
    expire_time: Optional[str] = None
    status: UserStatus
    quotas: Optional[int] = None
    use_quotas: Optional[int] = None
    user_bid_permissions: Optional[List[Dict[str, Any]]] = None  # 新格式权限
    avatar_url: Optional[str] = None
    last_login_time: Optional[str] = None
    login_count: int = 0
    created_at: str
    updated_at: str
    
    class Config:
        from_attributes = True
        
    @validator('last_login_time', 'created_at', 'updated_at', 'expire_time', pre=True)
    def format_datetime(cls, v):
        """格式化日期时间"""
        if isinstance(v, datetime):
            return v.strftime("%Y-%m-%d %H:%M:%S")
        return v


class UserList(BaseModel):
    """用户列表响应"""
    total: int = Field(..., description="总数")
    items: List[UserResponse] = Field(..., description="用户列表")
    page: int = Field(1, description="当前页")
    page_size: int = Field(20, description="每页大小")


class UserPermission(BaseModel):
    """用户权限模型"""
    user_id: int
    bid: str
    permissions: List[str] = Field(..., description="权限列表，如['read', 'write', 'admin']")
    sid_list: Optional[List[str]] = Field(None, description="SID列表")
    
    
class UserQuota(BaseModel):
    """用户配额模型"""
    monthly_report_limit: int = Field(100, description="月度报告限制")
    daily_query_limit: int = Field(1000, description="每日查询限制")
    monthly_report_count: int = Field(0, description="当月报告数量")
    daily_query_count: int = Field(0, description="当日查询数量")
    last_reset_date: str = Field(..., description="最后重置日期")