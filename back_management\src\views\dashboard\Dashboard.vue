<template>
  <div class="dashboard-container">
    <h2 class="page-title">仪表盘</h2>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stat-cards">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <el-statistic :value="statistics.totalUsers">
              <template #title>
                <div class="stat-title">
                  <el-icon color="#409eff"><User /></el-icon>
                  <span>用户总数</span>
                </div>
              </template>
            </el-statistic>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <el-statistic :value="statistics.activeUsers">
              <template #title>
                <div class="stat-title">
                  <el-icon color="#67c23a"><CircleCheckFilled /></el-icon>
                  <span>活跃用户</span>
                </div>
              </template>
            </el-statistic>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <el-statistic :value="statistics.totalBids">
              <template #title>
                <div class="stat-title">
                  <el-icon color="#e6a23c"><FolderOpened /></el-icon>
                  <span>BID配置</span>
                </div>
              </template>
            </el-statistic>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <el-statistic :value="statistics.todayQueries">
              <template #title>
                <div class="stat-title">
                  <el-icon color="#f56c6c"><DataLine /></el-icon>
                  <span>今日查询</span>
                </div>
              </template>
            </el-statistic>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 最近活动 -->
    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近注册用户</span>
              <el-button text @click="$router.push('/users')">查看全部</el-button>
            </div>
          </template>
          
          <el-table :data="recentUsers" style="width: 100%">
            <el-table-column prop="username" label="用户名" />
            <el-table-column prop="role" label="角色">
              <template #default="{ row }">
                <el-tag :type="getRoleType(row.role)" size="small">
                  {{ getRoleLabel(row.role) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="注册时间">
              <template #default="{ row }">
                {{ formatDate(row.created_at) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>系统信息</span>
            </div>
          </template>
          
          <div class="system-info">
            <div class="info-item">
              <span class="info-label">系统版本：</span>
              <span class="info-value">v1.0.0</span>
            </div>
            <div class="info-item">
              <span class="info-label">当前用户：</span>
              <span class="info-value">{{ authStore.username }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">用户角色：</span>
              <span class="info-value">
                <el-tag :type="getRoleType(authStore.userRole)">
                  {{ getRoleLabel(authStore.userRole) }}
                </el-tag>
              </span>
            </div>
            <div class="info-item">
              <span class="info-label">登录时间：</span>
              <span class="info-value">{{ loginTime }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">系统时间：</span>
              <span class="info-value">{{ currentTime }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { usersAPI } from '@/api/users'
import { bidAPI } from '@/api/bid'
import { ElMessage } from 'element-plus'

const authStore = useAuthStore()

// 统计数据
const statistics = reactive({
  totalUsers: 0,
  activeUsers: 0,
  totalBids: 0,
  todayQueries: 0
})

// 最近用户
const recentUsers = ref([])

// 时间相关
const loginTime = ref(new Date().toLocaleString('zh-CN'))
const currentTime = ref(new Date().toLocaleString('zh-CN'))
let timeInterval = null

// 获取统计数据
const fetchStatistics = async () => {
  try {
    // 使用新的综合统计接口
    const dashboardStats = await usersAPI.getDashboardStatistics()
    console.log('仪表盘统计数据:', dashboardStats)
    
    // 设置用户统计
    statistics.totalUsers = dashboardStats.user_statistics?.total_users || 0
    statistics.activeUsers = dashboardStats.user_statistics?.active_users || 0
    
    // 设置BID统计
    statistics.totalBids = dashboardStats.bid_statistics?.total_bids || 0
    
    // 设置今日查询统计（使用实际的查询数）
    statistics.todayQueries = dashboardStats.today_statistics?.query_count || 0
    
    // 如果需要，也可以显示今日登录数或其他统计
    console.log('今日登录数:', dashboardStats.user_statistics?.today_login_count)
    console.log('今日PPT生成数:', dashboardStats.today_statistics?.ppt_generation_count)
    console.log('今日活跃用户数:', dashboardStats.today_statistics?.active_users)
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 获取最近用户
const fetchRecentUsers = async () => {
  try {
    const response = await usersAPI.getUsers({ 
      page: 1,
      page_size: 5
    })
    console.log('最近用户数据:', response)
    recentUsers.value = response.items || []
  } catch (error) {
    console.error('获取最近用户失败:', error)
  }
}

// 获取角色标签
const getRoleLabel = (role) => {
  const roleMap = {
    'super_admin': '超级管理员',
    'admin': '管理员',
    'user': '普通用户'
  }
  return roleMap[role] || role
}

// 获取角色类型
const getRoleType = (role) => {
  const typeMap = {
    'super_admin': 'danger',
    'admin': 'warning',
    'user': 'info'
  }
  return typeMap[role] || 'info'
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 更新当前时间
const updateCurrentTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN')
}

onMounted(() => {
  fetchStatistics()
  fetchRecentUsers()
  
  // 每秒更新时间
  timeInterval = setInterval(updateCurrentTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.page-title {
  margin: 0 0 20px 0;
  font-size: 24px;
  color: #303133;
}

.stat-cards {
  margin-bottom: 20px;
}

.stat-card {
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.stat-item {
  padding: 10px 0;
}

.stat-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #909399;
  font-size: 14px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.system-info {
  padding: 10px 0;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  width: 100px;
  color: #909399;
  font-size: 14px;
}

.info-value {
  flex: 1;
  color: #303133;
  font-size: 14px;
}

:deep(.el-statistic__content) {
  font-size: 28px;
  font-weight: bold;
}

:deep(.el-statistic__head) {
  margin-bottom: 10px;
}
</style>