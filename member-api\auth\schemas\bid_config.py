"""
BID配置相关的Pydantic模型
"""
from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum


class BidStatus(str, Enum):
    """BID状态枚举"""
    PENDING = "pending"      # 待审批
    APPROVED = "approved"    # 已批准
    REJECTED = "rejected"    # 已拒绝
    ACTIVE = "active"        # 已激活
    INACTIVE = "inactive"    # 已停用


class BusinessType(str, Enum):
    """业态类型枚举"""
    FAST_FOOD = "fast_food"           # 快餐
    CASUAL_DINING = "casual_dining"   # 休闲餐
    FINE_DINING = "fine_dining"       # 正餐


class BidConfigBase(BaseModel):
    """BID配置基础模型"""
    bid: str = Field(..., min_length=1, max_length=50, description="BID代码")
    bid_name: str = Field(..., min_length=1, max_length=100, description="BID名称")
    description: Optional[str] = Field(None, description="BID描述")
    business_type: BusinessType = Field(..., description="业态类型")
    contact_name: Optional[str] = Field(None, max_length=100, description="联系人姓名")
    contact_phone: Optional[str] = Field(None, max_length=20, description="联系人电话")
    contact_email: Optional[str] = Field(None, max_length=100, description="联系人邮箱")
    company_name: Optional[str] = Field(None, max_length=200, description="公司名称")
    industry: Optional[str] = Field(None, max_length=100, description="所属行业")
    remark: Optional[str] = Field(None, description="备注信息")


class BidConfigCreate(BidConfigBase):
    """创建BID配置模型"""
    owner_username: Optional[str] = Field(None, max_length=50, description="所有者用户名")
    sid_list: Optional[List[str]] = Field(None, description="SID列表")
    
    @validator('bid')
    def validate_bid(cls, v):
        """验证BID格式"""
        if not v or len(v.strip()) == 0:
            raise ValueError('BID不能为空')
        # 可以添加更多BID格式验证
        return v.strip()


class BidConfigUpdate(BaseModel):
    """更新BID配置模型"""
    bid_name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None
    business_type: Optional[BusinessType] = None
    owner_username: Optional[str] = None
    status: Optional[BidStatus] = None
    sid_list: Optional[List[str]] = None
    contact_name: Optional[str] = Field(None, max_length=100)
    contact_phone: Optional[str] = Field(None, max_length=20)
    contact_email: Optional[str] = Field(None, max_length=100)
    company_name: Optional[str] = Field(None, max_length=200)
    industry: Optional[str] = Field(None, max_length=100)
    remark: Optional[str] = None


class BidConfigInDB(BidConfigBase):
    """数据库中的BID配置模型"""
    id: int
    owner_username: Optional[str] = None
    status: BidStatus
    sid_list: Optional[List[str]] = None
    created_at: datetime
    updated_at: datetime
    deleted_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.strftime("%Y-%m-%d %H:%M:%S") if v else None
        }


class BidConfigResponse(BaseModel):
    """BID配置响应模型"""
    id: int
    bid: str
    bid_name: str
    description: Optional[str] = None
    business_type: Optional[BusinessType] = None
    owner_username: Optional[str] = None
    status: BidStatus
    sid_list: Optional[List[str]] = None
    contact_name: Optional[str] = None
    contact_phone: Optional[str] = None
    contact_email: Optional[str] = None
    company_name: Optional[str] = None
    industry: Optional[str] = None
    created_at: str
    updated_at: str
    
    class Config:
        from_attributes = True
        
    @validator('created_at', 'updated_at', pre=True)
    def format_datetime(cls, v):
        """格式化日期时间"""
        if isinstance(v, datetime):
            return v.strftime("%Y-%m-%d %H:%M:%S")
        return v


class BidConfigList(BaseModel):
    """BID配置列表响应"""
    total: int = Field(..., description="总数")
    items: List[BidConfigResponse] = Field(..., description="BID配置列表")
    page: int = Field(1, description="当前页")
    page_size: int = Field(20, description="每页大小")


class SidConfig(BaseModel):
    """SID配置模型"""
    sid: str = Field(..., description="SID")
    name: str = Field(..., description="SID名称")
    status: str = Field("active", description="状态")
    description: Optional[str] = Field(None, description="描述")
    config: Optional[Dict[str, Any]] = Field(None, description="配置信息")


class BidSidMapping(BaseModel):
    """BID-SID映射模型"""
    bid: str
    sid_list: List[str]
    sid_configs: Optional[Dict[str, SidConfig]] = None