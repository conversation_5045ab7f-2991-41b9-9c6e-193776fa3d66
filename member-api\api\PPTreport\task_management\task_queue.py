"""
任务队列处理模块
负责管理任务的并发执行和队列调度
"""

import asyncio
import logging
from typing import Optional, Callable, Dict, Any, List
from datetime import datetime
from core.config import settings
from .models import TaskModel, TaskStatus
from .task_manager import TaskManager
from .task_executor import ppt_task_executor

logger = logging.getLogger(__name__)


class TaskQueue:
    """任务队列管理器"""
    
    def __init__(self, max_concurrent_tasks: Optional[int] = None):
        """
        初始化任务队列
        
        Args:
            max_concurrent_tasks: 最大并发任务数，None则从配置读取
        """
        self.task_manager = TaskManager()
        self.max_concurrent_tasks = max_concurrent_tasks or getattr(
            settings, 'MAX_CONCURRENT_TASKS', 1
        )
        self.running_tasks: Dict[int, asyncio.Task] = {}
        # 默认使用PPT任务执行器
        self.task_executor: Optional[Callable] = ppt_task_executor.execute_task
        self._running = False
        self._queue_task: Optional[asyncio.Task] = None
        
        logger.info(f"任务队列初始化完成，最大并发数: {self.max_concurrent_tasks}")
    
    def set_task_executor(self, executor: Callable):
        """
        设置任务执行器
        
        Args:
            executor: 异步任务执行函数，接收TaskModel参数
        """
        self.task_executor = executor
        logger.info("任务执行器已设置")
    
    async def start(self):
        """启动任务队列处理"""
        if self._running:
            logger.warning("任务队列已在运行中")
            return
        
        self._running = True
        self._queue_task = asyncio.create_task(self._process_queue())
        logger.info("任务队列处理已启动")
    
    async def stop(self):
        """停止任务队列处理"""
        self._running = False
        
        # 等待队列任务完成
        if self._queue_task:
            self._queue_task.cancel()
            try:
                await self._queue_task
            except asyncio.CancelledError:
                pass
        
        # 等待所有运行中的任务完成
        if self.running_tasks:
            logger.info(f"等待 {len(self.running_tasks)} 个任务完成...")
            await asyncio.gather(*self.running_tasks.values(), return_exceptions=True)
        
        logger.info("任务队列处理已停止")
    
    async def _process_queue(self):
        """处理任务队列的主循环"""
        logger.info("任务队列处理循环开始")
        
        while self._running:
            try:
                # 清理已完成的任务
                await self._cleanup_completed_tasks()
                
                # 检查是否可以启动新任务
                available_slots = self.max_concurrent_tasks - len(self.running_tasks)
                
                if available_slots > 0:
                    # 获取待处理任务
                    pending_tasks = await self.task_manager.get_pending_tasks(limit=available_slots)
                    
                    # 启动新任务
                    for task in pending_tasks:
                        await self._start_task(task)
                
                # 使用配置的轮询间隔
                poll_interval = getattr(settings, 'WORKER_POLL_INTERVAL', 5)
                await asyncio.sleep(poll_interval)
                
            except Exception as e:
                logger.error(f"任务队列处理出错: {e}", exc_info=True)
                await asyncio.sleep(5)  # 出错后等待更长时间
    
    async def _cleanup_completed_tasks(self):
        """清理已完成的任务"""
        completed_task_ids = []
        
        for task_id, task_future in self.running_tasks.items():
            if task_future.done():
                completed_task_ids.append(task_id)
                
                # 检查任务是否有异常
                try:
                    task_future.result()
                except Exception as e:
                    logger.error(f"任务执行异常: task_id={task_id}, error={e}")
        
        # 从运行列表中移除已完成的任务
        for task_id in completed_task_ids:
            del self.running_tasks[task_id]
            logger.info(f"任务已从运行列表中移除: ID={task_id}")
    
    async def _start_task(self, task: TaskModel):
        """启动任务执行"""
        try:
            # 更新任务状态为处理中
            await self.task_manager.update_task_status(
                task.id,
                TaskStatus.PROCESSING,
                current_step="任务开始执行"
            )
            
            # 创建任务执行协程
            if self.task_executor:
                task_coroutine = self._execute_task_with_error_handling(task)
            else:
                # 如果没有设置执行器，使用默认的模拟执行
                task_coroutine = self._simulate_task_execution(task)
            
            # 创建异步任务并添加到运行列表
            task_future = asyncio.create_task(task_coroutine)
            self.running_tasks[task.id] = task_future
            
            logger.info(f"任务已启动: ID={task.id}, UUID={task.task_uuid}")
            
        except Exception as e:
            logger.error(f"启动任务失败: {e}")
            await self.task_manager.mark_task_failed(
                task.id,
                f"任务启动失败: {str(e)}"
            )
    
    async def _execute_task_with_error_handling(self, task: TaskModel):
        """带错误处理的任务执行"""
        try:
            # 执行实际的任务
            result = await self.task_executor(task)
            
            # 更新任务为完成状态
            await self.task_manager.update_task_status(
                task.id,
                TaskStatus.COMPLETED,
                current_step="任务执行完成",
                progress=100
            )
            
            logger.info(f"任务执行成功: ID={task.id}")
            return result
            
        except Exception as e:
            logger.error(f"任务执行失败: ID={task.id}, error={e}")
            
            # 标记任务失败
            await self.task_manager.mark_task_failed(
                task.id,
                f"任务执行失败: {str(e)}",
                error_details={"exception": str(type(e).__name__)}
            )
            
            raise
    
    async def _simulate_task_execution(self, task: TaskModel):
        """模拟任务执行（用于测试）"""
        logger.info(f"开始模拟执行任务: ID={task.id}")
        
        try:
            # 模拟任务执行的各个步骤
            steps = [
                ("数据准备", 20),
                ("生成Excel", 40),
                ("生成图表", 60),
                ("生成PPT", 80),
                ("上传文件", 100)
            ]
            
            for step_name, progress in steps:
                await self.task_manager.update_task_progress(
                    task.id,
                    progress,
                    step_name
                )
                
                # 模拟每个步骤的执行时间
                await asyncio.sleep(2)
            
            # 更新任务完成
            await self.task_manager.update_task_status(
                task.id,
                TaskStatus.COMPLETED,
                current_step="任务完成"
            )
            
            logger.info(f"任务模拟执行完成: ID={task.id}")
            
        except Exception as e:
            logger.error(f"任务模拟执行失败: ID={task.id}, error={e}")
            raise
    
    async def add_task(self, task: TaskModel) -> TaskModel:
        """
        添加任务到队列
        
        Args:
            task: 任务模型
            
        Returns:
            创建后的任务模型（包含ID）
        """
        # 创建任务记录
        created_task = await self.task_manager.create_task(task)
        
        logger.info(f"任务已添加到队列: ID={created_task.id}, UUID={created_task.task_uuid}")
        
        # 如果队列正在运行且有空闲槽位，立即尝试执行
        if self._running and len(self.running_tasks) < self.max_concurrent_tasks:
            # 触发队列处理（异步，不等待）
            asyncio.create_task(self._try_start_immediate(created_task))
        
        return created_task
    
    async def _try_start_immediate(self, task: TaskModel):
        """尝试立即启动任务"""
        try:
            # 再次检查槽位
            if len(self.running_tasks) < self.max_concurrent_tasks:
                await self._start_task(task)
        except Exception as e:
            logger.error(f"立即启动任务失败: {e}")
    
    def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态"""
        return {
            "is_running": self._running,
            "max_concurrent_tasks": self.max_concurrent_tasks,
            "running_tasks_count": len(self.running_tasks),
            "running_task_ids": list(self.running_tasks.keys()),
            "available_slots": self.max_concurrent_tasks - len(self.running_tasks)
        }
    
    async def cancel_task(self, task_id: int) -> bool:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功取消
        """
        # 如果任务正在运行，取消它
        if task_id in self.running_tasks:
            task_future = self.running_tasks[task_id]
            task_future.cancel()
            
            # 从运行列表中移除
            del self.running_tasks[task_id]
            logger.info(f"运行中的任务已取消: ID={task_id}")
        
        # 更新数据库中的任务状态
        return await self.task_manager.cancel_task(task_id)
    
    async def get_task_info(self, task_id: int) -> Optional[TaskModel]:
        """
        获取任务信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务模型或None
        """
        return await self.task_manager.get_task_by_id(task_id)
    
    async def get_user_tasks(
        self,
        user_id: str,  # 修改为字符串类型，存储username
        status: Optional[TaskStatus] = None,
        limit: int = 20
    ) -> List[TaskModel]:
        """
        获取用户的任务列表
        
        Args:
            user_id: 用户名(username)
            status: 任务状态过滤
            limit: 返回数量限制
            
        Returns:
            任务列表
        """
        return await self.task_manager.get_user_tasks(user_id, status, limit)


# 全局任务队列实例
task_queue_instance: Optional[TaskQueue] = None


def get_task_queue() -> TaskQueue:
    """获取全局任务队列实例"""
    global task_queue_instance
    if task_queue_instance is None:
        task_queue_instance = TaskQueue()
    return task_queue_instance


async def init_task_queue():
    """初始化并启动任务队列"""
    queue = get_task_queue()
    await queue.start()
    return queue


async def shutdown_task_queue():
    """关闭任务队列"""
    if task_queue_instance:
        await task_queue_instance.stop()