#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
图片设置工具
用于创建示例图片和验证图片配置
"""

import os
import sys
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from data_reports.config import IMAGES_DIR, COMPANY_LOGO, DATA_CHART, MEMBER_TREND

def create_sample_image(width, height, text, filename, bg_color=(255, 255, 255), text_color=(0, 0, 0)):
    """
    创建示例图片
    
    Args:
        width: 图片宽度
        height: 图片高度
        text: 图片上的文字
        filename: 保存的文件名
        bg_color: 背景颜色 (R, G, B)
        text_color: 文字颜色 (R, G, B)
    """
    try:
        # 创建图片
        img = Image.new('RGB', (width, height), bg_color)
        draw = ImageDraw.Draw(img)
        
        # 尝试使用系统字体，如果失败则使用默认字体
        try:
            # Windows系统字体
            font = ImageFont.truetype("arial.ttf", 24)
        except:
            try:
                # 备用字体
                font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", 24)
            except:
                # 默认字体
                font = ImageFont.load_default()
        
        # 计算文字位置（居中）
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        x = (width - text_width) // 2
        y = (height - text_height) // 2
        
        # 绘制文字
        draw.text((x, y), text, fill=text_color, font=font)
        
        # 绘制边框
        draw.rectangle([0, 0, width-1, height-1], outline=(200, 200, 200), width=2)
        
        # 保存图片
        img.save(filename)
        print(f"✅ 创建示例图片: {filename}")
        return True
        
    except Exception as e:
        print(f"❌ 创建图片失败 {filename}: {e}")
        return False

def setup_sample_images():
    """创建所有示例图片"""
    print("🎨 开始创建示例图片...")
    
    # 确保图片目录存在
    os.makedirs(IMAGES_DIR, exist_ok=True)
    
    success_count = 0
    
    # 创建公司Logo示例
    if create_sample_image(300, 150, "公司 LOGO", COMPANY_LOGO, (240, 248, 255), (0, 100, 200)):
        success_count += 1
    
    # 创建数据图表示例
    if create_sample_image(600, 400, "会员数据图表", DATA_CHART, (248, 248, 255), (50, 50, 150)):
        success_count += 1
    
    # 创建趋势图示例
    if create_sample_image(800, 300, "会员增长趋势图", MEMBER_TREND, (255, 248, 240), (200, 100, 0)):
        success_count += 1
    
    print(f"\n📊 图片创建完成: {success_count}/3 个图片创建成功")
    return success_count == 3

def check_images():
    """检查图片文件是否存在"""
    print("\n🔍 检查图片文件...")
    
    images = {
        "公司Logo": COMPANY_LOGO,
        "数据图表": DATA_CHART,
        "趋势图": MEMBER_TREND
    }
    
    all_exist = True
    for name, path in images.items():
        if os.path.exists(path):
            size = os.path.getsize(path)
            print(f"✅ {name}: {path} ({size} bytes)")
        else:
            print(f"❌ {name}: {path} (文件不存在)")
            all_exist = False
    
    return all_exist

def show_usage_guide():
    """显示使用指南"""
    print("\n" + "="*60)
    print("📖 图片参数使用指南")
    print("="*60)
    print("""
1. 在PPT模板中添加图片占位符:
   - 创建文本框
   - 输入图片参数: {image_logo}, {image_chart}, {image_trend}
   
2. 替换示例图片:
   - 将您的图片文件复制到 data_reports/images/ 目录
   - 重命名为对应的文件名，或修改配置文件中的路径
   
3. 支持的图片格式:
   - PNG (推荐)
   - JPG/JPEG
   - GIF
   - BMP
   
4. 图片参数配置位置:
   - 文件: data_reports/config.py
   - 变量: MEMBER_DATA 字典中以 'image_' 开头的键
   
5. 运行报告生成:
   python data_reports/generate_member_report.py
""")
    print("="*60)

def main():
    """主函数"""
    print("🖼️  图片设置工具")
    print("="*40)
    
    # 检查PIL库是否可用
    try:
        from PIL import Image
    except ImportError:
        print("❌ 错误: 需要安装 Pillow 库")
        print("请运行: pip install Pillow")
        return False
    
    # 检查现有图片
    if check_images():
        print("\n✅ 所有图片文件都已存在")
        choice = input("\n是否要重新创建示例图片? (y/N): ").strip().lower()
        if choice not in ['y', 'yes']:
            show_usage_guide()
            return True
    
    # 创建示例图片
    success = setup_sample_images()
    
    if success:
        print("\n🎉 图片设置完成!")
        show_usage_guide()
    else:
        print("\n💥 图片设置失败!")
        print("请检查目录权限和依赖库安装情况")
    
    return success

if __name__ == "__main__":
    main()
