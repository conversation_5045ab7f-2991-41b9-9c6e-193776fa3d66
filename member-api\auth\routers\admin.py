"""
管理员路由
处理用户管理、BID配置管理等管理功能
"""
import logging
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request

from core.database import db
from ..schemas.user import (
    UserCreate, UserUpdate, UserResponse, UserList,
    UserPasswordUpdate, UserRole
)
from ..schemas.bid_config import (
    BidConfigCreate, BidConfigUpdate, BidConfigResponse, BidConfigList
)
from ..services.user_service import UserService
from ..services.bid_service import BidService
from ..services.audit_service import AuditService
from ..services.bid_approval_service import BidApprovalService
from ..middleware.auth_middleware import require_admin, require_super_admin, get_current_user

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/admin",
    tags=["管理员"]
)


# ========== 用户管理 ==========

@router.get("/users", response_model=UserList, summary="获取用户列表")
async def get_users(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    role: Optional[str] = Query(None, description="角色筛选"),
    status: Optional[str] = Query(None, description="状态筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    current_user: dict = Depends(require_admin)
):
    """
    获取用户列表（需要管理员权限）
    
    - **page**: 页码
    - **page_size**: 每页大小
    - **role**: 角色筛选（super_admin/admin/user）
    - **status**: 状态筛选（active/inactive/banned）
    - **search**: 搜索关键词（用户名/邮箱/真实姓名）
    """
    try:
        user_service = UserService(db.task_management_pool)
        return await user_service.get_user_list(page, page_size, role, status, search)
        
    except Exception as e:
        logger.error(f"获取用户列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户列表失败"
        )


@router.get("/users/{user_id}", response_model=UserResponse, summary="获取用户详情")
async def get_user(
    user_id: int,
    current_user: dict = Depends(require_admin)
):
    """
    获取用户详情（需要管理员权限）
    """
    try:
        user_service = UserService(db.task_management_pool)
        user = await user_service.get_user_by_id(user_id)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        return user
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户详情失败"
        )


@router.post("/users", response_model=UserResponse, summary="创建用户")
async def create_user(
    user_data: UserCreate,
    request: Request,
    current_user: dict = Depends(require_admin)
):
    """
    创建新用户（需要管理员权限）
    
    - 普通管理员只能创建普通用户
    - 超级管理员可以创建任何角色的用户
    """
    try:
        # 权限检查：普通管理员不能创建管理员账户
        if user_data.role in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
            if current_user.get('role') != 'super_admin':
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有超级管理员可以创建管理员账户"
                )
        
        user_service = UserService(db.task_management_pool)
        audit_service = AuditService(db.task_management_pool)
        user = await user_service.create_user(user_data, created_by=current_user.get('username'))
        
        if not user:
            # 记录创建失败
            await audit_service.log_user_operation(
                action="create_user",
                operator_id=current_user.get('user_id'),
                operator_username=current_user.get('username'),
                request_data=user_data.dict(),
                success=False,
                error_message="创建用户失败",
                ip_address=request.client.host if request.client else None,
                user_agent=request.headers.get("user-agent")
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="创建用户失败"
            )
        
        # 记录创建成功
        await audit_service.log_user_operation(
            action="create_user",
            operator_id=current_user.get('user_id'),
            operator_username=current_user.get('username'),
            target_user_id=user.id,
            request_data={"username": user_data.username, "role": user_data.role},
            success=True,
            ip_address=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent")
        )
        
        return user
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建用户失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建用户失败"
        )


@router.put("/users/{user_id}", response_model=UserResponse, summary="更新用户")
async def update_user(
    user_id: int,
    user_data: UserUpdate,
    current_user: dict = Depends(require_admin)
):
    """
    更新用户信息（需要管理员权限）
    
    - 普通管理员不能修改管理员账户
    - 超级管理员可以修改任何账户
    """
    try:
        user_service = UserService(db.task_management_pool)
        
        # 获取目标用户信息
        target_user = await user_service.get_user_by_id(user_id)
        if not target_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 权限检查
        if target_user.role in ['admin', 'super_admin']:
            if current_user.get('role') != 'super_admin':
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有超级管理员可以修改管理员账户"
                )
        
        # 防止降级超级管理员权限
        if target_user.role == 'super_admin' and user_data.role != UserRole.SUPER_ADMIN:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="不能降级超级管理员权限"
            )
        
        user = await user_service.update_user(user_id, user_data)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="更新用户失败"
            )
        
        return user
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新用户失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新用户失败"
        )


@router.delete("/users/{user_id}", summary="删除用户")
async def delete_user(
    user_id: int,
    hard_delete: bool = Query(False, description="是否硬删除"),
    current_user: dict = Depends(require_super_admin)
):
    """
    删除用户（需要超级管理员权限）
    
    - **hard_delete**: 是否硬删除（永久删除）
    """
    try:
        # 防止删除自己
        if user_id == current_user.get('user_id'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能删除自己的账户"
            )
        
        user_service = UserService(db.task_management_pool)
        success = await user_service.delete_user(user_id, hard_delete)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        return {"success": True, "message": "用户删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除用户失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除用户失败"
        )


@router.patch("/users/{user_id}/status", summary="更新用户状态")
async def update_user_status(
    user_id: int,
    status_data: dict,  # 接收包含 status 的字典
    current_user: dict = Depends(require_admin)
):
    """
    更新用户状态（需要管理员权限）
    """
    try:
        new_status = status_data.get('status')
        if not new_status:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请提供状态"
            )
        
        # 防止禁用自己
        if user_id == current_user.get('user_id') and new_status == 'inactive':
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能禁用自己的账户"
            )
        
        user_service = UserService(db.task_management_pool)
        from ..schemas.user import UserUpdate, UserStatus
        
        # 构建更新数据
        update_data = UserUpdate(status=UserStatus(new_status))
        user = await user_service.update_user(user_id, update_data)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        return {"success": True, "message": "状态更新成功"}
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新用户状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新用户状态失败"
        )


@router.post("/users/{user_id}/reset-password", summary="重置用户密码")
async def reset_user_password(
    user_id: int,
    password_data: dict,  # 接收包含 new_password 的字典
    current_user: dict = Depends(require_admin)
):
    """
    重置用户密码（需要管理员权限）
    """
    try:
        new_password = password_data.get('new_password')
        if not new_password:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请提供新密码"
            )
            
        if len(new_password) < 6:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="密码长度至少6位"
            )
        
        user_service = UserService(db.task_management_pool)
        success = await user_service.reset_password(user_id, new_password)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        return {"success": True, "message": "密码重置成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重置密码失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="重置密码失败"
        )


@router.patch("/users/{user_id}/quota", summary="更新用户配额")
async def update_user_quota(
    user_id: int,
    quota_data: dict,  # 接收配额数据
    current_user: dict = Depends(require_admin)
):
    """
    更新用户配额（需要管理员权限）
    """
    try:
        user_service = UserService(db.task_management_pool)
        from ..schemas.user import UserUpdate
        
        # 从字典中提取配额数值
        quota_value = quota_data.get('quotas', 0)
        if not isinstance(quota_value, int):
            quota_value = int(quota_value) if quota_value else 0
            
        # 构建更新数据
        update_data = UserUpdate(quotas=quota_value)
        user = await user_service.update_user(user_id, update_data)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        return {"success": True, "message": "配额更新成功", "data": user}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新用户配额失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新用户配额失败"
        )


@router.get("/users/statistics", summary="获取用户统计信息")
async def get_user_statistics(
    current_user: dict = Depends(require_admin)
):
    """
    获取用户统计信息（需要管理员权限）
    """
    try:
        user_service = UserService(db.task_management_pool)
        stats = await user_service.get_user_statistics()
        return stats
        
    except Exception as e:
        logger.error(f"获取用户统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取统计信息失败"
        )


# ========== BID配置管理 ==========

@router.get("/bid-configs", response_model=BidConfigList, summary="获取BID配置列表")
async def get_bid_configs(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    status: Optional[str] = Query(None, description="状态筛选"),
    owner_username: Optional[str] = Query(None, description="所有者用户名"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    current_user: dict = Depends(require_admin)
):
    """
    获取BID配置列表（需要管理员权限）
    """
    try:
        bid_service = BidService(db.task_management_pool)
        return await bid_service.get_bid_list(page, page_size, status, owner_username, search)
        
    except Exception as e:
        logger.error(f"获取BID配置列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取BID配置列表失败"
        )


@router.get("/bid-configs/{bid_id}", response_model=BidConfigResponse, summary="获取BID配置详情")
async def get_bid_config(
    bid_id: int,
    current_user: dict = Depends(require_admin)
):
    """
    获取BID配置详情（需要管理员权限）
    """
    try:
        bid_service = BidService(db.task_management_pool)
        config = await bid_service.get_bid_by_id(bid_id)
        
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="BID配置不存在"
            )
        
        return config
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取BID配置详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取BID配置详情失败"
        )


@router.post("/bid-configs", response_model=BidConfigResponse, summary="创建BID配置")
async def create_bid_config(
    bid_data: BidConfigCreate,
    current_user: dict = Depends(require_admin)
):
    """
    创建BID配置（需要管理员权限）
    """
    try:
        # 如果没有指定所有者，默认为当前用户的用户名
        if not bid_data.owner_username:
            bid_data.owner_username = current_user.get('username')
        
        bid_service = BidService(db.task_management_pool)
        config = await bid_service.create_bid(bid_data)
        
        if not config:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="创建BID配置失败"
            )
        
        return config
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建BID配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建BID配置失败"
        )


@router.put("/bid-configs/{bid_id}", response_model=BidConfigResponse, summary="更新BID配置")
async def update_bid_config(
    bid_id: int,
    bid_data: BidConfigUpdate,
    current_user: dict = Depends(require_admin)
):
    """
    更新BID配置（需要管理员权限）
    """
    try:
        bid_service = BidService(db.task_management_pool)
        config = await bid_service.update_bid(bid_id, bid_data)
        
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="BID配置不存在"
            )
        
        return config
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新BID配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新BID配置失败"
        )


@router.delete("/bid-configs/{bid_id}", summary="删除BID配置")
async def delete_bid_config(
    bid_id: int,
    hard_delete: bool = Query(False, description="是否硬删除"),
    current_user: dict = Depends(require_super_admin)
):
    """
    删除BID配置（需要超级管理员权限）
    """
    try:
        bid_service = BidService(db.task_management_pool)
        success = await bid_service.delete_bid(bid_id, hard_delete)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="BID配置不存在"
            )
        
        return {"success": True, "message": "BID配置删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除BID配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除BID配置失败"
        )


@router.patch("/bid-configs/{bid_id}/status", summary="更新BID状态")
async def update_bid_status(
    bid_id: int,
    status_data: dict,  # 接收包含 status 的字典
    current_user: dict = Depends(require_admin)
):
    """
    更新BID配置状态（需要管理员权限）
    """
    try:
        new_status = status_data.get('status')
        if not new_status:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请提供状态"
            )
        
        bid_service = BidService(db.task_management_pool)
        from ..schemas.bid_config import BidConfigUpdate, BidStatus
        
        # 构建更新数据
        update_data = BidConfigUpdate(status=BidStatus(new_status))
        config = await bid_service.update_bid(bid_id, update_data)
        
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="BID配置不存在"
            )
        
        return {"success": True, "message": "状态更新成功"}
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新BID状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新BID状态失败"
        )


@router.get("/bid-configs/{bid_id}/statistics", summary="获取BID统计信息")
async def get_bid_statistics(
    bid_id: int,
    current_user: dict = Depends(require_admin)
):
    """
    获取BID统计信息（需要管理员权限）
    """
    try:
        bid_service = BidService(db.task_management_pool)
        stats = await bid_service.get_bid_statistics(bid_id)
        return stats
        
    except Exception as e:
        logger.error(f"获取BID统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取统计信息失败"
        )


@router.get("/dashboard/statistics", summary="获取仪表盘统计数据")
async def get_dashboard_statistics(
    current_user: dict = Depends(require_admin)
):
    """
    获取仪表盘综合统计数据（需要管理员权限）
    
    返回:
    - 用户统计：总数、活跃数、角色分布
    - BID统计：总数、活跃数
    - 今日统计：登录数、查询数、PPT生成数
    """
    try:
        # 获取用户统计
        user_service = UserService(db.task_management_pool)
        user_stats = await user_service.get_user_statistics()
        
        # 获取BID统计
        bid_service = BidService(db.task_management_pool)
        bid_list = await bid_service.get_bid_list(page=1, page_size=1)  # 只获取总数
        
        # 获取今日操作统计
        from ..services.audit_service import AuditService
        audit_service = AuditService(db.task_management_pool)
        today_stats = await audit_service.get_today_statistics()
        
        # 组合统计数据
        return {
            "user_statistics": {
                "total_users": user_stats.get("total_users", 0),
                "active_users": user_stats.get("status_distribution", {}).get("active", 0),
                "inactive_users": user_stats.get("status_distribution", {}).get("inactive", 0),
                "role_distribution": user_stats.get("role_distribution", {}),
                "today_login_count": user_stats.get("today_login_count", 0)
            },
            "bid_statistics": {
                "total_bids": bid_list.total if bid_list else 0,
                "active_bids": 0  # TODO: 需要在BidService中实现活跃BID统计
            },
            "today_statistics": {
                "total_actions": today_stats.get("total_actions", 0),
                "login_count": today_stats.get("login_count", 0),
                "query_count": today_stats.get("query_count", 0),
                "ppt_generation_count": today_stats.get("ppt_generation_count", 0),
                "active_users": today_stats.get("active_users", 0)
            }
        }
        
    except Exception as e:
        logger.error(f"获取仪表盘统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取统计信息失败"
        )


# ========== BID申请审批管理 ==========

@router.get("/bid-approvals/pending", summary="获取待审批的BID申请列表")
async def get_pending_bid_applications(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    current_user: dict = Depends(require_admin)
):
    """
    获取待审批的BID申请列表（需要管理员权限）
    """
    try:
        approval_service = BidApprovalService(db.task_management_pool)
        result = await approval_service.get_pending_applications(page, page_size, search)
        return result
        
    except Exception as e:
        logger.error(f"获取待审批列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取待审批列表失败"
        )


@router.post("/bid-approvals/{bid}/approve", summary="批准BID申请")
async def approve_bid_application(
    bid: str,
    approval_data: dict,
    current_user: dict = Depends(require_admin)
):
    """
    批准BID申请（需要管理员权限）
    
    Args:
        bid: BID代码
        approval_data: 包含 auto_activate 和 comment 的字典
    """
    try:
        approval_service = BidApprovalService(db.task_management_pool)
        result = await approval_service.approve_application(
            bid=bid,
            admin_username=current_user.get('username'),
            auto_activate=approval_data.get('auto_activate', True),
            comment=approval_data.get('comment')
        )
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['message']
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批准BID申请失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="批准申请失败"
        )


@router.post("/bid-approvals/{bid}/reject", summary="拒绝BID申请")
async def reject_bid_application(
    bid: str,
    rejection_data: dict,
    current_user: dict = Depends(require_admin)
):
    """
    拒绝BID申请（需要管理员权限）
    
    Args:
        bid: BID代码
        rejection_data: 包含 reject_reason 的字典
    """
    try:
        reject_reason = rejection_data.get('reject_reason')
        if not reject_reason:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请提供拒绝理由"
            )
        
        approval_service = BidApprovalService(db.task_management_pool)
        result = await approval_service.reject_application(
            bid=bid,
            admin_username=current_user.get('username'),
            reject_reason=reject_reason
        )
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['message']
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"拒绝BID申请失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="拒绝申请失败"
        )


@router.get("/bid-approvals/history", summary="获取审批历史")
async def get_bid_approval_history(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    status: Optional[str] = Query(None, description="状态筛选"),
    current_user: dict = Depends(require_admin)
):
    """
    获取BID申请审批历史（需要管理员权限）
    """
    try:
        approval_service = BidApprovalService(db.task_management_pool)
        result = await approval_service.get_approval_history(page, page_size, status)
        return result
        
    except Exception as e:
        logger.error(f"获取审批历史失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取审批历史失败"
        )


