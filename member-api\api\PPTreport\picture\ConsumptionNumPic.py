# -*- coding: utf-8 -*-
"""
会员消费数量分析图片生成器
生成会员消费人数、消费笔数、充值笔数和消费频次的分析图表
"""

import datetime
import logging
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from typing import Dict, Any, List, Tuple
from pathlib import Path

# 导入智能标签管理器
from .SmartLabels import add_smart_bar_labels, add_smart_line_labels

# 导入数据收集装饰器
from ..excel import collect_chart_data

from .PictureAi import PictureAiAnalyzer

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class ConsumptionNumPicGenerator:
    """会员消费数量分析图片生成器"""

    def __init__(self, bid: str, image_manager):
        """
        初始化图片生成器

        Args:
            bid: 品牌ID
            image_manager: 图片管理器实例
        """
        self.bid = bid
        self.image_manager = image_manager
        self.ai_analyzer = PictureAiAnalyzer()

    def _extract_param(self, query_params, key, default=None):
        """
        从查询参数中提取值

        Args:
            query_params: 查询参数（对象或字典）
            key: 参数键
            default: 默认值

        Returns:
            参数值
        """
        if hasattr(query_params, key):
            return getattr(query_params, key)
        elif isinstance(query_params, dict):
            return query_params.get(key, default)
        else:
            return default

    async def generate_consumption_num_charts(self, query_params) -> Dict[str, str]:
        """
        生成会员消费数量分析图表

        Args:
            query_params: 查询参数（对象或字典）

        Returns:
            Dict: 包含两张图片路径的字典
        """
        try:
            logger.info(f"开始生成会员消费数量分析图表 - bid: {self.bid}")
            logger.info(f"查询参数类型: {type(query_params)}")
            logger.info(f"图片管理器会话目录: {self.image_manager.session_dir}")

            # 计算时间范围
            current_date = datetime.datetime.now()
            end_date = self._extract_param(query_params, 'end_date', '2025-06-30')
            query_end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d")

            logger.info(f"时间参数 - 当前日期: {current_date}, 查询结束日期: {query_end_date}")

            # 计算去年和今年的时间范围
            last_year = query_end_date.year - 1
            this_year = query_end_date.year

            # 去年：完整的12个月
            last_year_ranges = self._generate_monthly_ranges(last_year, 1, 12)

            # 今年：从1月到查询结束日期所在月份，当前月份截至昨天
            if current_date.year == this_year:
                # 如果当前年份等于查询年份，截至昨天
                end_month = (current_date - datetime.timedelta(days=1)).month
            else:
                # 如果查询年份不是当前年份，使用查询结束日期的月份
                end_month = query_end_date.month

            this_year_ranges = self._generate_monthly_ranges(this_year, 1, end_month)

            # 获取数据
            logger.info(f"开始获取数据 - 去年范围: {len(last_year_ranges)}个月, 今年范围: {len(this_year_ranges)}个月")
            last_year_data = await self._fetch_monthly_data(last_year_ranges, query_params)
            this_year_data = await self._fetch_monthly_data(this_year_ranges, query_params)

            logger.info(f"数据获取结果 - 去年: {len(last_year_data)}条, 今年: {len(this_year_data)}条")

            # 初始化结果字典
            result = {}

            # 如果数据获取失败，生成错误图片
            if not last_year_data:
                logger.warning("去年数据获取失败，生成错误图片")
                error_path = self._generate_error_image("consumption_num_last_year", "去年数据获取失败")
                if error_path:
                    result["consumption_num_last_year"] = error_path

            if not this_year_data:
                logger.warning("今年数据获取失败，生成错误图片")
                error_path = self._generate_error_image("consumption_num_this_year", "今年数据获取失败")
                if error_path:
                    result["consumption_num_this_year"] = error_path

            logger.info(f"最终数据 - 去年: {len(last_year_data)}条, 今年: {len(this_year_data)}条")

            # 生成图片（即使数据为0也要生成，显示无数据状态）
            if last_year_data:
                last_year_path = await self._generate_chart(
                    last_year_data,
                    f"{last_year}年会员消费数量分析",
                    "consumption_num_last_year"
                )
                if last_year_path:
                    result["consumption_num_last_year"] = last_year_path
                    logger.info(f"去年图片生成成功: {last_year_path}")
                else:
                    logger.warning("去年图片生成失败")

            if this_year_data:
                this_year_path = await self._generate_chart(
                    this_year_data,
                    f"{this_year}年会员消费数量分析",
                    "consumption_num_this_year"
                )
                if this_year_path:
                    result["consumption_num_this_year"] = this_year_path
                    logger.info(f"今年图片生成成功: {this_year_path}")
                else:
                    logger.warning("今年图片生成失败")

            # 生成AI分析报告
            try:
                logger.info("开始生成会员消费数量AI分析...")
                ai_analysis = await self.ai_analyzer.generate_all_consumption_num_analysis(
                    this_year_data, last_year_data
                )
                result.update(ai_analysis)
                logger.info("会员消费数量AI分析生成完成")
            except Exception as e:
                logger.error(f"生成会员消费数量AI分析失败: {e}")
                # 即使AI分析失败，也不影响图片生成结果

            logger.info(f"会员消费数量分析图表生成完成，共生成 {len(result)} 个结果")
            return result

        except Exception as e:
            logger.error(f"生成会员消费数量分析图表失败: {e}")
            return {}

    def _generate_error_image(self, image_type: str, error_message: str) -> str:
        """
        生成错误提示图片

        Args:
            image_type: 图片类型
            error_message: 错误信息

        Returns:
            str: 图片保存路径
        """
        try:
            # 创建错误图片
            fig, ax = plt.subplots(figsize=(12, 8))
            ax.text(0.5, 0.5, f'数据生成失败\n{error_message}',
                   horizontalalignment='center', verticalalignment='center',
                   fontsize=20, color='red', weight='bold',
                   transform=ax.transAxes)
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')

            # 保存图片
            file_path = self.image_manager.get_image_path(image_type)
            plt.savefig(file_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close(fig)

            logger.info(f"错误图片生成完成: {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"生成错误图片失败: {e}")
            return ""

    def _generate_monthly_ranges(self, year: int, start_month: int, end_month: int) -> List[Tuple[str, str, str]]:
        """
        生成月度时间范围

        Args:
            year: 年份
            start_month: 开始月份
            end_month: 结束月份

        Returns:
            List: 月度时间范围列表，每个元素为(月份标签, 开始日期, 结束日期)
        """
        ranges = []

        for month in range(start_month, end_month + 1):
            # 计算月份的第一天和最后一天
            first_day = datetime.date(year, month, 1)

            # 计算下个月的第一天，然后减去一天得到当月最后一天
            if month == 12:
                next_month_first = datetime.date(year + 1, 1, 1)
            else:
                next_month_first = datetime.date(year, month + 1, 1)

            last_day = next_month_first - datetime.timedelta(days=1)

            # 如果是当前月份且是今年，需要截止到昨天
            if year == datetime.datetime.now().year and month == datetime.datetime.now().month:
                yesterday = datetime.datetime.now().date() - datetime.timedelta(days=1)
                if yesterday < last_day:
                    last_day = yesterday

            month_label = f"{year}年{month}月"
            start_date = first_day.strftime("%Y-%m-%d")
            end_date = last_day.strftime("%Y-%m-%d")

            ranges.append((month_label, start_date, end_date))

        return ranges

    async def _fetch_monthly_data(self, time_ranges: List[Tuple[str, str, str]], query_params) -> List[Dict[str, Any]]:
        """
        获取月度数据

        Args:
            time_ranges: 时间范围列表
            query_params: 查询参数

        Returns:
            List: 月度数据列表
        """
        try:
            from api.query.MemberConsumeSql import MemberConsumeSqlQueries
            from api.query.MemberChargeSql import MemberChargeSqlQueries
            from core.database import db

            monthly_data = []
            bid = self._extract_param(query_params, 'bid')
            sid = self._extract_param(query_params, 'sid', None)

            for month_label, start_date, end_date in time_ranges:
                logger.info(f"获取 {month_label} 数据: {start_date} 到 {end_date}")

                # 转换日期格式为YYYYMMDD
                start_date_db = start_date.replace('-', '')
                end_date_db = end_date.replace('-', '')

                try:
                    # 获取会员消费人数
                    consume_uv_sql = MemberConsumeSqlQueries.build_dwoutput_consume_uv_sql(
                        start_date_db, end_date_db, bid, sid
                    )
                    consume_uv_result = await db.execute_dwoutput_one(consume_uv_sql)
                    consume_users_raw = consume_uv_result.get('total_consume_uv', 0) if consume_uv_result else 0
                    consume_users = int(consume_users_raw or 0)  # 转换为int类型，参考MemberConsumptionPic.py

                    # 获取会员消费笔数
                    consume_base_sql = MemberConsumeSqlQueries.build_dwoutput_consume_base_sql(
                        start_date_db, end_date_db, bid, sid
                    )
                    consume_base_result = await db.execute_dwoutput_one(consume_base_sql)
                    total_consume_count_raw = consume_base_result.get('total_consume_pv', 0) if consume_base_result else 0
                    total_consume_count = int(total_consume_count_raw or 0)  # 转换为int类型

                    # 获取会员充值笔数
                    charge_base_sql = MemberChargeSqlQueries.build_dwoutput_charge_base_sql(
                        start_date_db, end_date_db, bid, sid
                    )
                    charge_base_result = await db.execute_dwoutput_one(charge_base_sql)
                    charge_count_raw = charge_base_result.get('total_charge_pv', 0) if charge_base_result else 0
                    charge_count = int(charge_count_raw or 0)  # 转换为int类型

                    # 计算会员消费频次（处理除零情况，参考MemberConsumptionPic.py的计算方式）
                    consume_frequency = 0.0
                    if consume_users > 0:
                        consume_frequency = round(float(total_consume_count) / float(consume_users), 2)

                    logger.info(f"{month_label} 数据: 消费人数{consume_users}, 消费笔数{total_consume_count}, 充值笔数{charge_count}, 消费频次{consume_frequency}")

                    monthly_data.append({
                        'month': month_label,
                        'consume_users': consume_users,
                        'total_consume_count': total_consume_count,
                        'charge_count': charge_count,
                        'consume_frequency': consume_frequency
                    })

                except Exception as month_error:
                    logger.error(f"获取 {month_label} 数据失败: {month_error}")
                    # 添加失败的月份数据，避免图表中断
                    monthly_data.append({
                        'month': month_label,
                        'consume_users': 0,
                        'total_consume_count': 0,
                        'charge_count': 0,
                        'consume_frequency': 0.0
                    })

            logger.info(f"数据获取完成，共获取 {len(monthly_data)} 个月的数据")
            return monthly_data

        except Exception as e:
            logger.error(f"获取月度数据失败: {e}")
            return []

    @collect_chart_data("consumption_num", include_comparison=True)
    async def _generate_chart(self, data: List[Dict[str, Any]], title: str, image_type: str) -> str:
        """
        生成图表

        Args:
            data: 数据列表
            title: 图表标题
            image_type: 图片类型

        Returns:
            str: 图片保存路径
        """
        try:
            if not data:
                logger.warning(f"数据为空，无法生成图表: {image_type}")
                return ""

            # 创建图表
            fig, ax1 = plt.subplots(figsize=(14, 8))

            # 提取数据
            months = [item['month'] for item in data]
            consume_users = [item['consume_users'] for item in data]
            total_consume_count = [item['total_consume_count'] for item in data]
            charge_count = [item['charge_count'] for item in data]
            consume_frequency = [item['consume_frequency'] for item in data]

            # 设置X轴位置
            x_pos = range(len(months))

            # 绘制柱状图（与NewMembersPic.py保持一致的样式）
            bar_width = 0.25
            bars1 = ax1.bar([x - bar_width for x in x_pos], consume_users,
                           bar_width, label='会员消费人数', color='#4472C4', alpha=0.8)
            bars2 = ax1.bar(x_pos, total_consume_count,
                           bar_width, label='会员总消费笔数', color='#E74C3C', alpha=0.8)
            bars3 = ax1.bar([x + bar_width for x in x_pos], charge_count,
                           bar_width, label='期间会员充值笔数', color='#FF8C00', alpha=0.8)

            # 设置左侧Y轴
            ax1.set_xlabel('月份', fontsize=12)
            ax1.set_ylabel('数量', fontsize=12)

            ax1.set_xticks(x_pos)
            # 去掉年份前缀，只保留月份，水平显示（与NewMembersPic.py一致）
            month_labels = [month.split('-')[-1] + '月' if '-' in month else month for month in months]
            ax1.set_xticklabels(month_labels, rotation=0, ha='center')
            ax1.grid(True, alpha=0.3)

            # 创建右侧Y轴用于消费频次
            ax2 = ax1.twinx()
            ax2.plot(x_pos, consume_frequency, color='#F39C12', marker='o',
                    linewidth=3, markersize=8, label='会员消费频次')
            ax2.set_ylabel('消费频次 (次/人)', fontsize=12)
            # 设置右Y轴范围，处理全零数据的情况
            max_freq_value = max([float(x) for x in consume_frequency]) if consume_frequency and max([float(x) for x in consume_frequency]) > 0 else 1
            ax2.set_ylim(0, max_freq_value * 1.2)

            # 在柱状图上添加数值标签（智能避免重叠）
            # 确保所有数值都是数字类型，避免Decimal类型错误
            max_consume_users = max([float(x) for x in consume_users]) if consume_users and max(consume_users) > 0 else 10
            max_total_consume_count = max([float(x) for x in total_consume_count]) if total_consume_count and max(total_consume_count) > 0 else 10
            max_charge_count = max([float(x) for x in charge_count]) if charge_count and max(charge_count) > 0 else 10
            max_value = max(max_consume_users, max_total_consume_count, max_charge_count)

            # 收集所有柱状图的位置和数值
            bar_data = []
            for bar in bars1:
                height = bar.get_height()
                if height > 0:
                    bar_data.append((bar.get_x() + bar.get_width()/2., height))

            for bar in bars2:
                height = bar.get_height()
                if height > 0:
                    bar_data.append((bar.get_x() + bar.get_width()/2., height))

            for bar in bars3:
                height = bar.get_height()
                if height > 0:
                    bar_data.append((bar.get_x() + bar.get_width()/2., height))

            # 使用智能标签添加方法
            if bar_data:
                x_positions = [x for x, y in bar_data]
                values = [y for x, y in bar_data]
                add_smart_bar_labels(ax1, values, max_value, x_positions, fontsize=10, decimal_places=0)

            # 在折线图上添加数值标签（智能避免重叠）
            max_freq = max([float(x) for x in consume_frequency]) if consume_frequency and max(consume_frequency) > 0 else 1
            add_smart_line_labels(ax2, consume_frequency, max_freq, fontsize=10, color='#F39C12', decimal_places=2, reset_positions=False)

            # 合并图例 - 放在图表外部顶部
            lines1, labels1 = ax1.get_legend_handles_labels()
            lines2, labels2 = ax2.get_legend_handles_labels()
            ax1.legend(lines1 + lines2, labels1 + labels2,
                      bbox_to_anchor=(0.5, 1.02), loc='lower center',
                      ncol=len(labels1 + labels2), fontsize=11)

            # 添加数据表格
            self._add_data_table(data, ax1)

            # 调整布局
            plt.tight_layout()

            # 保存图片
            save_path = self.image_manager.get_image_path(image_type)
            logger.info(f"准备保存图片到: {save_path}")

            # 确保保存目录存在
            save_dir = Path(save_path).parent
            save_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"保存目录: {save_dir}, 存在: {save_dir.exists()}")

            # 保存图片
            try:
                plt.savefig(save_path, dpi=300, bbox_inches='tight',
                           facecolor='white', edgecolor='none')
                logger.info(f"matplotlib保存完成")

                # 验证文件是否真的被创建
                if Path(save_path).exists():
                    file_size = Path(save_path).stat().st_size
                    logger.info(f"图表生成成功: {save_path}, 文件大小: {file_size} 字节")
                else:
                    logger.error(f"图片保存失败: 文件未创建 {save_path}")
                    return ""

            except Exception as save_error:
                logger.error(f"matplotlib保存失败: {save_error}")
                return ""
            finally:
                plt.close()

            return save_path

        except Exception as e:
            logger.error(f"生成图表失败 {image_type}: {e}")
            return ""

    def _add_data_table(self, data: List[Dict[str, Any]], ax):
        """
        在图表下方添加数据表格（横向布局，时间为横轴）

        Args:
            data: 数据列表
            ax: 坐标轴对象
        """
        try:
            # 准备横向表格数据
            months = [item['month'] for item in data]
            consume_users = [f"{item['consume_users']:,}" for item in data]
            total_consume_count = [f"{item['total_consume_count']:,}" for item in data]
            charge_count = [f"{item['charge_count']:,}" for item in data]
            consume_frequency = [f"{item['consume_frequency']:.2f}" for item in data]

            # 构建横向表格数据：第一行是月份，后面四行是数据
            table_data = [
                months,  # 第一行：月份
                consume_users,  # 第二行：会员消费人数
                total_consume_count,  # 第三行：会员总消费笔数
                charge_count,  # 第四行：期间会员充值笔数
                consume_frequency  # 第五行：会员消费频次
            ]

            # 行标题（左侧标签）
            row_labels = ['月份', '会员消费人数(人)', '会员总消费笔数(笔)', '期间会员充值笔数(笔)', '会员消费频次(次/人)']

            # 创建表格
            table = ax.table(
                cellText=table_data,
                rowLabels=row_labels,
                cellLoc='center',
                loc='bottom',
                bbox=[0, -0.75, 1, 0.45]  # [x, y, width, height] - 向下移动避免遮挡图表
            )

            # 设置表格样式
            table.auto_set_font_size(False)
            table.set_fontsize(9)  # 增大字体大小提高可读性
            table.scale(1, 1.3)

            # 设置行标题样式（左侧列）
            for i in range(len(row_labels)):
                table[(i, -1)].set_facecolor('#4472C4')
                table[(i, -1)].set_text_props(weight='bold', color='white')

            # 设置数据单元格样式
            for i in range(len(row_labels)):
                for j in range(len(months)):
                    if i == 0:  # 月份行使用浅蓝色
                        table[(i, j)].set_facecolor('#E7F3FF')
                        table[(i, j)].set_text_props(weight='bold')
                    elif i % 2 == 1:  # 奇数行使用浅灰色
                        table[(i, j)].set_facecolor('#F8F9FA')
                    else:  # 偶数行使用白色
                        table[(i, j)].set_facecolor('white')

            logger.info(f"横向数据表格创建完成，包含 {len(months)} 个月的数据")

        except Exception as e:
            logger.error(f"添加数据表格失败: {e}")


def create_consumption_num_pic_generator(bid: str, image_manager):
    """
    创建会员消费数量分析图片生成器

    Args:
        bid: 品牌ID
        image_manager: 图片管理器实例

    Returns:
        ConsumptionNumPicGenerator: 图片生成器实例
    """
    return ConsumptionNumPicGenerator(bid, image_manager)