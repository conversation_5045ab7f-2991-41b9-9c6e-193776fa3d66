# -*- coding: utf-8 -*-
"""
智能标签管理模块
提供图表数字标签的智能定位功能，避免重叠并确保在框内显示
"""

import logging
from typing import List, Optional, Tuple, Union

logger = logging.getLogger(__name__)


class SmartLabelManager:
    """智能标签管理器"""
    
    def __init__(self):
        self.label_positions = []  # 存储已添加的标签位置
    
    def reset(self):
        """重置标签位置记录"""
        self.label_positions = []
    
    def add_bar_labels(self, ax, values: List[Union[int, float]], max_value: float, 
                      x_positions: Optional[List[float]] = None, fontsize: int = 9, 
                      min_spacing: float = 0.05, is_percentage: bool = False,
                      decimal_places: int = 0):
        """
        为柱状图添加智能标签，避免重叠并确保在框内显示

        Args:
            ax: matplotlib坐标轴对象
            values: 数值列表
            max_value: 最大值（用于计算基础偏移）
            x_positions: X轴位置列表，默认为range(len(values))
            fontsize: 字体大小
            min_spacing: 最小间距比例
            is_percentage: 是否为百分比数据
            decimal_places: 小数位数（0表示整数）
        """
        try:
            if not values:
                return

            if x_positions is None:
                x_positions = list(range(len(values)))

            base_offset = max_value * 0.02  # 基础偏移量

            for i, (x_pos, value) in enumerate(zip(x_positions, values)):
                if value <= (0.01 if decimal_places > 0 else 0):
                    continue

                # 初始位置：柱子顶部 + 基础偏移
                initial_y = value + base_offset
                
                # 检查与已有标签的冲突并调整位置
                final_y = self._resolve_conflicts(x_pos, initial_y, max_value, min_spacing)
                
                # 确保标签不超出图表顶部
                final_y = self._ensure_within_bounds(ax, final_y, value, max_value)

                # 格式化标签文本
                label_text = self._format_label(value, is_percentage, decimal_places)

                # 添加标签
                ax.text(x_pos, final_y, label_text,
                       ha='center', va='bottom', fontsize=fontsize, 
                       bbox=dict(boxstyle='round,pad=0.2', facecolor='white', 
                               alpha=0.8, edgecolor='none'))

                self.label_positions.append((x_pos, final_y))

        except Exception as e:
            logger.error(f"添加柱状图智能标签失败: {e}")
            # 降级到简单标签
            self._add_simple_bar_labels(ax, values, max_value, x_positions, 
                                      fontsize, is_percentage, decimal_places)

    def add_line_labels(self, ax, values: List[Union[int, float]], max_value: float,
                       x_positions: Optional[List[float]] = None, fontsize: int = 10, 
                       color: str = '#000000', min_spacing: float = 0.05, 
                       is_percentage: bool = False, decimal_places: int = 2):
        """
        为折线图添加智能标签，避免重叠并确保在框内显示

        Args:
            ax: matplotlib坐标轴对象
            values: 数值列表
            max_value: 最大值（用于计算基础偏移）
            x_positions: X轴位置列表，默认为range(len(values))
            fontsize: 字体大小
            color: 字体颜色
            min_spacing: 最小间距比例
            is_percentage: 是否为百分比数据
            decimal_places: 小数位数
        """
        try:
            if not values:
                return

            if x_positions is None:
                x_positions = list(range(len(values)))

            base_offset = max_value * 0.05  # 基础偏移量

            for i, (x_pos, value) in enumerate(zip(x_positions, values)):
                if value <= 0.01:  # 过滤掉很小的值
                    continue

                # 初始位置：点位置 + 基础偏移
                initial_y = value + base_offset
                
                # 检查与已有标签的冲突并调整位置
                final_y = self._resolve_conflicts(x_pos, initial_y, max_value, min_spacing)
                
                # 确保标签不超出图表顶部
                y_limit = ax.get_ylim()[1]
                if final_y > y_limit * 0.95:
                    final_y = y_limit * 0.92

                # 格式化标签文本
                label_text = self._format_label(value, is_percentage, decimal_places)

                # 添加标签
                ax.text(x_pos, final_y, label_text,
                       ha='center', va='bottom', fontsize=fontsize, color=color,
                       fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.2', facecolor='white', 
                               alpha=0.8, edgecolor='none'))

                self.label_positions.append((x_pos, final_y))

        except Exception as e:
            logger.error(f"添加折线图智能标签失败: {e}")
            # 降级到简单标签
            self._add_simple_line_labels(ax, values, max_value, x_positions, 
                                       fontsize, color, is_percentage, decimal_places)

    def _resolve_conflicts(self, x_pos: float, initial_y: float, max_value: float, 
                          min_spacing: float) -> float:
        """
        解决标签位置冲突

        Args:
            x_pos: X轴位置
            initial_y: 初始Y轴位置
            max_value: 最大值
            min_spacing: 最小间距比例

        Returns:
            调整后的Y轴位置
        """
        final_y = initial_y
        min_distance = max_value * min_spacing
        
        # 向上调整位置避免重叠
        for existing_x, existing_y in self.label_positions:
            # 如果X位置相近且Y位置冲突
            if abs(x_pos - existing_x) < 0.8 and abs(final_y - existing_y) < min_distance:
                final_y = max(final_y, existing_y + min_distance)
        
        return final_y

    def _ensure_within_bounds(self, ax, final_y: float, value: float, max_value: float) -> float:
        """
        确保标签在图表边界内

        Args:
            ax: matplotlib坐标轴对象
            final_y: 当前Y轴位置
            value: 数值
            max_value: 最大值

        Returns:
            调整后的Y轴位置
        """
        y_limit = ax.get_ylim()[1]
        if final_y > y_limit * 0.95:
            # 如果超出顶部，尝试放在柱子内部
            if value > max_value * 0.15:  # 柱子足够高
                final_y = value * 0.5  # 放在柱子中间
            else:
                final_y = y_limit * 0.92  # 放在接近顶部的位置
        
        return final_y

    def _format_label(self, value: Union[int, float], is_percentage: bool = False, 
                     decimal_places: int = 0) -> str:
        """
        格式化标签文本

        Args:
            value: 数值
            is_percentage: 是否为百分比
            decimal_places: 小数位数

        Returns:
            格式化后的文本
        """
        if decimal_places == 0:
            formatted_value = f'{value:,.0f}'
        else:
            formatted_value = f'{value:.{decimal_places}f}'
        
        if is_percentage:
            formatted_value += '%'
        
        return formatted_value

    def _add_simple_bar_labels(self, ax, values: List[Union[int, float]], max_value: float,
                              x_positions: Optional[List[float]], fontsize: int,
                              is_percentage: bool, decimal_places: int):
        """降级到简单标签（柱状图）"""
        if x_positions is None:
            x_positions = list(range(len(values)))
        
        for i, value in enumerate(values):
            if value > (0.01 if decimal_places > 0 else 0):
                x_pos = x_positions[i]
                label_text = self._format_label(value, is_percentage, decimal_places)
                ax.text(x_pos, value + max_value * 0.02, label_text,
                       ha='center', va='bottom', fontsize=fontsize)

    def _add_simple_line_labels(self, ax, values: List[Union[int, float]], max_value: float,
                               x_positions: Optional[List[float]], fontsize: int, color: str,
                               is_percentage: bool, decimal_places: int):
        """降级到简单标签（折线图）"""
        if x_positions is None:
            x_positions = list(range(len(values)))
        
        for i, value in enumerate(values):
            if value > 0.01:
                x_pos = x_positions[i]
                label_text = self._format_label(value, is_percentage, decimal_places)
                ax.text(x_pos, value + max_value * 0.05, label_text,
                       ha='center', va='bottom', fontsize=fontsize, color=color)


# 全局实例
smart_label_manager = SmartLabelManager()


# 便捷函数
def add_smart_bar_labels(ax, values: List[Union[int, float]], max_value: float, 
                        x_positions: Optional[List[float]] = None, fontsize: int = 9,
                        min_spacing: float = 0.05, is_percentage: bool = False,
                        decimal_places: int = 0, reset_positions: bool = True):
    """
    添加智能柱状图标签的便捷函数

    Args:
        ax: matplotlib坐标轴对象
        values: 数值列表
        max_value: 最大值
        x_positions: X轴位置列表
        fontsize: 字体大小
        min_spacing: 最小间距比例
        is_percentage: 是否为百分比数据
        decimal_places: 小数位数
        reset_positions: 是否重置位置记录
    """
    if reset_positions:
        smart_label_manager.reset()
    
    smart_label_manager.add_bar_labels(ax, values, max_value, x_positions, 
                                      fontsize, min_spacing, is_percentage, decimal_places)


def add_smart_line_labels(ax, values: List[Union[int, float]], max_value: float,
                         x_positions: Optional[List[float]] = None, fontsize: int = 10,
                         color: str = '#000000', min_spacing: float = 0.05,
                         is_percentage: bool = False, decimal_places: int = 2,
                         reset_positions: bool = False):
    """
    添加智能折线图标签的便捷函数

    Args:
        ax: matplotlib坐标轴对象
        values: 数值列表
        max_value: 最大值
        x_positions: X轴位置列表
        fontsize: 字体大小
        color: 字体颜色
        min_spacing: 最小间距比例
        is_percentage: 是否为百分比数据
        decimal_places: 小数位数
        reset_positions: 是否重置位置记录
    """
    if reset_positions:
        smart_label_manager.reset()
    
    smart_label_manager.add_line_labels(ax, values, max_value, x_positions, 
                                       fontsize, color, min_spacing, is_percentage, decimal_places)
