import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple, Union
from core.models import QueryTypeEnum, CompareOptionEnum
from services.llm_service import LLMService
from api.query.prompt import get_analysis_prompt

logger = logging.getLogger(__name__)

def get_time_range(query_type: QueryTypeEnum, start_date: Optional[str] = None, end_date: Optional[str] = None) -> Tuple[str, str]:
    """
    获取时间范围
    
    Args:
        query_type: 查询类型 (week, month, quarter, halfyear, custom)
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
    
    Returns:
        Tuple[str, str]: (开始日期, 结束日期) 格式: YYYY-MM-DD
    """
    # 如果明确传入了开始和结束日期，直接使用传入的日期
    if start_date and end_date:
        return start_date, end_date
    
    if query_type == QueryTypeEnum.CUSTOM:
        raise ValueError("自定义查询类型必须提供开始和结束日期")
    
    # 获取当前日期
    today = datetime.now().date()
    
    if query_type == QueryTypeEnum.WEEK:
        # 周查询：过去7天
        start = today - timedelta(days=7)
        end = today - timedelta(days=1)
    elif query_type == QueryTypeEnum.MONTH:
        # 月查询：过去30天
        start = today - timedelta(days=30)
        end = today - timedelta(days=1)
    elif query_type == QueryTypeEnum.QUARTER:
        # 季度查询：过去90天
        start = today - timedelta(days=90)
        end = today - timedelta(days=1)
    elif query_type == QueryTypeEnum.HALFYEAR:
        # 半年查询：过去180天
        start = today - timedelta(days=180)
        end = today - timedelta(days=1)
    else:
        raise ValueError(f"不支持的查询类型: {query_type}")
    
    return start.strftime('%Y-%m-%d'), end.strftime('%Y-%m-%d')

def calculate_comparison_periods(start_date: str, end_date: str, query_type: QueryTypeEnum, 
                               compare_options: List[CompareOptionEnum]) -> Dict[str, Tuple[str, str]]:
    """
    计算对比时间段
    
    Args:
        start_date: 当前期间开始日期
        end_date: 当前期间结束日期
        query_type: 查询类型
        compare_options: 对比选项列表
    
    Returns:
        Dict[str, Tuple[str, str]]: 对比时间段字典
    """
    result = {}
    
    start_dt = datetime.strptime(start_date, '%Y-%m-%d').date()
    end_dt = datetime.strptime(end_date, '%Y-%m-%d').date()
    period_days = (end_dt - start_dt).days + 1
    
    # 环比：上一个相同长度的时间段
    if CompareOptionEnum.CHAIN in compare_options:
        chain_end = start_dt - timedelta(days=1)
        chain_start = chain_end - timedelta(days=period_days - 1)
        result['chain'] = (chain_start.strftime('%Y-%m-%d'), chain_end.strftime('%Y-%m-%d'))
    
    # 同比：去年同期
    if CompareOptionEnum.YEAR_ON_YEAR in compare_options:
        yoy_start = start_dt - timedelta(days=365)
        yoy_end = end_dt - timedelta(days=365)
        result['year_on_year'] = (yoy_start.strftime('%Y-%m-%d'), yoy_end.strftime('%Y-%m-%d'))
    
    return result

class AIAnalysisService:
    """AI分析服务"""
    
    def __init__(self):
        self.llm_service = LLMService()
    
    async def analyze_member_base_data(self, data: Dict[str, Any], query_params: Dict[str, Any]) -> str:
        """分析会员基础数据"""
        try:
            # 记录分析开始
            logger.info(f"开始会员基础数据AI分析 - 查询参数: {query_params}")

            # 生成分析提示词
            prompt = get_analysis_prompt("member_base", data, query_params)

            # 调用LLM服务进行分析
            response = await self.llm_service.generate_response(prompt)

            # 格式化分析结果
            formatted_response = self._format_analysis_result(response)

            logger.info("会员基础数据AI分析完成")
            return formatted_response

        except Exception as e:
            logger.error(f"会员基础数据AI分析失败: {str(e)}", exc_info=True)
            return self._get_fallback_member_base_analysis()
    
    async def analyze_member_consume_data(self, data: Union[Dict[str, Any], List[Dict[str, Any]]], query_params: Dict[str, Any]) -> str:
        """分析会员消费数据"""
        try:
            logger.info(f"开始会员消费数据AI分析 - 查询参数: {query_params}")

            prompt = get_analysis_prompt("member_consume", data, query_params)
            response = await self.llm_service.generate_response(prompt)
            formatted_response = self._format_analysis_result(response)

            logger.info("会员消费数据AI分析完成")
            return formatted_response

        except Exception as e:
            logger.error(f"会员消费数据AI分析失败: {str(e)}", exc_info=True)
            return self._get_fallback_member_consume_analysis()

    async def analyze_member_charge_data(self, data: Union[Dict[str, Any], List[Dict[str, Any]]], query_params: Dict[str, Any]) -> str:
        """分析会员充值数据"""
        try:
            logger.info(f"开始会员充值数据AI分析 - 查询参数: {query_params}")

            prompt = get_analysis_prompt("member_charge", data, query_params)
            response = await self.llm_service.generate_response(prompt)
            formatted_response = self._format_analysis_result(response)

            logger.info("会员充值数据AI分析完成")
            return formatted_response

        except Exception as e:
            logger.error(f"会员充值数据AI分析失败: {str(e)}", exc_info=True)
            return self._get_fallback_member_charge_analysis()

    async def analyze_coupon_trade_data(self, data: Union[Dict[str, Any], List[Dict[str, Any]]], query_params: Dict[str, Any]) -> str:
        """分析券交易数据"""
        try:
            logger.info(f"开始券交易数据AI分析 - 查询参数: {query_params}")

            prompt = get_analysis_prompt("coupon_trade", data, query_params)
            response = await self.llm_service.generate_response(prompt)
            formatted_response = self._format_analysis_result(response)

            logger.info("券交易数据AI分析完成")
            return formatted_response

        except Exception as e:
            logger.error(f"券交易数据AI分析失败: {str(e)}", exc_info=True)
            return self._get_fallback_coupon_trade_analysis()

    async def analyze_pinzhi_cashier_data(self, data: Dict[str, Any], query_params: Dict[str, Any]) -> str:
        """分析品智收银数据"""
        try:
            logger.info(f"开始品智收银数据AI分析 - 查询参数: {query_params}")

            # 使用统一的提示词生成方法
            prompt = get_analysis_prompt("pinzhi_cashier", data, query_params)

            # 调用LLM服务进行分析
            response = await self.llm_service.generate_response(prompt)

            # 格式化分析结果
            formatted_response = self._format_analysis_result(response)

            logger.info("品智收银数据AI分析完成")
            return formatted_response
        except Exception as e:
            logger.error(f"品智收银数据AI分析失败: {str(e)}", exc_info=True)
            return self._get_fallback_pinzhi_cashier_analysis()

    def _format_analysis_result(self, analysis_result: str) -> str:
        """格式化分析结果，确保输出5条分析"""
        if not analysis_result or not analysis_result.strip():
            return "AI分析结果为空，请稍后再试。"

        # 按行分割并过滤空行
        lines = [line.strip() for line in analysis_result.split('\n') if line.strip()]
        formatted_lines = []

        # 查找以数字开头的分析条目
        for line in lines:
            line = line.strip()
            # 匹配1、2、3、4、5开头的分析条目
            if line and any(line.startswith(f'{i}、') for i in range(1, 6)):
                formatted_lines.append(line)

        # 如果没有找到正确格式的行，尝试从原文本中提取
        if len(formatted_lines) < 5:
            logger.warning("AI分析结果格式不标准，尝试重新格式化")
            # 尝试按句号分割并重新格式化
            sentences = [s.strip() for s in analysis_result.split('。') if s.strip() and len(s.strip()) > 20]
            formatted_lines = []

            for i, sentence in enumerate(sentences[:5], 1):
                if sentence and not sentence.startswith(str(i) + '、'):
                    formatted_lines.append(f"{i}、{sentence}。")
                elif sentence:
                    formatted_lines.append(sentence if sentence.endswith('。') else sentence + '。')

        # 确保有5条分析，如果不足则使用默认内容
        while len(formatted_lines) < 5:
            index = len(formatted_lines) + 1
            formatted_lines.append(f"{index}、数据分析中，建议关注关键指标变化趋势和业务优化机会。")

        # 记录分析结果的质量
        logger.info(f"AI分析结果格式化完成，共{len(formatted_lines)}条分析")

        return '\n'.join(formatted_lines[:5])

    def _get_fallback_member_base_analysis(self) -> str:
        """获取会员基础数据分析的备用结果"""
        return """1、会员总量增长缓慢且新增会员质量不高，建议优化获客渠道并建立新手引导体系提升转化率。
2、会员信息完善率偏低影响精准营销效果，建议设置完善奖励机制并简化信息收集流程。
3、会员活跃度分化严重存在大量沉睡用户，建议建立分层运营体系和用户唤醒机制。
4、会员留存率不足且生命周期价值偏低，建议加强会员权益设计和忠诚度计划建设。
5、会员结构不均衡高价值用户占比过少，建议建立VIP服务体系和价值用户培养机制。"""

    def _get_fallback_member_consume_analysis(self) -> str:
        """获取会员消费数据分析的备用结果"""
        return """1、消费总额增长乏力且人均消费偏低，建议推出套餐产品和消费激励活动提升客单价。
2、储值消费占比过高存在资金风险，建议推动现金消费并优化储值使用策略和权益设计。
3、消费频次不足且重复消费率偏低，建议建立复购奖励机制和定期消费提醒系统。
4、消费会员占比偏低转化效率不高，建议优化产品结构和服务体验提升消费转化率。
5、消费时段分布不均且季节性波动明显，建议制定差异化营销策略和淡季促销方案。"""

    def _get_fallback_member_charge_analysis(self) -> str:
        """获取会员充值数据分析的备用结果"""
        return """1、充值总额不达预期且充值会员占比偏低，建议优化充值档位设计和充值奖励机制。
2、平均充值金额偏小且大额充值用户稀少，建议推出高价值充值套餐和VIP充值特权。
3、储值使用率不高且余额沉淀严重，建议加强储值消费引导和限时使用激励政策。
4、充值转化率偏低且充值频次不足，建议优化充值流程和建立充值习惯培养机制。
5、充值活动效果一般且用户粘性不强，建议创新充值营销方式和建立充值会员专属权益。"""

    def _get_fallback_coupon_trade_analysis(self) -> str:
        """获取券交易数据分析的备用结果"""
        return """1、券使用率偏低且大量券过期浪费，建议优化券面值设计和使用门槛降低券使用难度。
2、券带动效果不明显投入产出比偏低，建议精准投放券类型和加强券使用场景引导。
3、券用户覆盖面窄且重复使用率不高，建议扩大券发放范围和建立券使用激励机制。
4、券成本控制不当且效果监控不足，建议建立券效果评估体系和动态调整券策略。
5、券活动创新不足且用户参与度偏低，建议开发多样化券玩法和提升券营销趣味性。"""

    def _get_fallback_pinzhi_cashier_analysis(self) -> str:
        """获取品智收银数据分析的备用结果"""
        return """1、营业额实收率偏低折扣力度过大，建议优化定价策略和控制促销频次提升盈利能力。
2、堂食外卖结构失衡单一渠道依赖风险高，建议均衡发展双渠道并优化各渠道运营策略。
3、订单转化效率不高客单价提升空间大，建议推出套餐产品和增值服务提升单笔消费金额。
4、业务增长波动较大缺乏稳定增长动力，建议建立会员体系和复购激励机制稳定客流。
5、运营数据监控不足决策缺乏数据支撑，建议完善数据分析体系和建立关键指标预警机制。"""

# 创建AI分析服务实例
ai_analysis_service = AIAnalysisService()
