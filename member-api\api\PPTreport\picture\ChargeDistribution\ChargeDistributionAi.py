# -*- coding: utf-8 -*-
"""
充值档位分布AI分析器
提供充值档位分布数据的智能分析功能
"""

import logging
from typing import Dict, Any, List

from services.llm_service import LLMService

logger = logging.getLogger(__name__)

class ChargeDistributionAiAnalyzer:
    """充值档位分布AI分析器"""

    def __init__(self):
        """初始化充值档位分布AI分析器"""
        self.llm_service = LLMService()
        logger.info("充值档位分布AI分析器初始化完成")

    async def analyze_charge_distribution_data(self, charge_data: List[Dict[str, Any]], start_date: str = None, end_date: str = None) -> str:
        """
        分析充值档位分布数据

        Args:
            charge_data: 充值档位分布数据列表
            start_date: 查询开始日期 (YYYY-MM-DD格式)
            end_date: 查询结束日期 (YYYY-MM-DD格式)

        Returns:
            str: AI分析结果（200-250字的整段分析）
        """
        try:
            if not charge_data:
                return "充值档位分布数据不足，无法进行有效分析。建议完善数据收集机制，确保充值档位数据的完整性和准确性，为精准营销和会员价值提升提供可靠的数据支撑。"

            # 构建时间范围描述
            time_range_desc = ""
            if start_date and end_date:
                time_range_desc = f"分析时间范围：{start_date} 至 {end_date}"
            elif start_date:
                time_range_desc = f"分析时间范围：{start_date} 开始"
            elif end_date:
                time_range_desc = f"分析时间范围：截至 {end_date}"
            else:
                time_range_desc = f"分析时间范围：未指定时间范围"

            # 构建完整的AI分析请求
            full_prompt = f"""
作为专业的会员运营数据分析师，请基于以下充值档位分布数据进行深入分析：

{time_range_desc}

数据概况：
{self._format_charge_distribution_data_summary(charge_data, start_date, end_date)}

分析要求：
1. 使用"1、2、3、"作为项目符号，按点列出3个关键洞察
2. 每个洞察控制在28字以内（标点符号不计入字数），突出关键发现和实用建议
3. 重点关注：峰值表现、结构特征、优化建议
4. 必须标注具体数值和关键指标
5. 每个洞察格式：关键发现 + 核心数据 + 简要建议
6. 每个洞察点必须包含适当的标点符号（逗号、句号等）
7. 禁止空行和格式化符号

请基于实际数据直接生成专业洞察（每点28字以内，含标点符号）：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)

            if analysis_result and analysis_result.strip():
                result = analysis_result.strip()
                logger.info(f"充值档位分布数据AI分析完成")
                return result
            else:
                logger.warning("AI分析返回空结果")
                return "充值档位分布数据分析：AI服务暂时不可用，请稍后重试。"

        except Exception as e:
            logger.error(f"充值档位分布数据AI分析失败: {str(e)}")
            return f"充值档位分布数据分析失败：{str(e)}。建议检查数据格式和AI服务状态。"

    def _format_charge_distribution_data_summary(self, charge_data: List[Dict[str, Any]], start_date: str = None, end_date: str = None) -> str:
        """
        格式化充值档位分布数据摘要

        Args:
            charge_data: 充值档位分布数据列表
            start_date: 查询开始日期
            end_date: 查询结束日期

        Returns:
            str: 格式化的数据摘要
        """
        if not charge_data:
            return "无数据"

        try:
            # 提取数据
            charge_amounts = [item.get('charge_cash', 0) for item in charge_data]
            charge_counts = [item.get('charge_count', 0) for item in charge_data]

            # 计算统计指标
            total_tiers = len(charge_data)
            total_transactions = sum(charge_counts)
            avg_amount = sum(charge_amounts) / total_tiers if total_tiers > 0 else 0
            avg_count = sum(charge_counts) / total_tiers if total_tiers > 0 else 0

            # 找出极值
            max_amount = max(charge_amounts) if charge_amounts else 0
            min_amount = min(charge_amounts) if charge_amounts else 0
            max_count = max(charge_counts) if charge_counts else 0
            min_count = min(charge_counts) if charge_counts else 0

            # 找出热门档位（充值笔数最多的档位）
            max_count_idx = charge_counts.index(max_count) if charge_counts else 0
            popular_tier = charge_amounts[max_count_idx] if charge_amounts else 0

            # 计算集中度（前3档位占比）
            top3_count = sum(sorted(charge_counts, reverse=True)[:3])
            concentration_ratio = (top3_count / total_transactions * 100) if total_transactions > 0 else 0

            # 构建时间范围信息
            time_info = ""
            if start_date and end_date:
                time_info = f"数据时间范围：{start_date} 至 {end_date}\n"
            elif start_date:
                time_info = f"数据时间范围：{start_date} 开始\n"
            elif end_date:
                time_info = f"数据时间范围：截至 {end_date}\n"

            summary = f"""
{time_info}档位数量：{total_tiers}个充值档位
总充值笔数：{total_transactions}笔
档位范围：{min_amount:.0f}元 - {max_amount:.0f}元
平均档位金额：{avg_amount:.2f}元，平均充值笔数：{avg_count:.2f}笔
热门档位：{popular_tier:.0f}元（{max_count}笔，占比{max_count/total_transactions*100:.1f}%）
档位集中度：前3档位占总笔数{concentration_ratio:.1f}%
详细分布：{[f"{charge_amounts[i]:.0f}元({charge_counts[i]}笔)" for i in range(len(charge_amounts))]}
"""
            return summary.strip()

        except Exception as e:
            logger.error(f"格式化充值档位分布数据摘要失败: {str(e)}")
            return f"数据格式化失败: {str(e)}"


    async def analyze_charge_frequency_data(self, frequency_data: List[Dict[str, Any]], start_date: str = None, end_date: str = None) -> str:
        """
        分析充值频次分布数据

        Args:
            frequency_data: 充值频次分布数据列表
            start_date: 查询开始日期 (YYYY-MM-DD格式)
            end_date: 查询结束日期 (YYYY-MM-DD格式)

        Returns:
            str: AI分析结果（200-250字的整段分析）
        """
        try:
            if not frequency_data:
                return "充值频次分布数据不足，无法进行有效分析。建议完善数据收集机制，确保充值频次数据的完整性和准确性，为精准营销和会员价值提升提供可靠的数据支撑。"

            # 构建时间范围描述
            time_range_desc = ""
            if start_date and end_date:
                time_range_desc = f"分析时间范围：{start_date} 至 {end_date}"
            elif start_date:
                time_range_desc = f"分析时间范围：{start_date} 开始"
            elif end_date:
                time_range_desc = f"分析时间范围：截至 {end_date}"
            else:
                time_range_desc = f"分析时间范围：未指定时间范围"

            # 构建完整的AI分析请求
            full_prompt = f"""
作为专业的会员运营数据分析师，请基于以下充值频次分布数据进行深入分析：

{time_range_desc}

数据概况：
{self._format_charge_frequency_data_summary(frequency_data, start_date, end_date)}

分析要求：
1. 使用"1、2、3、"作为项目符号，按点列出3个关键洞察
2. 每个洞察控制在28字以内（标点符号不计入字数），突出关键发现和实用建议
3. 重点关注：峰值表现、结构特征、优化建议
4. 必须标注具体数值和关键指标
5. 每个洞察格式：关键发现 + 核心数据 + 简要建议
6. 每个洞察点必须包含适当的标点符号（逗号、句号等）
7. 禁止空行和格式化符号

请基于实际数据直接生成专业洞察（每点28字以内，含标点符号）：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)

            if analysis_result and analysis_result.strip():
                result = analysis_result.strip()
                logger.info(f"充值频次分布数据AI分析完成")
                return result
            else:
                logger.warning("AI分析返回空结果")
                return "充值频次分布数据分析：AI服务暂时不可用，请稍后重试。"

        except Exception as e:
            logger.error(f"充值频次分布数据AI分析失败: {str(e)}")
            return f"充值频次分布数据分析失败：{str(e)}。建议检查数据格式和AI服务状态。"

    def _format_charge_frequency_data_summary(self, frequency_data: List[Dict[str, Any]], start_date: str = None, end_date: str = None) -> str:
        """
        格式化充值频次分布数据摘要

        Args:
            frequency_data: 充值频次分布数据列表
            start_date: 查询开始日期
            end_date: 查询结束日期

        Returns:
            str: 格式化的数据摘要
        """
        if not frequency_data:
            return "无数据"

        try:
            # 提取数据
            frequency_ranges = [item.get('charge_count_range', '') for item in frequency_data]
            user_counts = [item.get('user_count', 0) for item in frequency_data]

            # 计算统计指标
            total_users = sum(user_counts)
            total_frequency_types = len(frequency_data)

            # 找出最活跃的频次区间
            max_count_idx = user_counts.index(max(user_counts)) if user_counts else 0
            most_active_frequency = frequency_ranges[max_count_idx] if frequency_ranges else ""
            most_active_count = user_counts[max_count_idx] if user_counts else 0

            # 计算单次充值用户占比
            single_charge_count = 0
            for i, freq in enumerate(frequency_ranges):
                if freq == "1次":
                    single_charge_count = user_counts[i]
                    break

            single_charge_ratio = (single_charge_count / total_users * 100) if total_users > 0 else 0

            # 计算高频用户占比（3次以上）
            high_frequency_count = 0
            for i, freq in enumerate(frequency_ranges):
                if freq in ["3次", "4次", "5次", "5次以上"]:
                    high_frequency_count += user_counts[i]

            high_frequency_ratio = (high_frequency_count / total_users * 100) if total_users > 0 else 0

            # 构建时间范围信息
            time_info = ""
            if start_date and end_date:
                time_info = f"数据时间范围：{start_date} 至 {end_date}\n"
            elif start_date:
                time_info = f"数据时间范围：{start_date} 开始\n"
            elif end_date:
                time_info = f"数据时间范围：截至 {end_date}\n"

            summary = f"""
{time_info}总用户数：{total_users}人
频次分布：{total_frequency_types}个频次区间
最活跃频次：{most_active_frequency}（{most_active_count}人，占比{most_active_count/total_users*100:.1f}%）
单次充值用户：{single_charge_count}人（占比{single_charge_ratio:.1f}%）
高频用户（3次以上）：{high_frequency_count}人（占比{high_frequency_ratio:.1f}%）
详细分布：{[f"{frequency_ranges[i]}({user_counts[i]}人)" for i in range(len(frequency_ranges))]}
"""
            return summary.strip()

        except Exception as e:
            logger.error(f"格式化充值频次分布数据摘要失败: {str(e)}")
            return f"数据格式化失败: {str(e)}"


# 创建全局分析器实例
charge_distribution_ai_analyzer = ChargeDistributionAiAnalyzer()
