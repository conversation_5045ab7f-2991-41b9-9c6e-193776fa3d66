"""
会员基础数据SQL查询模块
将每个SQL查询拆分成独立的函数，便于维护和独立管理
"""

from typing import Optional
import logging

logger = logging.getLogger(__name__)

class MemberBaseSqlQueries:
    """会员基础数据SQL查询类 - 每个查询都是独立的函数"""
    
    # ========== wedatas数据库相关查询 ==========
    

    
    @staticmethod
    def get_dwoutput_total_all_user_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取会员总数量查询SQL

        数据库：dprpt_welife_users_stat
        字段：uCardNum（会员卡总数量）
        计算方式：最后一天的uCardNum求和
        说明：会员卡总数量

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            会员总数量查询SQL
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""
        max_date_subquery = MemberBaseSqlQueries.get_dwoutput_max_date_subquery(start_date, end_date, bid, sid, "dprpt_welife_users_stat")

        return f"""
        (SELECT SUM(uCardNum)
         FROM dprpt_welife_users_stat
         WHERE ftime = ({max_date_subquery})
         AND bid = {bid}
         {sid_condition}) AS total_all_user
        """
    
    @staticmethod
    def get_dwoutput_net_all_user_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取会员净存量查询SQL

        数据库：dprpt_welife_users_stat
        字段：uCardRegisteredNum（会员卡净存数量）
        计算方式：最后一天的uCardRegisteredNum求和
        说明：会员卡净存数量

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            会员净存量查询SQL
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""
        max_date_subquery = MemberBaseSqlQueries.get_dwoutput_max_date_subquery(start_date, end_date, bid, sid, "dprpt_welife_users_stat")

        return f"""
        (SELECT SUM(uCardRegisteredNum)
         FROM dprpt_welife_users_stat
         WHERE ftime = ({max_date_subquery})
         AND bid = {bid}
         {sid_condition}) AS net_all_user
        """
    
    @staticmethod
    def get_dwoutput_total_cancel_user_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取取关会员数量查询SQL（最后一天）

        数据库：dprpt_welife_users_stat
        字段：uCardNum - uCardRegisteredNum（取关会员数量）
        计算方式：最后一天的(uCardNum - uCardRegisteredNum)求和
        说明：取关会员数量 = 会员卡总数量 - 会员卡净存数量

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            取关会员数量查询SQL
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""
        max_date_subquery = MemberBaseSqlQueries.get_dwoutput_max_date_subquery(start_date, end_date, bid, sid, "dprpt_welife_users_stat")

        return f"""
        (SELECT SUM(uCardNum - uCardRegisteredNum)
         FROM dprpt_welife_users_stat
         WHERE ftime = ({max_date_subquery})
         AND bid = {bid}
         {sid_condition}) AS total_cancel_user
        """
    
    @staticmethod
    def get_dwoutput_new_user_total_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取新增会员数量查询SQL

        数据库：dprpt_welife_users_stat
        字段：uIncreNum（总新增会员数）
        计算方式：时间范围内所有天的uIncreNum求和
        说明：统计时间范围内的新增会员总数

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            新增会员数量查询SQL
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""

        return f"""
        (SELECT SUM(uIncreNum)
         FROM dprpt_welife_users_stat
         WHERE ftime BETWEEN {start_date} AND {end_date}
           AND bid = {bid}
           {sid_condition}) AS new_user_total
        """
    

    

    
    @staticmethod
    def get_dwoutput_new_cancel_user_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取新增取消注册会员数查询SQL

        数据库：dprpt_welife_users_stat
        字段：uCancelRegisteredNum（取消注册会员数）
        计算方式：时间范围内所有天的uCancelRegisteredNum求和
        说明：统计时间范围内取消注册的会员总数

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            新增取消注册会员数查询SQL
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""

        return f"""
        (SELECT SUM(uCancelRegisteredNum)
         FROM dprpt_welife_users_stat
         WHERE ftime BETWEEN {start_date} AND {end_date}
           AND bid = {bid}
           {sid_condition}) AS new_cancel_user
        """
    
    # ========== dwoutput数据库相关查询 ==========
    
    @staticmethod
    def get_dwoutput_max_date_subquery(start_date: str, end_date: str, bid: str, sid: Optional[str] = None, table_name: str = "dprpt_welife_user_log") -> str:
        """获取dwoutput数据库时间范围内的最后一天日期子查询
        
        数据库：dwoutput（多个表）
        用途：获取时间范围内的最大日期，用于其他查询的时间过滤
        
        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)
            table_name: 表名
        
        Returns:
            最大日期子查询SQL
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""
        
        return f"""
        SELECT MAX(ftime)
        FROM {table_name}
        WHERE ftime BETWEEN {start_date} AND {end_date}
          AND bid = {bid}
          {sid_condition}
        """
    
    @staticmethod
    def get_dwoutput_total_all_user_consomer_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取累计消费会员查询SQL（最后一天）
        
        数据库：dprpt_welife_user_log
        字段：all_user_consomer（累计消费会员数）
        计算方式：最后一天的all_user_consomer求和
        说明：累计到当前为止有过消费行为的会员总数
        
        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)
        
        Returns:
            累计消费会员查询SQL
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""
        max_date_subquery = MemberBaseSqlQueries.get_dwoutput_max_date_subquery(start_date, end_date, bid, sid, "dprpt_welife_user_log")
        
        return f"""
        (SELECT SUM(all_user_consomer)
         FROM dprpt_welife_user_log
         WHERE ftime = ({max_date_subquery})
         AND bid = {bid}
         {sid_condition}) AS total_all_user_consomer
        """
    
    @staticmethod
    def get_dwoutput_total_all_user_charger_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取累计储值会员查询SQL（最后一天）
        
        数据库：dprpt_welife_user_log
        字段：all_user_charger（累计储值会员数）
        计算方式：最后一天的all_user_charger求和
        说明：累计到当前为止有过储值行为的会员总数
        
        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)
        
        Returns:
            累计储值会员查询SQL
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""
        max_date_subquery = MemberBaseSqlQueries.get_dwoutput_max_date_subquery(start_date, end_date, bid, sid, "dprpt_welife_user_log")
        
        return f"""
        (SELECT SUM(all_user_charger)
         FROM dprpt_welife_user_log
         WHERE ftime = ({max_date_subquery})
         AND bid = {bid}
         {sid_condition}) AS total_all_user_charger
        """
    
    @staticmethod
    def get_dwoutput_new_user_consomer_total_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取新增有消费的会员数查询SQL
        
        数据库：dprpt_welife_user_log
        字段：new_user_consomer（新增消费会员数）
        计算方式：时间范围内所有天的new_user_consomer求和
        说明：统计时间范围内首次产生消费行为的会员数
        
        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)
        
        Returns:
            新增有消费的会员数查询SQL
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""
        
        return f"""
        (SELECT SUM(new_user_consomer)
         FROM dprpt_welife_user_log
         WHERE ftime BETWEEN {start_date} AND {end_date}
           AND bid = {bid}
           {sid_condition}) AS new_user_consomer_total
        """
    
    @staticmethod
    def get_dwoutput_new_user_charger_total_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取新增有储值的会员数查询SQL
        
        数据库：dprpt_welife_user_log
        字段：new_user_charger（新增储值会员数）
        计算方式：时间范围内所有天的new_user_charger求和
        说明：统计时间范围内首次产生储值行为的会员数
        
        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)
        
        Returns:
            新增有储值的会员数查询SQL
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""
        
        return f"""
        (SELECT SUM(new_user_charger)
         FROM dprpt_welife_user_log
         WHERE ftime BETWEEN {start_date} AND {end_date}
           AND bid = {bid}
           {sid_condition}) AS new_user_charger_total
        """
    
    @staticmethod
    def get_dwoutput_total_card_phone_num_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取会员手机号完善数查询SQL

        数据库：dprpt_welife_users_stat
        字段：uCardPhoneNum（会员手机号完善数）
        计算方式：最后一天的uCardPhoneNum求和
        说明：统计完善了手机号信息的会员数量

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            会员手机号完善数查询SQL
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""
        max_date_subquery = MemberBaseSqlQueries.get_dwoutput_max_date_subquery(start_date, end_date, bid, sid, "dprpt_welife_users_stat")

        return f"""
        (SELECT SUM(uCardPhoneNum)
         FROM dprpt_welife_users_stat
         WHERE ftime = ({max_date_subquery})
         AND bid = {bid}
         {sid_condition}) AS total_card_phone_num
        """
    
    @staticmethod
    def get_dwoutput_total_card_info_num_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取会员信息完善数查询SQL

        数据库：dprpt_welife_users_stat
        字段：uCardInfoNum（会员信息完善数）
        计算方式：最后一天的uCardInfoNum求和
        说明：统计完善了基本信息的会员数量

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            会员信息完善数查询SQL
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""
        max_date_subquery = MemberBaseSqlQueries.get_dwoutput_max_date_subquery(start_date, end_date, bid, sid, "dprpt_welife_users_stat")

        return f"""
        (SELECT SUM(uCardInfoNum)
         FROM dprpt_welife_users_stat
         WHERE ftime = ({max_date_subquery})
         AND bid = {bid}
         {sid_condition}) AS total_card_info_num
        """
    
    @staticmethod
    def get_dwoutput_total_consume_1_num_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取消费1次的会员数查询SQL

        数据库：dprpt_welife_users_stat
        字段：uConsume1Num（消费1次的会员数）
        计算方式：最后一天的uConsume1Num求和
        说明：统计只消费过1次的会员数量

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            消费1次的会员数查询SQL
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""
        max_date_subquery = MemberBaseSqlQueries.get_dwoutput_max_date_subquery(start_date, end_date, bid, sid, "dprpt_welife_users_stat")

        return f"""
        (SELECT SUM(uConsume1Num)
         FROM dprpt_welife_users_stat
         WHERE ftime = ({max_date_subquery})
         AND bid = {bid}
         {sid_condition}) AS total_consume_1_num
        """
    
    @staticmethod
    def get_dwoutput_total_consume_2plus_num_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取消费2次以上的会员数查询SQL

        数据库：dprpt_welife_users_stat
        字段：uConsume2Num（消费2次以上的会员数）
        计算方式：最后一天的uConsume2Num求和
        说明：统计消费过2次或以上的会员数量，体现客户粘性

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            消费2次以上的会员数查询SQL
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""
        max_date_subquery = MemberBaseSqlQueries.get_dwoutput_max_date_subquery(start_date, end_date, bid, sid, "dprpt_welife_users_stat")

        return f"""
        (SELECT SUM(uConsume2Num)
         FROM dprpt_welife_users_stat
         WHERE ftime = ({max_date_subquery})
         AND bid = {bid}
         {sid_condition}) AS total_consume_2plus_num
        """
    
    @staticmethod
    def get_dwoutput_total_user_info_num_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取新增完善会员量查询SQL

        数据库：dprpt_welife_users_stat
        字段：uUserInfoNum（新增完善会员量）
        计算方式：时间范围内所有天的uUserInfoNum求和
        说明：统计新增的完善了信息的会员数量，用于计算完善率

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            新增完善会员量查询SQL
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""

        return f"""
        SELECT SUM(uUserInfoNum) AS total_user_info_num
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN {start_date} AND {end_date}
        AND bid = {bid}
        {sid_condition}
        """
    
    @staticmethod
    def get_dwoutput_total_consume_0_num_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取消费0次的会员数查询SQL

        数据库：dprpt_welife_users_stat
        字段：uConsume0Num（消费0次的会员数）
        计算方式：最后一天的uConsume0Num求和
        说明：统计从未消费过的会员数量

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            消费0次的会员数查询SQL
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""
        max_date_subquery = MemberBaseSqlQueries.get_dwoutput_max_date_subquery(start_date, end_date, bid, sid, "dprpt_welife_users_stat")

        return f"""
        (SELECT SUM(uConsume0Num)
         FROM dprpt_welife_users_stat
         WHERE ftime = ({max_date_subquery})
         AND bid = {bid}
         {sid_condition}) AS total_consume_0_num
        """
    
    # ========== 组合查询构建函数 ==========
    
    @staticmethod
    def build_dwoutput_member_base_sql_v2(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """构建会员基础数据查询SQL（使用dwoutput数据库）

        注意：此函数现在使用SQL片段组合，每个函数返回完整的子查询

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            SQL查询字符串
        """
        # 组合所有查询片段
        sql = f"""
        SELECT
          {MemberBaseSqlQueries.get_dwoutput_total_all_user_sql(start_date, end_date, bid, sid)},
          {MemberBaseSqlQueries.get_dwoutput_net_all_user_sql(start_date, end_date, bid, sid)},
          {MemberBaseSqlQueries.get_dwoutput_total_cancel_user_sql(start_date, end_date, bid, sid)},
          {MemberBaseSqlQueries.get_dwoutput_new_user_total_sql(start_date, end_date, bid, sid)},
          {MemberBaseSqlQueries.get_dwoutput_new_cancel_user_sql(start_date, end_date, bid, sid)}
        """

        logger.debug(f"构建会员基础数据查询SQL: bid={bid}, sid={sid}, date_range={start_date}-{end_date}")
        return sql
    
    @staticmethod
    def build_dwoutput_member_base_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """构建dwoutput数据库的会员基础数据查询SQL
        
        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)
        
        Returns:
            SQL查询字符串
        """
        # 组合所有dwoutput会员日志相关的查询
        sql = f"""
        SELECT
          {MemberBaseSqlQueries.get_dwoutput_total_all_user_consomer_sql(start_date, end_date, bid, sid)},
          {MemberBaseSqlQueries.get_dwoutput_total_all_user_charger_sql(start_date, end_date, bid, sid)},
          {MemberBaseSqlQueries.get_dwoutput_new_user_consomer_total_sql(start_date, end_date, bid, sid)},
          {MemberBaseSqlQueries.get_dwoutput_new_user_charger_total_sql(start_date, end_date, bid, sid)}
        """
        
        logger.debug(f"构建dwoutput查询SQL: bid={bid}, sid={sid}, date_range={start_date}-{end_date}")
        return sql
    
    @staticmethod
    def build_dwoutput_member_stats_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """构建dwoutput数据库的会员统计数据查询SQL

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            SQL查询字符串
        """
        # 组合所有dwoutput统计相关的查询（每个函数已经返回完整的子查询）
        sql = f"""
        SELECT
          {MemberBaseSqlQueries.get_dwoutput_total_card_phone_num_sql(start_date, end_date, bid, sid)},
          {MemberBaseSqlQueries.get_dwoutput_total_card_info_num_sql(start_date, end_date, bid, sid)},
          {MemberBaseSqlQueries.get_dwoutput_total_consume_1_num_sql(start_date, end_date, bid, sid)},
          {MemberBaseSqlQueries.get_dwoutput_total_consume_2plus_num_sql(start_date, end_date, bid, sid)},
          {MemberBaseSqlQueries.get_dwoutput_total_consume_0_num_sql(start_date, end_date, bid, sid)}
        """

        logger.debug(f"构建dwoutput统计查询SQL: bid={bid}, sid={sid}, date_range={start_date}-{end_date}")
        return sql
    
    @staticmethod
    def build_wedatas_new_user_count_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """构建获取新增会员数量的SQL（用于计算新增会员完善率的分母）

        使用 dprpt_welife_users_stat 表的 uIncreNum 字段

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            SQL查询字符串
        """
        sql = f"""
        SELECT
          {MemberBaseSqlQueries.get_dwoutput_new_user_total_sql(start_date, end_date, bid, sid)}
        """

        logger.debug(f"构建新增用户数查询SQL: bid={bid}, sid={sid}, date_range={start_date}-{end_date}")
        return sql


class MemberBaseSqlCalculator:
    """会员基础数据计算器"""
    
    @staticmethod
    def calculate_cancel_user_rate(new_cancel_user: float, new_user_total: float) -> float:
        """计算取关占比

        计算公式：取关占比 = 新增取消注册会员数 / 新增会员总数

        Args:
            new_cancel_user: 新增取消注册会员数 (来自dprpt_welife_users_stat.uCancelRegisteredNum)
            new_user_total: 新增会员总数 (来自dprpt_welife_users_stat.uIncreNum)

        Returns:
            取关占比（小数形式）
        """
        try:
            # 确保数据类型转换为float
            new_cancel_user = float(new_cancel_user) if new_cancel_user is not None else 0.0
            new_user_total = float(new_user_total) if new_user_total is not None else 0.0

            if new_user_total == 0:
                logger.warning("新增会员总数为0，取关占比无法计算")
                return 0.0

            # 计算取关占比
            rate = new_cancel_user / new_user_total

            logger.debug(f"取关占比计算: {new_cancel_user} / {new_user_total} = {rate}")
            return round(rate, 4)

        except Exception as e:
            logger.error(f"计算取关占比失败: {str(e)}")
            return 0.0
    
    @staticmethod
    def calculate_new_user_info_rate(total_first_info_num: float, new_user_count: float) -> float:
        """计算新增会员完善率
        
        计算公式：新增会员完善率 = 新增完善会员量 / 新增会员量
        
        Args:
            total_first_info_num: 新增完善会员量 (来自dprpt_welife_users_stat.uUserInfoNum)
            new_user_count: 新增会员量 (来自wedatas_user_card_category_stat.new_user)
        
        Returns:
            新增会员完善率（小数形式）
        """
        try:
            # 确保数据类型转换为float
            total_first_info_num = float(total_first_info_num) if total_first_info_num is not None else 0.0
            new_user_count = float(new_user_count) if new_user_count is not None else 0.0
            
            if new_user_count == 0:
                logger.warning("新增会员数为0，完善率无法计算")
                return 0.0
            
            # 计算完善率
            rate = total_first_info_num / new_user_count
            
            logger.debug(f"新增会员完善率计算: {total_first_info_num} / {new_user_count} = {rate}")
            return round(rate, 4)
            
        except Exception as e:
            logger.error(f"计算新增会员完善率失败: {str(e)}")
            return 0.0 