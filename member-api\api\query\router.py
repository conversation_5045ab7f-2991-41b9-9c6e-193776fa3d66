from fastapi import APIRouter, Depends
from auth.middleware.auth_middleware import get_current_user
from .MemberBaseTab import router as member_base_router
from .MemberConsumeTab import router as member_consume_router
from .MemberChargeTab import router as member_charge_router
from .MemberDataQuery import router as member_data_router
from .inquiry import router as inquiry_router  # 导入inquiry路由

# 创建查询模块的主路由（需要认证）
router = APIRouter(dependencies=[Depends(get_current_user)])

# 注册统一数据查询路由
router.include_router(member_data_router, prefix="/data", tags=["会员数据查询"])

# 注册inquiry路由（周报、月报、季报、半年报、SID查询）
router.include_router(inquiry_router, prefix="/inquiry", tags=["报表查询"])

# 注册子路由
router.include_router(member_base_router, prefix="/member-base", tags=["会员基础数据"])
router.include_router(member_consume_router, prefix="/member-consume", tags=["会员消费数据"])
router.include_router(member_charge_router, prefix="/member-charge", tags=["会员充值数据"])

# 券交易模块和品智收银模块路由在MemberDataQuery中统一处理
