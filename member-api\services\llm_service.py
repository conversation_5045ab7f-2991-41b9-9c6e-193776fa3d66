"""
LLM服务模块，处理与LLM的交互
"""
import json
import logging
from typing import Dict, Any, List, Generator
import openai
from core.config import settings

# 配置日志
logger = logging.getLogger(__name__)

# 配置OpenAI API
# 初始化OpenAI客户端
try:
    # 详细的配置调试信息
    logger.info("=== LLM服务配置调试 ===")
    logger.info(f"API密钥长度: {len(settings.DASHSCOPE_API_KEY) if settings.DASHSCOPE_API_KEY else 0}")
    logger.info(f"API密钥前缀: {settings.DASHSCOPE_API_KEY[:10] if settings.DASHSCOPE_API_KEY else 'None'}...")
    logger.info(f"Base URL: {settings.DASHSCOPE_BASE_URL}")
    logger.info(f"模型: {settings.DASHSCOPE_MODEL}")

    # 检查API密钥配置
    if not settings.DASHSCOPE_API_KEY or settings.DASHSCOPE_API_KEY == "your_api_key_here":
        logger.error("DASHSCOPE_API_KEY未正确配置，大模型功能将不可用")
        logger.error("请检查.env文件中的DASHSCOPE_API_KEY配置")
        client = None
    else:
        logger.info("正在初始化OpenAI客户端...")

        # 尝试多种初始化方式以解决代理兼容性问题
        try:
            # 方案1：使用自定义 httpx 客户端，不设置proxies参数
            try:
                import httpx
                http_client = httpx.Client()  # 不设置proxies参数
                client = openai.OpenAI(
                    api_key=settings.DASHSCOPE_API_KEY,
                    base_url=settings.DASHSCOPE_BASE_URL,
                    http_client=http_client
                )
                logger.info("✅ OpenAI客户端初始化成功（使用自定义httpx客户端）")
            except ImportError:
                logger.warning("httpx 库不可用，跳过自定义客户端方案")
                raise Exception("httpx not available")
        except Exception as e1:
            logger.warning(f"自定义httpx客户端初始化失败: {e1}")
            try:
                # 方案2：设置环境变量禁用代理
                import os
                os.environ['NO_PROXY'] = '*'
                os.environ['HTTP_PROXY'] = ''
                os.environ['HTTPS_PROXY'] = ''
                client = openai.OpenAI(
                    api_key=settings.DASHSCOPE_API_KEY,
                    base_url=settings.DASHSCOPE_BASE_URL
                )
                logger.info("✅ OpenAI客户端初始化成功（禁用代理环境变量）")
            except Exception as e2:
                logger.warning(f"禁用代理环境变量初始化失败: {e2}")
                # 方案3：基础初始化（原始方式）
                client = openai.OpenAI(
                    api_key=settings.DASHSCOPE_API_KEY,
                    base_url=settings.DASHSCOPE_BASE_URL
                )
                logger.info("✅ OpenAI客户端初始化成功（基础方式）")
except Exception as e:
    logger.error(f"❌ OpenAI客户端初始化失败: {e}")
    logger.error(f"错误详情: {type(e).__name__}: {str(e)}")
    # 创建一个空的客户端对象，避免导入错误
    client = None

class LLMService:
    """LLM服务类，处理与LLM的交互"""
    
    def __init__(self):
        self.model = settings.DASHSCOPE_MODEL or "qwen-max"
        logger.info(f"LLMService初始化完成，使用模型: {self.model}")
    
    async def generate_response(self, prompt: str) -> str:
        """
        生成AI响应

        Args:
            prompt: 输入提示词

        Returns:
            str: AI生成的响应
        """
        try:
            # 检查客户端是否可用
            if client is None:
                logger.warning("OpenAI客户端未初始化，使用降级响应")
                return "AI分析暂时不可用，请检查大模型配置。"

            # 检查API密钥配置
            if not settings.DASHSCOPE_API_KEY or settings.DASHSCOPE_API_KEY == "your_api_key_here":
                logger.warning("DASHSCOPE_API_KEY未正确配置，使用降级响应")
                return "AI分析暂时不可用，请检查API密钥配置。"

            messages = [{"role": "user", "content": prompt}]
            response = self.call(messages, model=self.model)

            if "error" in response:
                logger.error(f"LLM调用失败: {response['error']}")
                return "AI分析暂时不可用，请稍后再试。"

            # 检查响应内容
            if "choices" not in response or not response["choices"]:
                logger.error("LLM响应格式异常：缺少choices")
                return "AI分析暂时不可用，响应格式异常。"

            content = response["choices"][0]["message"]["content"]
            if not content or content.strip() == "":
                logger.warning("LLM返回空内容")
                return "AI分析暂时不可用，返回内容为空。"

            return content

        except Exception as e:
            logger.error(f"生成AI响应失败: {str(e)}", exc_info=True)
            return "AI分析暂时不可用，请稍后再试。"
    
    def call_with_stream(self, messages: List[Dict], functions: List[Dict] = None, model: str = None, enable_search: bool = False) -> Generator[Dict, None, None]:
        """
        调用LLM并使用流式响应
        
        参数:
        - messages: 消息列表
        - functions: 函数列表（可选）
        - model: 指定使用的模型，如果为None则使用默认模型
        - enable_search: 是否启用联网搜索
        
        返回:
        - 生成器，逐步返回LLM响应的内容和推理内容
        """
        try:
            # 检查客户端是否可用
            if client is None:
                logger.warning("OpenAI客户端未初始化，返回错误响应")
                yield {"content": "AI服务暂时不可用，请检查配置", "reasoning_content": ""}
                return

            # 确定使用的模型
            # 联网搜索 https://help.aliyun.com/zh/model-studio/developer-reference/use-qwen-by-calling-api#b7a9dedef6gh2
            use_model = model if model else self.model
            logger.info(f"流式调用LLM，模型: {use_model}, enable_search={enable_search}")

            # 构建请求参数
            params = {
                "model": use_model,
                "stream": True
            }

            # 如果启用联网搜索，添加参数, 只有qwen-max模型支持联网搜索
            if enable_search:
                params["extra_body"] = {"enable_search": True}
                params["model"] = "qwen-max"

            params["messages"] = messages

            logger.info(f"消息: {json.dumps(params, ensure_ascii=False)[:200]}...")

            # 流式调用
            response = client.chat.completions.create(**params)
                      
            content = ""
            reasoning_content = ""
            
            for chunk in response:
                # 记录完整的LLM响应
                # logger.info(chunk)

                if hasattr(chunk, 'choices') and len(chunk.choices) > 0:
                    delta = chunk.choices[0].delta
                    
                    # 处理content
                    if hasattr(delta, 'content') and delta.content is not None:
                        content = delta.content
                        yield {
                            "content": content,
                            "reasoning_content": ""
                        }

                    # 处理reasoning_content
                    if hasattr(delta, 'reasoning_content') and delta.reasoning_content is not None:
                        reasoning_content = delta.reasoning_content
                        yield {
                            "content": "",
                            "reasoning_content": reasoning_content
                        }

                # 处理usage
                if hasattr(chunk, 'usage') and chunk.usage is not None:
                    usage = {
                        "prompt_tokens": chunk.usage.prompt_tokens,
                        "completion_tokens": chunk.usage.completion_tokens,
                        "total_tokens": chunk.usage.total_tokens
                    }
                    yield {
                        "usage": usage
                    }
            
            logger.info("流式LLM调用完成")
            
        except Exception as e:
            logger.error(f"流式LLM调用错误: {str(e)}")
            yield {"content": f"错误: {str(e)}", "reasoning_content": ""}

    def call(self, messages: List[Dict], model: str = None, enable_search: bool = False) -> Dict:
        """
        普通的LLM调用，不使用function calling功能

        参数:
        - messages: 消息列表
        - model: 模型名称
        - enable_search: 是否启用联网搜索
        """
        try:
            # 检查客户端是否可用
            if client is None:
                logger.warning("OpenAI客户端未初始化，返回错误响应")
                return {
                    "error": "OpenAI客户端未初始化，请检查配置"
                }

            # 确定使用的模型
            use_model = model if model else self.model
            logger.info(f"调用LLM，模型: {use_model}, enable_search={enable_search}")
            logger.info(f"消息: {json.dumps(messages, ensure_ascii=False)[:200]}...")

            # 构建请求参数
            params = {
                "model": use_model,
                "messages": messages
            }

            # 如果启用联网搜索，添加extra_body参数
            if enable_search:
                params["extra_body"] = {
                    "enable_search": True
                }

            response = client.chat.completions.create(**params)

            # 记录完整的LLM响应
            logger.info(f"LLM响应: {str(response)}")

            return {
                "choices": [
                    {
                        "message": {
                            "content": response.choices[0].message.content,
                            "role": response.choices[0].message.role
                        }
                    }
                ],
                "usage": {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                }
            }
            
        except Exception as e:
            logger.error(f"LLM调用错误: {str(e)}")
            return {"error": str(e)}
