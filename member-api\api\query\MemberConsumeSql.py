"""
会员消费数据SQL查询模块
将每个SQL查询拆分成独立的函数，便于维护和独立管理
"""

from typing import Optional
import logging

logger = logging.getLogger(__name__)

class MemberConsumeSqlQueries:
    """会员消费数据SQL查询类 - 每个查询都是独立的函数"""
    
    # ========== dwoutput数据库消费基础数据查询 ==========
    
    @staticmethod
    def get_dwoutput_total_consume_amount_virtual_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取消费总金额查询SQL（非实收）
        
        数据库：dprpt_welife_consume_log
        字段：consume_amount（消费金额）, overdue_amount（过期金额）, cancel_amount（取消金额）
        计算方式：时间范围内所有天的(consume_amount + overdue_amount - cancel_amount)求和
        说明：消费总金额 = 消费金额 + 过期金额 - 取消金额（包含储值部分，非纯现金）
        注意：数据库存储的是真实值*100，需要在业务层除以100
        
        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)
        
        Returns:
            消费总金额查询SQL
        """
        return f"""
        SUM(consume_amount) + SUM(overdue_amount) - SUM(cancel_amount) AS total_consume_amount_virtual
        """
    
    @staticmethod
    def get_dwoutput_total_consume_uv_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取会员消费人数查询SQL

        数据库：dprpt_welife_trade_consume_detail
        字段：uno（卡号）
        计算方式：统计时间范围内不同uno卡号的数量
        说明：会员消费人数 = 不同卡号数量

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            会员消费人数查询SQL（需要单独查询）
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""

        return f"""
        SELECT COUNT(DISTINCT uno) AS total_consume_uv
        FROM dprpt_welife_trade_consume_detail
        WHERE ftime BETWEEN {start_date} AND {end_date}
        AND bid = {bid}
        AND tctype = 2
        {sid_condition}
        """


    
    @staticmethod
    def get_dwoutput_total_consume_pv_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取会员总消费笔数查询SQL（标准方法 - 推荐使用）

        ✅ 标准方法：这是计算会员总消费笔数的标准实现
        ✅ 业务统一：与现金消费笔数和储值消费笔数计算逻辑保持一致
        ✅ 性能优化：基于汇总表查询，性能较好

        数据库：dprpt_welife_consume_log（汇总表）
        字段：consume_amount_pv（消费笔数）, overdue_pv（过期笔数）
        计算方式：时间范围内所有天的(consume_amount_pv + overdue_pv)求和
        说明：会员总消费笔数 = 消费笔数 + 过期笔数 - 取消笔数（可以和总消费单数对上）

        使用场景：
        - 前端total_consume_count字段的数据源
        - 消费频次计算的分子
        - 单均消费计算的分母

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            会员总消费笔数查询SQL（标准方法）
        """
        return f"""
        SUM(consume_amount_pv) + SUM(overdue_pv) - SUM(cancel_amount_pv) AS total_consume_pv
        """
    
    @staticmethod
    def get_dwoutput_total_consume_cash_real_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取会员实收现金金额查询SQL
        
        数据库：dprpt_welife_consume_log
        字段：consume_cash（现金消费金额）, cancel_cash（取消现金金额）
        计算方式：时间范围内所有天的(consume_cash - cancel_cash)求和
        说明：会员实收现金金额 = 现金消费金额 - 取消现金金额
        注意：数据库存储的是真实值*100，需要在业务层除以100
        
        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)
        
        Returns:
            会员实收现金金额查询SQL
        """
        return f"""
        SUM(consume_cash) - SUM(cancel_cash) AS total_consume_cash_real
        """
    
    @staticmethod
    def get_dwoutput_total_consume_cash_uv_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取现金支付会员数查询SQL

        数据库：dprpt_welife_trade_consume_detail
        字段：uno（卡号）
        计算方式：统计时间范围内tcfee > 0 AND tcstoredpay = 0的不同uno卡号数量
        说明：现金支付会员数 = 现金支付且无储值支付的不同卡号数量

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            现金支付会员数查询SQL（需要单独查询）
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""

        return f"""
        SELECT COUNT(DISTINCT CASE WHEN tcfee > 0 AND tcstoredpay = 0 THEN uno END) AS total_consume_cash_uv
        FROM dprpt_welife_trade_consume_detail
        WHERE ftime BETWEEN {start_date} AND {end_date}
        AND bid = {bid}
        AND tctype = 2
        {sid_condition}
        """
    
    @staticmethod
    def get_dwoutput_total_consume_cash_pv_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取会员现金消费笔数查询SQL

        ✅ 与标准方法逻辑一致：不包含取消处理，确保数据一致性

        数据库：dprpt_welife_consume_log
        字段：consume_cash_pv（现金消费笔数）
        计算方式：时间范围内所有天的consume_cash_pv求和
        说明：会员现金消费笔数 = 现金消费笔数 - 取消现金支付笔数（可以和总消费单数对上）

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            会员现金消费笔数查询SQL
        """
        return f"""
        SUM(consume_cash_pv) - SUM(cancel_cash_pv) AS total_consume_cash_pv
        """
    
    @staticmethod
    def get_dwoutput_total_prepay_real_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取储值实收金额查询SQL
        
        数据库：dprpt_welife_consume_log
        字段：consume_prepay（储值消费金额）, cancel_prepay（取消储值金额）, overdue_amount（过期金额）
        计算方式：时间范围内所有天的(consume_prepay - cancel_prepay + overdue_amount)求和
        说明：储值实收金额 = 储值消费金额 - 取消储值金额 + 过期金额
        注意：数据库存储的是真实值*100，需要在业务层除以100
        
        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)
        
        Returns:
            储值实收金额查询SQL
        """
        return f"""
        SUM(consume_prepay) - SUM(cancel_prepay) + SUM(overdue_amount) AS total_prepay_real
        """
    
    @staticmethod
    def get_dwoutput_total_prepay_uv_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取储值支付会员数查询SQL

        数据库：dprpt_welife_trade_consume_detail
        字段：uno（卡号）
        计算方式：统计时间范围内tcstoredpay > 0的不同uno卡号数量
        说明：储值支付会员数 = 储值支付的不同卡号数量

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            储值支付会员数查询SQL（需要单独查询）
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""

        return f"""
        SELECT COUNT(DISTINCT CASE WHEN tcstoredpay > 0 THEN uno END) AS total_prepay_uv
        FROM dprpt_welife_trade_consume_detail
        WHERE ftime BETWEEN {start_date} AND {end_date}
        AND bid = {bid}
        AND tctype = 2
        {sid_condition}
        """
    
    @staticmethod
    def get_dwoutput_total_prepay_pv_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取储值消费笔数查询SQL

        ✅ 与标准方法逻辑一致：包含过期处理但不包含取消处理，确保数据一致性

        数据库：dprpt_welife_consume_log
        字段：consume_prepay_pv（储值消费笔数）, overdue_pv（过期笔数）
        计算方式：时间范围内所有天的(consume_prepay_pv + overdue_pv)求和
        说明：储值消费笔数 = 储值消费笔数 - 取消储值笔数 + 过期笔数（可以和总消费单数对上）

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            储值消费笔数查询SQL
        """
        return f"""
        SUM(consume_prepay_pv) - SUM(cancel_prepay_pv) + SUM(overdue_pv) AS total_prepay_pv
        """
    
    # ========== dwoutput数据库储值使用详情查询 ==========
    
    @staticmethod
    def get_dwoutput_total_prepay_used_real_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取会员使用储值的实收金额查询SQL - 新逻辑
        
        计算方式：前一天沉淀金额 - 最后一天沉淀金额 + 期间实收净充值
        说明：通过沉淀金额变化和期间充值来计算实际消耗
        注意：数据库存储的是真实值*100，需要在业务层除以100
        
        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)
        
        Returns:
            会员使用储值的实收金额查询SQL
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""
        
        # 计算前一天日期
        from datetime import datetime, timedelta
        start_dt = datetime.strptime(start_date, "%Y%m%d")
        prev_date = (start_dt - timedelta(days=1)).strftime("%Y%m%d")
        
        return f"""
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = {prev_date} AND bid = {bid} {sid_condition})
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = {end_date} AND bid = {bid} {sid_condition})
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN {start_date} AND {end_date} AND bid = {bid} {sid_condition})
        ) AS total_prepay_used_real
        """
    
    # ========== wedatas数据库券带动交易查询 ==========
    
    @staticmethod
    def get_wedatas_total_coupon_trade_amount_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取券带动总交易金额查询SQL
        
        数据库：wedatas_welife_coupon_log
        字段：trade_amount（交易金额）, cancel_trade_amount（取消交易金额）
        计算方式：时间范围内所有天的(trade_amount - cancel_trade_amount)求和
        说明：券带动总交易金额 = 交易金额 - 取消交易金额
        注意：数据库存储的是真实值*100，需要在业务层除以100
        
        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)
        
        Returns:
            券带动总交易金额查询SQL
        """
        return f"""
        SUM(trade_amount) - SUM(cancel_trade_amount) AS total_coupon_trade_amount
        """
    
    # ========== 组合查询构建函数 ==========
    
    @staticmethod
    def build_dwoutput_consume_base_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """构建dwoutput数据库的消费基础数据查询SQL

        注意：会员消费人数(total_consume_uv)、现金支付会员数(total_consume_cash_uv)、
        储值支付会员数(total_prepay_uv)需要单独查询，因为数据源不同

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            SQL查询字符串
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""

        # 组合消费基础数据查询（除了消费人数、现金支付会员数、储值支付会员数）
        sql = f"""
        SELECT
        {MemberConsumeSqlQueries.get_dwoutput_total_consume_amount_virtual_sql(start_date, end_date, bid, sid)},
        {MemberConsumeSqlQueries.get_dwoutput_total_consume_pv_sql(start_date, end_date, bid, sid)},
        {MemberConsumeSqlQueries.get_dwoutput_total_consume_cash_real_sql(start_date, end_date, bid, sid)},
        {MemberConsumeSqlQueries.get_dwoutput_total_consume_cash_pv_sql(start_date, end_date, bid, sid)},
        {MemberConsumeSqlQueries.get_dwoutput_total_prepay_real_sql(start_date, end_date, bid, sid)},
        {MemberConsumeSqlQueries.get_dwoutput_total_prepay_pv_sql(start_date, end_date, bid, sid)}
        FROM dprpt_welife_consume_log
        WHERE ftime BETWEEN {start_date} AND {end_date}
        AND bid = {bid}
        {sid_condition}
        """

        logger.debug(f"构建dwoutput消费基础查询SQL: bid={bid}, sid={sid}, date_range={start_date}-{end_date}")
        return sql

    @staticmethod
    def build_dwoutput_consume_uv_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """构建dwoutput数据库的会员消费人数查询SQL

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            会员消费人数查询SQL
        """
        return MemberConsumeSqlQueries.get_dwoutput_total_consume_uv_sql(start_date, end_date, bid, sid)
    
    @staticmethod
    def build_dwoutput_consume_detail_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """构建dwoutput数据库的储值使用详情查询SQL
        
        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)
        
        Returns:
            SQL查询字符串
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""
        
        # 组合储值使用详情查询
        sql = f"""
        SELECT
        {MemberConsumeSqlQueries.get_dwoutput_total_prepay_used_real_sql(start_date, end_date, bid, sid)}
        FROM dprpt_welife_trade_consume_detail
        WHERE ftime BETWEEN {start_date} AND {end_date}
        AND bid = {bid}
        {sid_condition}
        """
        
        logger.debug(f"构建dwoutput储值使用详情查询SQL: bid={bid}, sid={sid}, date_range={start_date}-{end_date}")
        return sql
    
    @staticmethod
    def build_wedatas_coupon_trade_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """构建wedatas数据库的券带动交易查询SQL
        
        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)
        
        Returns:
            SQL查询字符串
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""
        
        # 组合券带动交易查询
        sql = f"""
        SELECT
        {MemberConsumeSqlQueries.get_wedatas_total_coupon_trade_amount_sql(start_date, end_date, bid, sid)}
        FROM wedatas_welife_coupon_log
        WHERE ftime BETWEEN {start_date} AND {end_date}
        AND bid = {bid}
        {sid_condition}
        """
        
        logger.debug(f"构建wedatas券交易查询SQL: bid={bid}, sid={sid}, date_range={start_date}-{end_date}")
        return sql


class MemberConsumeCalculator:
    """会员消费数据计算器"""
    
    @staticmethod
    def calculate_total_real_income(consume_cash_real: float, prepay_used_real: float) -> float:
        """计算会员总实收金额
        
        计算公式：会员总实收金额 = 会员实收现金金额 + 会员使用储值的实收金额
        
        Args:
            consume_cash_real: 会员实收现金金额 (来自dprpt_welife_consume_log.consume_cash - cancel_cash)
            prepay_used_real: 会员使用储值的实收金额 (来自dprpt_welife_trade_consume_detail.tclprinciple)
        
        Returns:
            总实收金额
        """
        try:
            # 确保数据类型转换为float
            consume_cash_real = float(consume_cash_real) if consume_cash_real is not None else 0.0
            prepay_used_real = float(prepay_used_real) if prepay_used_real is not None else 0.0
            
            total = consume_cash_real + prepay_used_real
            logger.debug(f"总实收金额计算: {consume_cash_real} + {prepay_used_real} = {total}")
            return total
        except Exception as e:
            logger.error(f"计算总实收金额失败: {str(e)}")
            return 0.0
    
    @staticmethod
    def calculate_avg_contribution_per_user(total_real_income: float, total_consume_uv: int) -> float:
        """计算会员人均贡献
        
        计算公式：会员人均贡献 = 会员总实收金额 / 会员消费人数
        
        Args:
            total_real_income: 会员总实收金额 (计算得出)
            total_consume_uv: 会员消费人数 (来自dprpt_welife_consume_log.consume_amount_uv + overdue_uv)
        
        Returns:
            人均贡献金额
        """
        try:
            # 确保数据类型转换
            total_real_income = float(total_real_income) if total_real_income is not None else 0.0
            total_consume_uv = int(total_consume_uv) if total_consume_uv is not None else 0
            
            if total_consume_uv == 0:
                logger.warning("会员消费人数为0，人均贡献无法计算")
                return 0.0
            
            avg_contribution = total_real_income / total_consume_uv
            result = round(avg_contribution, 2)
            
            logger.debug(f"人均贡献计算: {total_real_income} / {total_consume_uv} = {result}")
            return result
            
        except Exception as e:
            logger.error(f"计算人均贡献失败: {str(e)}")
            return 0.0

    @staticmethod
    def calculate_consume_frequency(total_consume_pv: int, total_consume_uv: int) -> float:
        """计算会员消费频次

        计算公式：会员消费频次 = 会员总消费笔数 / 会员消费人数

        数据源一致性：
        - total_consume_pv 来自 dprpt_welife_consume_log（消费笔数 + 过期笔数 - 取消笔数）
        - total_consume_uv 来自 dprpt_welife_trade_consume_detail（明细表不同uno数）
        - 使用汇总表计算方式确保数据准确性

        Args:
            total_consume_pv: 会员总消费笔数（来自汇总表计算）
            total_consume_uv: 会员消费人数（来自明细表）

        Returns:
            消费频次（次/人），保留两位小数
        """
        try:
            # 确保数据类型转换
            total_consume_pv = int(total_consume_pv) if total_consume_pv is not None else 0
            total_consume_uv = int(total_consume_uv) if total_consume_uv is not None else 0

            if total_consume_uv == 0:
                logger.warning("会员消费人数为0，消费频次无法计算")
                return 0.0

            frequency = total_consume_pv / total_consume_uv
            result = round(frequency, 2)

            logger.debug(f"消费频次计算: {total_consume_pv} / {total_consume_uv} = {result}")
            return result

        except Exception as e:
            logger.error(f"计算消费频次失败: {str(e)}")
            return 0.0

    @staticmethod
    def calculate_avg_consume_amount(total_real_income: float, total_consume_pv: int) -> float:
        """计算会员单均消费

        计算公式：会员单均消费 = 会员总实收金额 / 会员总消费笔数

        数据源说明：
        - total_consume_pv 使用标准汇总表方法（get_dwoutput_total_consume_pv_sql）

        Args:
            total_real_income: 会员总实收金额
            total_consume_pv: 会员总消费笔数（来自汇总表标准方法）

        Returns:
            单均消费金额（元/笔），保留两位小数
        """
        try:
            # 确保数据类型转换
            total_real_income = float(total_real_income) if total_real_income is not None else 0.0
            total_consume_pv = int(total_consume_pv) if total_consume_pv is not None else 0

            if total_consume_pv == 0:
                logger.warning("会员总消费笔数为0，单均消费无法计算")
                return 0.0

            avg_amount = total_real_income / total_consume_pv
            result = round(avg_amount, 2)

            logger.debug(f"单均消费计算: {total_real_income} / {total_consume_pv} = {result}")
            return result

        except Exception as e:
            logger.error(f"计算单均消费失败: {str(e)}")
            return 0.0

    @staticmethod
    def merge_consume_data(base_data: dict, detail_data: dict, coupon_data: dict) -> dict:
        """合并消费数据
        
        Args:
            base_data: 消费基础数据 (来自dprpt_welife_consume_log表)
            detail_data: 储值使用详情数据 (来自dprpt_welife_trade_consume_detail表)
            coupon_data: 券交易数据 (来自wedatas_welife_coupon_log表)
        
        Returns:
            合并后的数据字典
        """
        try:
            logger.debug("开始合并消费数据")
            
            # 合并基础数据
            result = base_data.copy()
            
            # 添加储值使用详情
            total_prepay_used_real = detail_data.get('total_prepay_used_real', 0) or 0
            total_prepay_used_real = float(total_prepay_used_real) if total_prepay_used_real is not None else 0.0
            result['total_prepay_used_real'] = total_prepay_used_real
            
            # 添加券交易数据
            total_coupon_trade_amount = coupon_data.get('total_coupon_trade_amount', 0) or 0
            total_coupon_trade_amount = float(total_coupon_trade_amount) if total_coupon_trade_amount is not None else 0.0
            result['total_coupon_trade_amount'] = total_coupon_trade_amount
            
            # 计算总实收金额
            consume_cash_real = result.get('total_consume_cash_real', 0) or 0
            consume_cash_real = float(consume_cash_real) if consume_cash_real is not None else 0.0
            total_real_income = MemberConsumeCalculator.calculate_total_real_income(
                consume_cash_real, total_prepay_used_real
            )
            result['total_real_income'] = total_real_income
            
            # 计算人均贡献
            total_consume_uv = result.get('total_consume_uv', 0) or 0
            total_consume_uv = int(total_consume_uv) if total_consume_uv is not None else 0
            avg_contribution = MemberConsumeCalculator.calculate_avg_contribution_per_user(
                total_real_income, total_consume_uv
            )
            result['avg_contribution_per_user'] = avg_contribution

            # 计算消费频次（使用汇总表数据确保一致性）
            total_consume_pv = result.get('total_consume_pv', 0) or 0
            total_consume_pv = int(total_consume_pv) if total_consume_pv is not None else 0
            consume_frequency = MemberConsumeCalculator.calculate_consume_frequency(
                total_consume_pv, total_consume_uv
            )
            result['consume_frequency'] = consume_frequency

            # 计算单均消费（使用汇总表的消费笔数，复用上面的变量）
            avg_consume_amount = MemberConsumeCalculator.calculate_avg_consume_amount(
                total_real_income, total_consume_pv
            )
            result['avg_consume_amount'] = avg_consume_amount

            logger.debug(f"消费数据合并完成，字段数: {len(result)}")
            return result
            
        except Exception as e:
            logger.error(f"合并消费数据失败: {str(e)}")
            return {}
    
    @staticmethod
    def process_money_fields(data: dict, money_fields: list) -> dict:
        """处理金额字段（数据库存储的是真实值*100）
        
        Args:
            data: 原始数据
            money_fields: 需要处理的金额字段列表
        
        Returns:
            处理后的数据
        """
        try:
            result = data.copy()
            
            for field in money_fields:
                if field in result and result[field] is not None:
                    original_value = result[field]
                    # 数据库存储的金额是真实值*100，需要除以100还原
                    processed_value = float(original_value) / 100.0
                    result[field] = processed_value
                    logger.debug(f"金额字段处理: {field} {original_value} -> {processed_value}")
            
            return result
            
        except Exception as e:
            logger.error(f"处理金额字段失败: {str(e)}")
            return data

