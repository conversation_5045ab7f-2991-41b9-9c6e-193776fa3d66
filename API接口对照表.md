# API接口对照表

## 后端路由注册（member-api/main.py）

```python
app.include_router(auth_router, prefix="/api", tags=["认证"])
app.include_router(admin_router, prefix="/api", tags=["管理员"])
app.include_router(query_router, prefix="/api/query", tags=["查询"])
app.include_router(ppt_report_router, prefix="/api/ppt-report", tags=["PPT报告"])
app.include_router(custom_report_router, prefix="/api/ppt-report", tags=["自定义报告"])
```

## 1. 认证相关接口 (auth_router)

### back_management 前端调用
| 前端文件 | 前端路径 | 后端路径 | 状态 |
|---------|---------|---------|------|
| src/api/auth.js | `/auth/login` | `/api/auth/login` | ✅ |
| src/api/auth.js | `/auth/logout` | `/api/auth/logout` | ✅ |
| src/api/auth.js | `/auth/me` | `/api/auth/me` | ✅ |
| src/api/auth.js | `/auth/refresh` | `/api/auth/refresh` | ✅ |

### vue-element 前端调用
| 前端文件 | 前端路径 | 后端路径 | 状态 |
|---------|---------|---------|------|
| src/api/auth.js | `/auth/login` | `/api/auth/login` | ✅ |
| src/api/auth.js | `/auth/logout` | `/api/auth/logout` | ✅ |
| src/api/auth.js | `/auth/me` | `/api/auth/me` | ✅ |
| src/api/auth.js | `/auth/refresh` | `/api/auth/refresh` | ✅ |
| src/api/auth.js | `/auth/check-super-admin` | `/api/auth/check-super-admin` | ✅ |
| src/api/auth.js | `/auth/init-super-admin` | `/api/auth/init-super-admin` | ✅ |
| src/api/auth.js | `/auth/verify` | `/api/auth/verify` | ✅ |

基础URL配置:
- back_management: `VITE_API_BASE_URL=http://127.0.0.1:8000/api`
- vue-element: `VITE_API_BASE_URL=http://127.0.0.1:8000/api`

## 2. 管理员接口 (admin_router)

### back_management 前端调用
| 前端文件 | 前端路径 | 后端路径 | 状态 |
|---------|---------|---------|------|
| src/api/users.js | `/admin/users` | `/api/admin/users` | ✅ |
| src/api/users.js | `/admin/users/{id}` | `/api/admin/users/{id}` | ✅ |
| src/api/users.js | `/admin/users/{id}/reset-password` | `/api/admin/users/{id}/reset-password` | ✅ |
| src/api/users.js | `/admin/users/{id}/status` | `/api/admin/users/{id}/status` | ✅ |
| src/api/users.js | `/admin/users/{id}/quota` | `/api/admin/users/{id}/quota` | ✅ |
| src/api/users.js | `/admin/users/statistics` | `/api/admin/users/statistics` | ✅ |
| src/api/bid.js | `/admin/bid-configs` | `/api/admin/bid-configs` | ✅ |
| src/api/bid.js | `/admin/bid-configs/{id}` | `/api/admin/bid-configs/{id}` | ✅ |
| src/api/bid.js | `/admin/bid-configs/{id}/status` | `/api/admin/bid-configs/{id}/status` | ✅ |
| src/api/bid.js | `/admin/bid-configs/{id}/statistics` | `/api/admin/bid-configs/{id}/statistics` | ✅ |

## 3. 查询接口 (query_router)

### vue-element 前端调用
| 前端文件 | 前端路径 | 后端路径 | 状态 |
|---------|---------|---------|------|
| src/api/index.js | `/query/inquiry/week` | `/api/query/inquiry/week` | ✅ |
| src/api/index.js | `/query/inquiry/month` | `/api/query/inquiry/month` | ✅ |
| src/api/index.js | `/query/inquiry/quarter` | `/api/query/inquiry/quarter` | ✅ |
| src/api/index.js | `/query/inquiry/halfyear` | `/api/query/inquiry/halfyear` | ✅ |
| src/api/index.js | `/query/data/all` | `/api/query/data/all` | ✅ |
| src/api/index.js | `/query/data/member-base` | `/api/query/data/member-base` | ✅ |
| src/api/index.js | `/query/data/member-consume` | `/api/query/data/member-consume` | ✅ |
| src/api/index.js | `/query/data/member-charge` | `/api/query/data/member-charge` | ✅ |
| src/api/index.js | `/query/data/pinzhi-cashier` | `/api/query/data/pinzhi-cashier` | ✅ |
| src/api/index.js | `/query/data/coupon-trade` | `/api/query/data/coupon-trade` | ✅ |
| src/api/index.js | `/query/data/member-base/ai-analysis` | `/api/query/data/member-base/ai-analysis` | ✅ |
| src/api/index.js | `/query/data/member-consume/ai-analysis` | `/api/query/data/member-consume/ai-analysis` | ✅ |
| src/api/index.js | `/query/data/member-charge/ai-analysis` | `/api/query/data/member-charge/ai-analysis` | ✅ |
| src/api/index.js | `/query/data/coupon-trade/ai-analysis` | `/api/query/data/coupon-trade/ai-analysis` | ✅ |
| src/api/index.js | `/query/data/pinzhi-cashier-ai-analysis` | `/api/query/data/pinzhi-cashier-ai-analysis` | ✅ |
| src/api/index.js | `/query/inquiry/sid` | `/api/query/inquiry/sid` | ✅ |
| src/api/index.js | `/query/inquiry/sid/batch` | `/api/query/inquiry/sid/batch` | ✅ |
| src/api/index.js | `/query/inquiry/sid/{sid}/validate` | `/api/query/inquiry/sid/{sid}/validate` | ✅ |

## 4. PPT报告接口 (ppt_report_router)

### vue-element 前端调用
| 前端文件 | 前端路径 | 后端路径 | 状态 |
|---------|---------|---------|------|
| src/api/index.js | `/ppt-report/generate` | `/api/ppt-report/generate` | ✅ |
| src/api/index.js | `/ppt-report/quota` | `/api/ppt-report/quota` | ❌ 缺失 |
| src/api/index.js | `/ppt-report/download/ppt-reports/{filename}` | `/api/ppt-report/download/ppt-reports/{filename}` | ⚠️ 需确认 |
| src/api/index.js | `/ppt-report/task/{id}/status` | `/api/ppt-report/task/{id}/status` | ✅ |

## 5. 其他接口

### 未实现的接口（前端调用但后端缺失）
| 前端文件 | 前端路径 | 需要的后端路径 | 问题 |
|---------|---------|--------------|------|
| src/api/index.js | `/report/export` | `/api/report/export` | 缺失 |
| src/api/index.js | `/brand/{id}/config` | `/api/brand/{id}/config` | 缺失 |
| src/api/index.js | `/fields/config/{id}` | `/api/fields/config/{id}` | 缺失 |

## 数据库表结构更新

### 主要表结构
1. **member_report_users** - 用户表（包含deleted_at字段）
2. **member_report_sessions** - 会话表
3. **member_report_audit_logs** - 审计日志表
4. **member_report_bid_configs** - BID配置表（包含bid_name、deleted_at等字段）
5. **member_report_user_bid_permissions** - 用户BID权限关联表
6. **member_report_tasks** - PPT任务表（user_id字段存储username）
7. **member_report_task_logs** - 任务日志表

### 关键字段说明
- `member_report_tasks.user_id` - 存储username而非数字ID，便于任务系统使用
- `member_report_bid_configs.bid_name` - BID名称，必填字段
- `member_report_bid_configs.deleted_at` - 软删除时间戳
- `member_report_user_bid_permissions` - 管理用户对BID的访问权限

## 修复记录

### 2025-01-18 修复内容
1. ✅ 整合数据库创建脚本到 `complete_schema.sql`
2. ✅ 修改 `member_report_tasks` 表使用username
3. ✅ 添加缺失的数据库字段（bid_name、deleted_at等）
4. ✅ 修复前端API路径重复问题
5. ✅ 统一环境变量配置
6. ✅ 恢复inquiry路由注册

### 建议的后续优化
1. 实现报表导出接口 (`/api/report/export`)
2. 实现品牌配置接口 (`/api/brand/{id}/config`)
3. 实现字段配置接口 (`/api/fields/config/{id}`)
4. 添加独立的配额查询接口
5. 验证文件下载功能