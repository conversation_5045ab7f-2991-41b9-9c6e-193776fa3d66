"""
BID申请管理模块
处理用户的BID配置申请
"""
import json
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
from fastapi import HTTPException, Depends
import aiomysql

from auth.middleware.auth_middleware import get_current_user

logger = logging.getLogger(__name__)


class BidApplicationService:
    """BID申请服务类"""
    
    def __init__(self, db_pool):
        self.db_pool = db_pool
    
    async def submit_application(
        self,
        application_data: dict,
        current_user: dict
    ) -> dict:
        """
        提交BID申请
        申请会创建一条status='pending'的bid_config记录
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 检查当前用户是否已申请过该BID
                    check_sql = """
                    SELECT bid, status FROM member_report_bid_configs 
                    WHERE bid = %s AND owner_username = %s AND deleted_at IS NULL
                    """
                    await cursor.execute(check_sql, (application_data['bid'], current_user['username']))
                    existing = await cursor.fetchone()
                    
                    if existing:
                        if existing['status'] in ['active', 'approved', 'pending']:
                            raise HTTPException(
                                status_code=400,
                                detail=f"您已申请过BID {application_data['bid']}，当前状态为{existing['status']}"
                            )
                        elif existing['status'] == 'rejected':
                            # 如果是被拒绝的，可以更新重新申请
                            update_sql = """
                            UPDATE member_report_bid_configs
                            SET bid_name = %s,
                                description = %s,
                                business_type = %s,
                                apply_reason = %s,
                                sid_list = %s,
                                contact_name = %s,
                                contact_phone = %s,
                                contact_email = %s,
                                company_name = %s,
                                industry = %s,
                                status = 'pending',
                                reject_reason = NULL,
                                updated_at = NOW()
                            WHERE bid = %s AND owner_username = %s
                            """
                            params = (
                                application_data.get('bid_name'),
                                application_data.get('description'),
                                application_data.get('business_type'),
                                application_data.get('apply_reason'),
                                json.dumps(application_data.get('sid_list', [])),
                                application_data.get('contact_name'),
                                application_data.get('contact_phone'),
                                application_data.get('contact_email'),
                                application_data.get('company_name'),
                                application_data.get('industry'),
                                application_data['bid'],
                                current_user['username']
                            )
                            await cursor.execute(update_sql, params)
                            await conn.commit()
                            
                            return {
                                'success': True,
                                'message': '重新申请提交成功'
                            }
                    
                    # 创建新的申请记录
                    insert_sql = """
                    INSERT INTO member_report_bid_configs (
                        bid, bid_name, description, business_type, owner_username,
                        status, apply_reason, sid_list,
                        contact_name, contact_phone, contact_email,
                        company_name, industry, created_at, updated_at
                    ) VALUES (
                        %s, %s, %s, %s, %s, 'pending', %s, %s,
                        %s, %s, %s, %s, %s, NOW(), NOW()
                    )
                    """
                    
                    params = (
                        application_data['bid'],
                        application_data.get('bid_name'),
                        application_data.get('description'),
                        application_data.get('business_type'),
                        current_user.get('username'),
                        application_data.get('apply_reason'),
                        json.dumps(application_data.get('sid_list', [])),
                        application_data.get('contact_name'),
                        application_data.get('contact_phone'),
                        application_data.get('contact_email'),
                        application_data.get('company_name'),
                        application_data.get('industry')
                    )
                    
                    await cursor.execute(insert_sql, params)
                    await conn.commit()
                    
                    # 记录审计日志
                    audit_sql = """
                    INSERT INTO member_report_audit_logs (
                        user_id, username, action, resource, resource_id,
                        request_data, status, created_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, NOW())
                    """
                    await cursor.execute(audit_sql, (
                        current_user.get('user_id'),
                        current_user.get('username'),
                        'bid_apply',
                        'bid_config',
                        application_data['bid'],
                        json.dumps(application_data),
                        'success'
                    ))
                    await conn.commit()
                    
                    return {
                        'success': True,
                        'message': '申请提交成功，等待审批'
                    }
                    
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"提交BID申请失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def get_user_applications(
        self,
        current_user: dict,
        page: int = 1,
        page_size: int = 10
    ) -> dict:
        """获取用户的BID申请列表"""
        try:
            offset = (page - 1) * page_size
            
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 获取总数
                    count_sql = """
                    SELECT COUNT(*) as total 
                    FROM member_report_bid_configs
                    WHERE owner_username = %s AND deleted_at IS NULL
                    """
                    await cursor.execute(count_sql, (current_user.get('username'),))
                    total = (await cursor.fetchone())['total']
                    
                    # 获取列表
                    list_sql = """
                    SELECT 
                        id, bid, bid_name, description, business_type, status,
                        apply_reason, reject_reason, sid_list,
                        contact_name, contact_phone, contact_email,
                        company_name, industry,
                        approved_by, approved_at,
                        created_at, updated_at
                    FROM member_report_bid_configs
                    WHERE owner_username = %s AND deleted_at IS NULL
                    ORDER BY created_at DESC
                    LIMIT %s OFFSET %s
                    """
                    await cursor.execute(list_sql, (current_user.get('username'), page_size, offset))
                    applications = await cursor.fetchall()
                    
                    # 处理JSON字段
                    for app in applications:
                        if app['sid_list']:
                            app['sid_list'] = json.loads(app['sid_list'])
                        else:
                            app['sid_list'] = []
                        
                        # 格式化时间
                        if app['created_at']:
                            app['created_at'] = app['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                        if app['updated_at']:
                            app['updated_at'] = app['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
                        if app['approved_at']:
                            app['approved_at'] = app['approved_at'].strftime('%Y-%m-%d %H:%M:%S')
                    
                    return {
                        'total': total,
                        'items': applications,
                        'page': page,
                        'page_size': page_size
                    }
                    
        except Exception as e:
            logger.error(f"获取用户BID申请列表失败: {e}")
            return {
                'total': 0,
                'items': [],
                'page': page,
                'page_size': page_size
            }
    
    async def cancel_application(
        self,
        bid: str,
        current_user: dict
    ) -> dict:
        """撤销BID申请"""
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 检查申请是否存在且属于当前用户
                    check_sql = """
                    SELECT status FROM member_report_bid_configs
                    WHERE bid = %s AND owner_username = %s AND deleted_at IS NULL
                    """
                    await cursor.execute(check_sql, (bid, current_user.get('username')))
                    config = await cursor.fetchone()
                    
                    if not config:
                        raise HTTPException(status_code=404, detail="申请不存在")
                    
                    if config['status'] != 'pending':
                        raise HTTPException(status_code=400, detail="只能撤销待审批的申请")
                    
                    # 软删除申请
                    delete_sql = """
                    UPDATE member_report_bid_configs
                    SET deleted_at = NOW(), updated_at = NOW()
                    WHERE bid = %s AND owner_username = %s
                    """
                    await cursor.execute(delete_sql, (bid, current_user.get('username')))
                    await conn.commit()
                    
                    return {
                        'success': True,
                        'message': '申请已撤销'
                    }
                    
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"撤销BID申请失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def update_application(
        self,
        bid: str,
        update_data: dict,
        current_user: dict
    ) -> dict:
        """修改BID申请（仅限pending状态）"""
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 检查申请状态
                    check_sql = """
                    SELECT status FROM member_report_bid_configs
                    WHERE bid = %s AND owner_username = %s AND deleted_at IS NULL
                    """
                    await cursor.execute(check_sql, (bid, current_user.get('username')))
                    config = await cursor.fetchone()
                    
                    if not config:
                        raise HTTPException(status_code=404, detail="申请不存在")
                    
                    if config['status'] not in ['pending', 'rejected']:
                        raise HTTPException(
                            status_code=400, 
                            detail="只能修改待审批或已拒绝的申请"
                        )
                    
                    # 更新申请
                    update_sql = """
                    UPDATE member_report_bid_configs
                    SET bid_name = %s,
                        description = %s,
                        apply_reason = %s,
                        sid_list = %s,
                        contact_name = %s,
                        contact_phone = %s,
                        contact_email = %s,
                        company_name = %s,
                        industry = %s,
                        status = 'pending',
                        updated_at = NOW()
                    WHERE bid = %s AND owner_username = %s
                    """
                    
                    params = (
                        update_data.get('bid_name'),
                        update_data.get('description'),
                        update_data.get('apply_reason'),
                        json.dumps(update_data.get('sid_list', [])),
                        update_data.get('contact_name'),
                        update_data.get('contact_phone'),
                        update_data.get('contact_email'),
                        update_data.get('company_name'),
                        update_data.get('industry'),
                        bid,
                        current_user.get('username')
                    )
                    
                    await cursor.execute(update_sql, params)
                    await conn.commit()
                    
                    return {
                        'success': True,
                        'message': '申请已更新'
                    }
                    
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"更新BID申请失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))