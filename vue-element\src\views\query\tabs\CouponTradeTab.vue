<template>
  <div class="coupon-trade-tab">
    <!-- 数据表格 -->
    <el-card class="data-table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h4>券交易数据</h4>
          <div class="table-actions">
            <el-button type="primary" size="small" @click="exportData">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
            <el-button type="success" size="small" @click="showAIAnalysis">
              <el-icon><ChatDotSquare /></el-icon>
              AI分析
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="tableData" stripe v-loading="loading" border class="coupon-table" :fit="true">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="couponName" label="券名称" min-width="180" align="center" />
        <el-table-column prop="couponId" label="券编号ID" min-width="120" align="center" />
        <el-table-column prop="couponSendCount" label="券发放量(张)" min-width="120" align="center">
          <template #default="scope">
            <span>{{ formatValue(scope.row.couponSendCount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="couponUsedCount" label="券使用量(张)" min-width="120" align="center">
          <template #default="scope">
            <span>{{ formatValue(scope.row.couponUsedCount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="couponUsageRate" label="券使用率(%)" min-width="120" align="center">
          <template #default="scope">
            <el-progress 
              :percentage="scope.row.couponUsageRate" 
              :color="getUsageRateColor(scope.row.couponUsageRate)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="couponDiscountAmount" label="券抵扣金额(元)" min-width="140" align="center">
          <template #default="scope">
            <span>{{ formatValue(scope.row.couponDiscountAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="drivePrepayAmount" label="带动储值消费(元)" min-width="140" align="center">
          <template #default="scope">
            <span>{{ formatValue(scope.row.drivePrepayAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="driveCashAmount" label="带动现金消费(元)" min-width="140" align="center">
          <template #default="scope">
            <span>{{ formatValue(scope.row.driveCashAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="driveTotalAmount" label="带动总交易金额(元)" min-width="160" align="center">
          <template #default="scope">
            <span>{{ formatValue(scope.row.driveTotalAmount) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 图表展示 -->
    <el-row :gutter="20" class="chart-section">
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="card-header">
              <h4>券使用率分析</h4>
              <el-button type="text" size="small" @click="downloadChart('usage')">
                <el-icon><Download /></el-icon>
                下载图表
              </el-button>
            </div>
          </template>
          <div ref="usageChart" class="chart-container"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="card-header">
              <h4>券价值分析</h4>
              <el-button type="text" size="small" @click="downloadChart('value')">
                <el-icon><Download /></el-icon>
                下载图表
              </el-button>
            </div>
          </template>
          <div ref="valueChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 汇总统计 -->
    <el-card class="summary-card" shadow="never">
      <template #header>
        <h4>汇总统计</h4>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="券发放总量" :value="summaryData.totalSendCount" suffix="张" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="券使用总量" :value="summaryData.totalUsedCount" suffix="张" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="平均使用率" :value="summaryData.avgUsageRate" suffix="%" :precision="1" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="带动总金额" :value="summaryData.totalDriveAmount" suffix="元" />
        </el-col>
      </el-row>
    </el-card>

    <!-- AI分析结果 -->
    <el-card v-if="showAIResult" class="ai-analysis-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h4>
            <el-icon><ChatDotSquare /></el-icon>
            AI智能分析
          </h4>
          <el-button type="text" size="small" @click="showAIResult = false">
            <el-icon><Close /></el-icon>
            关闭
          </el-button>
        </div>
      </template>
      <div class="ai-analysis-content">
        <el-alert title="分析结果" type="info" :closable="false">
          <div class="analysis-text">
            {{ aiAnalysisResult }}
          </div>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, ChatDotSquare, Close } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { memberReportApi } from '@/api/index'

export default {
  name: 'CouponTradeTab',
  props: {
    queryParams: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const usageChart = ref(null)
    const valueChart = ref(null)
    const showAIResult = ref(false)
    const aiAnalysisResult = ref('')
    const loading = ref(false)
    const tableData = ref([])
    
    // 获取券交易数据
    const fetchCouponTradeData = async () => {
      if (!props.queryParams || !props.queryParams.bid) {
        return
      }
      
      loading.value = true
      try {
                  const response = await memberReportApi.getCouponTradeData(props.queryParams)
        if (response.code === 200) {
          tableData.value = response.data || []
          
          // 数据更新后重新初始化图表
          nextTick(() => {
            setTimeout(() => {
              initUsageChart()
              initValueChart()
            }, 300)
          })
        } else {
          ElMessage.error(response.message || '获取券交易数据失败')
          tableData.value = []
        }
      } catch (error) {
        console.error('获取券交易数据失败:', error)
        ElMessage.error('获取券交易数据失败')
        tableData.value = []
      } finally {
        loading.value = false
      }
    }
    
    // 监听查询参数变化
    watch(
      () => props.queryParams,
      (newParams) => {
        if (newParams && newParams.bid) {
          fetchCouponTradeData()
        }
      },
      { deep: true, immediate: true }
    )
    
    // 汇总数据
    const summaryData = computed(() => {
      if (!tableData.value || tableData.value.length === 0) {
        return {
          totalSendCount: 0,
          totalUsedCount: 0,
          avgUsageRate: 0,
          totalDriveAmount: 0
        }
      }
      
      const totalSendCount = tableData.value.reduce((sum, item) => sum + (item.couponSendCount || 0), 0)
      const totalUsedCount = tableData.value.reduce((sum, item) => sum + (item.couponUsedCount || 0), 0)
      const avgUsageRate = totalSendCount > 0 ? (totalUsedCount / totalSendCount * 100) : 0
      const totalDriveAmount = tableData.value.reduce((sum, item) => sum + (item.driveTotalAmount || 0), 0)
      
      return {
        totalSendCount,
        totalUsedCount,
        avgUsageRate,
        totalDriveAmount
      }
    })
    
    // 格式化数值
    const formatValue = (value) => {
      if (typeof value === 'number') {
        return value.toLocaleString()
      }
      return value
    }
    
    // 获取使用率颜色
    const getUsageRateColor = (rate) => {
      if (rate >= 70) return '#52c41a'
      if (rate >= 50) return '#faad14'
      return '#ff4d4f'
    }
    
    // 初始化使用率图表
    const initUsageChart = () => {
      if (!usageChart.value || !tableData.value || tableData.value.length === 0) return
      
      // 确保容器有宽度
      if (usageChart.value.offsetWidth === 0) {
        setTimeout(initUsageChart, 100)
        return
      }
      
      const chart = echarts.init(usageChart.value, null, {
        renderer: 'canvas',
        useDirtyRect: false
      })
      
      // 限制数据量，最多显示5个券
      const displayData = tableData.value.slice(0, 5)
      const couponNames = displayData.map(item => item.couponName)
      const usageRates = displayData.map(item => item.couponUsageRate)
      
      const option = {
        title: {
          text: '券使用率分析',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold',
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            return params[0].name + '<br/>' + params[0].seriesName + ': ' + params[0].value + '%'
          }
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '20%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: (tableData.value && tableData.value.length > 0) ? (tableData.value.map(item => item.couponName) || ['上期', '本期']) : ['上期', '本期'],
          axisLabel: {
            interval: 0,
            rotate: 30,
            fontSize: 12,
            color: '#333',
            formatter: function(value) {
              if (value.length > 8) {
                return value.substring(0, 8) + '...'
              }
              return value
            }
          },
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '使用率(%)',
          min: 0,
          max: 100,
          axisLabel: {
            formatter: '{value}%',
            color: '#333'
          },
          nameTextStyle: {
            fontSize: 12,
            color: '#333'
          },
          splitLine: {
            lineStyle: {
              type: 'dashed',
              color: '#eee'
            }
          }
        },
        series: [
          {
            name: '使用率',
            type: 'bar',
            data: usageRates.map((rate, index) => ({
              value: rate,
              itemStyle: {
                color: getUsageRateColor(rate),
                borderRadius: [4, 4, 0, 0]
              }
            })),
            barWidth: '50%',
            label: {
              show: true,
              position: 'top',
              formatter: '{c}%',
              fontSize: 14,
              fontWeight: 'bold',
              color: '#333'
            }
          }
        ]
      }
      chart.setOption(option)
      
      // 强制重绘
      chart.resize()
    }
    
    // 初始化价值图表
    const initValueChart = () => {
      if (!valueChart.value || !tableData.value || tableData.value.length === 0) return
      
      // 确保容器有宽度
      if (valueChart.value.offsetWidth === 0) {
        setTimeout(initValueChart, 100)
        return
      }
      
      const chart = echarts.init(valueChart.value, null, {
        renderer: 'canvas',
        useDirtyRect: false
      })
      
      // 限制数据量，最多显示5个券
      const displayData = tableData.value.slice(0, 5)
      const couponNames = displayData.map(item => item.couponName)
      const discountData = displayData.map(item => item.couponDiscountAmount)
      const driveData = displayData.map(item => item.driveTotalAmount)
      
      const option = {
        title: {
          text: '券价值分析',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold',
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            let result = params[0].name + '<br/>'
            params.forEach(param => {
              result += param.seriesName + ': ' + param.value.toLocaleString() + '元<br/>'
            })
            if (params.length >= 2) {
              const roi = ((params[1].value / params[0].value) - 1) * 100
              result += '<br/>ROI: ' + roi.toFixed(1) + '%'
            }
            return result
          }
        },
        legend: {
          data: ['抵扣金额', '带动金额'],
          bottom: 10,
          textStyle: {
            fontSize: 12
          }
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '20%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: (tableData.value && tableData.value.length > 0) ? (tableData.value.map(item => item.couponName) || ['上期', '本期']) : ['上期', '本期'],
          axisLabel: {
            interval: 0,
            rotate: 30,
            fontSize: 12,
            color: '#333',
            formatter: function(value) {
              if (value.length > 8) {
                return value.substring(0, 8) + '...'
              }
              return value
            }
          },
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '金额（元）',
          axisLabel: {
            formatter: function(value) {
              return (value / 1000).toFixed(0) + 'K'
            },
            color: '#333'
          },
          nameTextStyle: {
            fontSize: 12,
            color: '#333'
          },
          splitLine: {
            lineStyle: {
              type: 'dashed',
              color: '#eee'
            }
          }
        },
        series: [
          {
            name: '抵扣金额',
            type: 'bar',
            stack: 'total',
            data: discountData.map(value => ({
              value: value,
              itemStyle: {
                color: '#1890ff',
                borderRadius: [4, 4, 0, 0]
              }
            })),
            barWidth: '50%'
          },
          {
            name: '带动金额',
            type: 'bar',
            stack: 'total',
            data: driveData.map((value, index) => ({
              value: value - discountData[index],
              itemStyle: {
                color: '#52c41a',
                borderRadius: [4, 4, 0, 0]
              }
            })),
            label: {
              show: true,
              position: 'top',
              formatter: function(params) {
                const totalValue = params.value + discountData[params.dataIndex]
                return (totalValue / 1000).toFixed(1) + 'K'
              },
              fontSize: 12,
              fontWeight: 'bold',
              color: '#333'
            }
          }
        ]
      }
      chart.setOption(option)
      
      // 强制重绘
      chart.resize()
    }
    
    // 处理窗口大小变化
    const handleResize = () => {
      // 重新初始化图表
      initUsageChart()
      initValueChart()
      initTrendChart()
      
      // 如果仍有性能问题，可以恢复为仅调整大小
      /*
      if (usageChart.value) {
        const chart = echarts.getInstanceByDom(usageChart.value)
        if (chart) chart.resize()
      }
      
      if (valueChart.value) {
        const chart = echarts.getInstanceByDom(valueChart.value)
        if (chart) chart.resize()
      }
      */
    }
    
    // 导出数据
    const exportData = () => {
      ElMessage.success('数据导出功能开发中...')
    }
    
    // 显示AI分析
    const showAIAnalysis = async () => {
      if (!props.queryParams || !props.queryParams.bid) {
        ElMessage.warning('请先设置查询条件')
        return
      }
      
      showAIResult.value = true
      aiAnalysisResult.value = '正在分析中，请稍候...'
      
      try {
        const response = await memberReportApi.getCouponTradeAIAnalysis(props.queryParams)
        if (response.code === 200) {
          aiAnalysisResult.value = response.data
        } else {
          aiAnalysisResult.value = response.message || 'AI分析失败'
        }
      } catch (error) {
        console.error('获取AI分析失败:', error)
        aiAnalysisResult.value = '获取AI分析失败，请稍后再试'
      }
    }
    
    // 下载图表
    const downloadChart = (type) => {
      ElMessage.success(`${type}图表下载功能开发中...`)
    }
    
    // 初始化图表
    onMounted(() => {
      nextTick(() => {
        // 监听窗口大小变化
        window.addEventListener('resize', handleResize)
      })
    })
    
    // 在组件销毁时清理
    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)
      
      if (usageChart.value) {
        const chart = echarts.getInstanceByDom(usageChart.value)
        if (chart) chart.dispose()
      }
      
      if (valueChart.value) {
        const chart = echarts.getInstanceByDom(valueChart.value)
        if (chart) chart.dispose()
      }
    })
    
    return {
      usageChart,
      valueChart,
      showAIResult,
      aiAnalysisResult,
      tableData,
      summaryData,
      loading,
      formatValue,
      getUsageRateColor,
      exportData,
      showAIAnalysis,
      downloadChart,
      fetchCouponTradeData,
      Download,
      ChatDotSquare,
      Close
    }
  }
}
</script>

<style scoped>
.coupon-trade-tab {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header h4 {
  margin: 0;
  color: #303133;
  font-weight: 600;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.data-table-card {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
}

.member-table {
  border-radius: 4px;
  overflow: hidden;
}

.member-table :deep(.el-table__header-wrapper) {
  background: #fafafa;
}

.member-table :deep(.el-table__body-wrapper) {
  background: #fff;
}

.member-table :deep(.el-table__row) {
  background: #fff;
}

.member-table :deep(.el-table__row.el-table__row--striped) {
  background: #fafafa;
}

.member-table :deep(.el-table__cell) {
  border-color: #e6e6e6;
  padding: 12px 8px;
}

.member-table :deep(.el-table__header .el-table__cell) {
  background: #f5f7fa;
  font-weight: 600;
  color: #303133;
}

.total-amount {
  font-weight: bold;
  color: #52c41a;
}

.field-name {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: 500;
}

.coupon-table :deep(th.el-table__cell) {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 600;
  text-align: center;
}

.coupon-table :deep(td.el-table__cell) {
  text-align: center;
}

.no-data {
  color: #999;
  font-style: italic;
  font-size: 12px;
  padding: 4px 8px;
  background: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #e6e6e6;
  display: inline-block;
}

.chart-section {
  margin-bottom: 20px;
}

.chart-card {
  height: 450px;
  margin-bottom: 20px;
}

.chart-container {
  width: 100%;
  height: 380px;
}

.summary-card {
  margin-bottom: 20px;
}

.summary-card :deep(.el-statistic__number) {
  color: #1890ff;
}

.ai-analysis-card {
  margin-top: 20px;
}

.ai-analysis-content {
  padding: 20px;
}

.analysis-text {
  white-space: pre-line;
  line-height: 1.6;
  color: #666;
}

.usage-rate-progress :deep(.el-progress-bar__outer) {
  border-radius: 0;
  background-color: #e6f7ff; /* 浅蓝色背景 */
}

.usage-rate-progress :deep(.el-progress-bar__inner) {
  border-radius: 0;
  background-color: #1890ff; /* 深蓝色进度条 */
  border: none; /* 移除边框 */
  box-shadow: none; /* 移除阴影 */
}

.usage-rate-progress :deep(.el-progress-bar__innerText) {
  font-weight: bold; /* 加粗字体 */
  color: #000; /* 黑色字体 */
  font-size: 14px; /* 字体大小 */
}
</style> 