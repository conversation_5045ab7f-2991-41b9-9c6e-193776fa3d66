// PPT模板示例数据
export const pptTemplates = [
  {
    id: 'template1',
    name: '会员分析报告模板',
    thumbnail: 'https://via.placeholder.com/200x150?text=会员分析报告',
    description: '包含会员基础数据、消费数据和充值数据的综合分析报告'
  },
  {
    id: 'template2',
    name: '会员消费分析模板',
    thumbnail: 'https://via.placeholder.com/200x150?text=会员消费分析',
    description: '专注于会员消费行为和模式的深度分析'
  },
  {
    id: 'template3',
    name: '会员充值分析模板',
    thumbnail: 'https://via.placeholder.com/200x150?text=会员充值分析',
    description: '专注于会员充值行为和储值留存的分析报告'
  }
];

// 数据模块选项
export const dataModules = [
  {
    id: 'memberBase',
    name: '会员基础信息',
    description: '包含会员总数、新增会员、取关会员等基础数据'
  },
  {
    id: 'memberConsume',
    name: '会员消费信息',
    description: '包含消费金额、消费人数、消费频次等数据'
  },
  {
    id: 'memberCharge',
    name: '会员充值信息',
    description: '包含充值金额、充值笔数、储值留存率等数据'
  },
  {
    id: 'couponTrade',
    name: '券交易信息',
    description: '包含券使用情况、带动消费等数据'
  }
];

// PPT生成状态示例
export const generationStatus = {
  idle: '准备就绪',
  generating: '正在生成PPT...',
  success: 'PPT生成成功',
  failed: 'PPT生成失败'
};

// PPT预览示例
export const previewData = {
  template1: {
    slides: [
      'https://via.placeholder.com/640x360?text=封面:会员分析报告',
      'https://via.placeholder.com/640x360?text=会员基础数据概览',
      'https://via.placeholder.com/640x360?text=会员消费数据分析',
      'https://via.placeholder.com/640x360?text=会员充值数据分析'
    ]
  },
  template2: {
    slides: [
      'https://via.placeholder.com/640x360?text=封面:会员消费分析',
      'https://via.placeholder.com/640x360?text=消费金额趋势',
      'https://via.placeholder.com/640x360?text=消费人数分析',
      'https://via.placeholder.com/640x360?text=消费方式占比'
    ]
  },
  template3: {
    slides: [
      'https://via.placeholder.com/640x360?text=封面:会员充值分析',
      'https://via.placeholder.com/640x360?text=充值金额趋势',
      'https://via.placeholder.com/640x360?text=充值笔数分析',
      'https://via.placeholder.com/640x360?text=储值留存率分析'
    ]
  }
};

// 示例下载链接（修正URL格式）
export const downloadLinks = {
  template1: '/api/ppt-report/download/ppt-reports/sample-member-analysis.pptx',
  template2: '/api/ppt-report/download/ppt-reports/sample-consume-analysis.pptx',
  template3: '/api/ppt-report/download/ppt-reports/sample-charge-analysis.pptx'
};

// 示例API响应（修正URL格式）
export const sampleApiResponse = {
  success: true,
  message: 'PPT生成成功',
  data: {
    downloadUrl: '/api/ppt-report/download/ppt-reports/generated-report-123456.pptx',
    previewUrl: 'https://via.placeholder.com/640x360?text=生成的PPT预览',
    generatedAt: '2023-07-15T10:30:45',
    slideCount: 12,
    fileSize: '2.5MB',
    fileName: 'generated-report-123456.pptx',
    objectName: 'generated-report-123456.pptx'
  }
};

export default {
  pptTemplates,
  dataModules,
  generationStatus,
  previewData,
  downloadLinks,
  sampleApiResponse
};