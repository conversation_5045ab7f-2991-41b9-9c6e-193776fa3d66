<template>
  <div class="data-indicator-table">
    <!-- 表格容器 -->
    <el-table
      :data="formattedData"
      :loading="loading"
      stripe
      border
      class="indicator-table"
      :header-cell-style="headerCellStyle"
      :cell-style="cellStyle"
      :max-height="maxHeight"
    >
      <!-- 字段名列 - 固定左侧 -->
      <el-table-column
        prop="fieldName"
        label="字段名"
        width="200"
        fixed="left"
        align="center"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div class="field-name">
            <el-icon :class="getFieldIconClass(row.category)">
              <component :is="getFieldIcon(row.category)" />
            </el-icon>
            <span class="field-text">{{ row.fieldName }}</span>
          </div>
        </template>
      </el-table-column>

      <!-- 真实值列 -->
      <el-table-column
        prop="currentValue"
        label="真实值"
        width="150"
        align="center"
      >
        <template #default="{ row }">
          <div class="current-value">
            {{ formatValue(row.currentValue, row.fieldName) }}
          </div>
        </template>
      </el-table-column>

      <!-- 动态环比变化列 - 根据reportType显示不同列数 -->
      <template v-if="reportType === 'week' || reportType === 'month'">
        <!-- 7列格式：3个环比变化列 -->
        <el-table-column label="环比变化" align="center">
          <el-table-column
            prop="comparison1"
            :label="getComparisonLabel(1)"
            width="120"
            align="center"
          >
            <template #default="{ row }">
              <div class="comparison-cell">
                <el-tag
                  :type="getComparisonType(row.comparison1)"
                  size="small"
                  effect="light"
                >
                  {{ row.comparison1 || '-' }}
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="comparison2"
            :label="getComparisonLabel(2)"
            width="120"
            align="center"
          >
            <template #default="{ row }">
              <div class="comparison-cell">
                <el-tag
                  :type="getComparisonType(row.comparison2)"
                  size="small"
                  effect="light"
                >
                  {{ row.comparison2 || '-' }}
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="comparison3"
            :label="getComparisonLabel(3)"
            width="120"
            align="center"
          >
            <template #default="{ row }">
              <div class="comparison-cell">
                <el-tag
                  :type="getComparisonType(row.comparison3)"
                  size="small"
                  effect="light"
                >
                  {{ row.comparison3 || '-' }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
        </el-table-column>
      </template>

      <template v-else>
        <!-- 5列格式：1个环比变化列 -->
        <el-table-column
          prop="comparison1"
          label="环比变化"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <div class="comparison-cell">
              <el-tag
                :type="getComparisonType(row.comparison1)"
                size="small"
                effect="light"
              >
                {{ row.comparison1 || '-' }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
      </template>

      <!-- 同期变化列 -->
      <el-table-column
        prop="yearOverYear"
        label="同期变化"
        width="120"
        align="center"
      >
        <template #default="{ row }">
          <div class="year-over-year">
            <el-tag
              :type="getComparisonType(row.yearOverYear)"
              size="small"
              effect="dark"
            >
              <el-icon><TrendCharts /></el-icon>
              {{ row.yearOverYear || '-' }}
            </el-tag>
          </div>
        </template>
      </el-table-column>

      <!-- 字段来源/计算方法列 -->
      <el-table-column
        prop="source"
        label="字段来源/计算方法"
        min-width="250"
        align="center"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div class="source-info">
            <el-icon class="source-icon"><Document /></el-icon>
            <span class="source-text">{{ row.source || '暂无说明' }}</span>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 表格底部信息 -->
    <div class="table-footer">
      <div class="stats-info">
        <el-icon><InfoFilled /></el-icon>
        <span>共 {{ formattedData.length }} 个{{ categoryName }}指标</span>
        <el-tag v-if="reportType" size="small" type="info" class="report-type-tag">
          {{ getReportTypeName() }}
        </el-tag>
      </div>
      <div class="update-time">
        <el-icon><Clock /></el-icon>
        <span>数据更新时间：{{ updateTime }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import {
  User,
  Money,
  Promotion,
  DataAnalysis,
  ShoppingCart,
  TrendCharts,
  Document,
  InfoFilled,
  Clock
} from '@element-plus/icons-vue'

// Props定义
const props = defineProps({
  // 数据源
  data: {
    type: Array,
    default: () => []
  },
  // 报表类型：week/month/quarter/halfyear
  reportType: {
    type: String,
    default: 'week',
    validator: (value) => ['week', 'month', 'quarter', 'halfyear'].includes(value)
  },
  // 数据类别名称
  categoryName: {
    type: String,
    default: '数据'
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  },
  // 表格最大高度
  maxHeight: {
    type: [String, Number],
    default: '600px'
  }
})

// 响应式数据
const updateTime = ref(new Date().toLocaleString())

// 计算属性
const formattedData = computed(() => {
  return props.data.map(item => ({
    ...item,
    // 确保每个数据项都有category字段，用于图标显示
    category: item.category || getCategoryFromFieldName(item.fieldName)
  }))
})

// 表格样式配置
const headerCellStyle = {
  background: '#f8f9fa',
  color: '#606266',
  fontWeight: '600',
  fontSize: '14px',
  textAlign: 'center'
}

const cellStyle = {
  fontSize: '13px',
  textAlign: 'center'
}

// 方法定义
// 根据字段名推断类别
const getCategoryFromFieldName = (fieldName) => {
  if (fieldName.includes('会员') || fieldName.includes('用户')) return 'member'
  if (fieldName.includes('金额') || fieldName.includes('营业额') || fieldName.includes('实收')) return 'revenue'
  if (fieldName.includes('券') || fieldName.includes('活动') || fieldName.includes('营销')) return 'marketing'
  if (fieldName.includes('消费') || fieldName.includes('购买')) return 'consumption'
  return 'analysis'
}

// 获取字段图标
const getFieldIcon = (category) => {
  const iconMap = {
    member: User,
    revenue: Money,
    marketing: Promotion,
    consumption: ShoppingCart,
    analysis: DataAnalysis
  }
  return iconMap[category] || DataAnalysis
}

// 获取字段图标样式类
const getFieldIconClass = (category) => {
  const classMap = {
    member: 'field-icon-member',
    revenue: 'field-icon-revenue',
    marketing: 'field-icon-marketing',
    consumption: 'field-icon-consumption',
    analysis: 'field-icon-analysis'
  }
  return `field-icon ${classMap[category] || 'field-icon-analysis'}`
}

// 获取环比标签文本
const getComparisonLabel = (index) => {
  const labelMap = {
    week: [`前1周`, `前2周`, `前3周`],
    month: [`前1月`, `前2月`, `前3月`],
    quarter: [`前1季`, `前2季`, `前3季`],
    halfyear: [`前半年`, `前1年`, `前1.5年`]
  }
  return labelMap[props.reportType]?.[index - 1] || `对比${index}`
}

// 获取报表类型名称
const getReportTypeName = () => {
  const nameMap = {
    week: '周报(7列)',
    month: '月报(7列)',
    quarter: '季报(5列)',
    halfyear: '半年报(5列)'
  }
  return nameMap[props.reportType] || '报表'
}

// 格式化数值显示
const formatValue = (value, fieldName) => {
  if (value === null || value === undefined || value === '') return '-'
  
  // 根据字段名判断格式化方式
  if (fieldName.includes('金额') || fieldName.includes('实收') || fieldName.includes('营业额')) {
    return `¥${Number(value).toLocaleString()}`
  } else if (fieldName.includes('率') || fieldName.includes('比')) {
    return `${value}%`
  } else if (fieldName.includes('张') && fieldName.includes('券')) {
    return `${Number(value).toLocaleString()}张`
  } else if (typeof value === 'number') {
    return Number(value).toLocaleString()
  }
  return value
}

// 获取对比标签类型
const getComparisonType = (value) => {
  if (!value || value === '-') return 'info'
  
  const numValue = parseFloat(value.replace(/[+\-%]/g, ''))
  if (value.startsWith('+')) {
    return numValue > 10 ? 'success' : 'primary'
  } else if (value.startsWith('-')) {
    return numValue > 10 ? 'danger' : 'warning'
  }
  return 'info'
}

// 监听数据变化，更新时间
watch(() => props.data, () => {
  updateTime.value = new Date().toLocaleString()
}, { deep: true })
</script>

<style scoped>
.data-indicator-table {
  width: 100%;
}

.indicator-table {
  border-radius: 8px;
  overflow: hidden;
}

/* 字段名样式 */
.field-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  justify-content: center;
}

.field-icon {
  font-size: 16px;
}

.field-icon-member { color: #409eff; }
.field-icon-revenue { color: #f093fb; }
.field-icon-marketing { color: #43e97b; }
.field-icon-consumption { color: #4facfe; }
.field-icon-analysis { color: #667eea; }

.field-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 数值样式 */
.current-value {
  font-weight: 600;
  color: #303133;
  text-align: center;
}

/* 对比单元格样式 */
.comparison-cell,
.year-over-year {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 来源信息样式 */
.source-info {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #606266;
  justify-content: center;
  font-size: 12px;
}

.source-icon {
  color: #909399;
  font-size: 14px;
  flex-shrink: 0;
}

.source-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 表格底部样式 */
.table-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  font-size: 13px;
  color: #606266;
}

.stats-info,
.update-time {
  display: flex;
  align-items: center;
  gap: 6px;
}

.report-type-tag {
  margin-left: 8px;
}

/* Element Plus 样式覆盖 */
:deep(.el-table th) {
  padding: 12px 0;
}

:deep(.el-table td) {
  padding: 10px 0;
}

:deep(.el-tag) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-table__fixed-right) {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
}

:deep(.el-table__fixed-left) {
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-footer {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .field-name {
    font-size: 12px;
  }

  .current-value {
    font-size: 12px;
  }
}
</style>
