<template>
  <el-card class="history-card" shadow="never">
    <template #header>
      <div class="card-header">
        <h3>
          <el-icon><Clock /></el-icon>
          历史生成记录
        </h3>
        <el-button type="primary" size="small" @click="fetchHistory">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </template>

    <el-table 
      :data="historyList" 
      v-loading="historyLoading"
      stripe
      style="width: 100%"
    >
      <el-table-column prop="bid" label="品牌ID" width="100" />
      <el-table-column prop="sid" label="门店ID" width="100" />
      <el-table-column prop="task_start_ftime" label="开始日期" width="110" />
      <el-table-column prop="task_end_ftime" label="结束日期" width="110" />
      <el-table-column prop="status_text" label="状态" width="90">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)" size="small">
            {{ row.status_text }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间" width="160" />
      <el-table-column prop="completed_at" label="完成时间" width="160" />
      <el-table-column prop="duration" label="耗时" width="100" />
      <el-table-column prop="error_info" label="错误信息" min-width="150" show-overflow-tooltip />
      <el-table-column label="操作" fixed="right" width="280">
        <template #default="{ row }">
          <el-button
            v-if="row.ppt_oss_url"
            type="primary"
            size="small"
            @click="downloadHistoryPPT(row)"
          >
            下载PPT
          </el-button>
          <el-button
            v-if="row.excel_oss_url"
            type="success"
            size="small"
            @click="downloadHistoryExcel(row)"
          >
            下载Excel
          </el-button>
          <!-- 历史记录的备用下载 -->
          <el-dropdown
            v-if="row.ppt_oss_url || row.excel_oss_url"
            @command="(command) => handleHistoryBackupDownload(command, row)"
            style="margin-left: 5px"
          >
            <el-button type="warning" size="small">
              备用 <el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="ppt" v-if="row.ppt_oss_url">
                  备用PPT下载
                </el-dropdown-item>
                <el-dropdown-item command="excel" v-if="row.excel_oss_url">
                  备用Excel下载
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-if="historyTotal > 0"
      v-model:current-page="historyPage"
      v-model:page-size="historyPageSize"
      :page-sizes="[10, 20, 50]"
      :total="historyTotal"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      style="margin-top: 20px"
    />
  </el-card>
</template>

<script>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Clock, Refresh, ArrowDown } from '@element-plus/icons-vue'
import { pptReportApi } from '@/api'

export default {
  name: 'HistorySection',
  
  components: {
    Clock,
    Refresh,
    ArrowDown
  },
  
  setup() {
    // 历史记录相关
    const historyList = ref([])
    const historyLoading = ref(false)
    const historyTotal = ref(0)
    const historyPage = ref(1)
    const historyPageSize = ref(10)
    
    // 获取历史记录
    const fetchHistory = async () => {
      historyLoading.value = true
      try {
        const skip = (historyPage.value - 1) * historyPageSize.value
        const response = await pptReportApi.getHistory({
          skip,
          limit: historyPageSize.value
        })
        
        if (response.code === 200) {
          historyList.value = response.data.list || []
          historyTotal.value = response.data.total || 0
        } else {
          ElMessage.error(response.message || '获取历史记录失败')
        }
      } catch (error) {
        console.error('获取历史记录失败:', error)
        ElMessage.error('获取历史记录失败')
      } finally {
        historyLoading.value = false
      }
    }
    
    // 获取状态标签类型
    const getStatusType = (status) => {
      const typeMap = {
        'pending': 'info',
        'running': 'warning',
        'completed': 'success',
        'failed': 'danger',
        'cancelled': 'info'
      }
      return typeMap[status] || 'info'
    }
    
    // 下载历史PPT
    const downloadHistoryPPT = (row) => {
      if (!row.ppt_oss_url) {
        ElMessage.error('PPT下载链接不可用')
        return
      }
      
      // 直接使用OSS链接下载
      window.open(row.ppt_oss_url, '_blank')
      ElMessage.success('开始下载PPT文件')
    }
    
    // 下载历史Excel
    const downloadHistoryExcel = (row) => {
      if (!row.excel_oss_url) {
        ElMessage.error('Excel下载链接不可用')
        return
      }
      
      // 直接使用OSS链接下载
      window.open(row.excel_oss_url, '_blank')
      ElMessage.success('开始下载Excel文件')
    }
    
    // 分页大小改变
    const handleSizeChange = (val) => {
      historyPageSize.value = val
      fetchHistory()
    }
    
    // 当前页改变
    const handleCurrentChange = (val) => {
      historyPage.value = val
      fetchHistory()
    }
    
    // 处理历史记录的备用下载
    const handleHistoryBackupDownload = (command, row) => {
      if (!row) {
        ElMessage.error('没有可用的文件信息')
        return
      }
      
      // 构建备用下载路径
      // 文件存储在 member-api/uploads/ppt-reports/bid_task_id/
      const bid = row.bid || 'unknown'
      const taskId = row.task_id || 'unknown'  // task_id 是 task_uuid 的别名
      const folderName = `${bid}_${taskId}`
      
      if (command === 'ppt') {
        // 从ppt_oss_url提取文件名，或使用默认名称
        let fileName = 'report.pptx'
        if (row.ppt_oss_url) {
          const urlParts = row.ppt_oss_url.split('/')
          fileName = urlParts[urlParts.length - 1] || 'report.pptx'
        }
        
        const backupUrl = `/api/ppt-report/download/ppt-reports/${folderName}/${fileName}`
        
        ElMessage.info('正在使用备用链接下载PPT...')
        console.log('历史记录备用PPT下载路径:', backupUrl)
        
        // 添加token到URL参数中
        const token = localStorage.getItem('access_token')
        const urlWithToken = token ? `${backupUrl}?token=${encodeURIComponent(token)}` : backupUrl
        
        // 直接打开下载链接
        window.open(urlWithToken, '_blank')
        
      } else if (command === 'excel') {
        // 从excel_oss_url提取文件名，或使用默认名称
        let excelFileName = 'data.xlsx'
        if (row.excel_oss_url) {
          const urlParts = row.excel_oss_url.split('/')
          excelFileName = urlParts[urlParts.length - 1] || 'data.xlsx'
        } else if (row.ppt_oss_url) {
          // 如果没有excel_oss_url，尝试从PPT文件名推导
          const urlParts = row.ppt_oss_url.split('/')
          const pptName = urlParts[urlParts.length - 1] || 'report.pptx'
          excelFileName = pptName.replace('.pptx', '.xlsx')
        }
        
        const backupUrl = `/api/ppt-report/download/ppt-reports/${folderName}/${excelFileName}`
        
        ElMessage.info('正在使用备用链接下载Excel...')
        console.log('历史记录备用Excel下载路径:', backupUrl)
        
        // 添加token到URL参数中
        const token = localStorage.getItem('access_token')
        const urlWithToken = token ? `${backupUrl}?token=${encodeURIComponent(token)}` : backupUrl
        
        // 直接打开下载链接
        window.open(urlWithToken, '_blank')
      }
    }
    
    // 页面加载时获取历史记录
    onMounted(() => {
      fetchHistory()
    })
    
    return {
      historyList,
      historyLoading,
      historyTotal,
      historyPage,
      historyPageSize,
      fetchHistory,
      getStatusType,
      downloadHistoryPPT,
      downloadHistoryExcel,
      handleSizeChange,
      handleCurrentChange,
      handleHistoryBackupDownload
    }
  }
}
</script>

<style scoped>
.history-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  color: #303133;
}
</style>