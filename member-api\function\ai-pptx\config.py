import json
import Agently
import os
import sys

# 添加主项目路径到 Python 路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

# 从主项目导入配置
from core.config import settings


class Config:

    cache_folder = "./.cache"

    def __init__(self) -> None:

        # init cache folder
        os.makedirs(self.cache_folder, exist_ok=True)

        # load llm - 使用主项目的配置
        from utils.llm import LLM

        self.llm = LLM(
            api_key=settings.DASHSCOPE_API_KEY,
            base_url=settings.DASHSCOPE_BASE_URL,
            model_name=settings.DASHSCOPE_MODEL,
        )

        # load agent factory - 临时禁用以解决 OpenAI 客户端 proxies 参数兼容性问题
        try:
            self.agent_factory = (
                Agently.AgentFactory()
                .set_settings("current_model", "OAIClient")
                .set_settings("model.OAIClient.url", settings.DASHSCOPE_BASE_URL)
                .set_settings("model.OAIClient.auth", {"api_key": settings.DASHSCOPE_API_KEY})
                .set_settings("model.OAIClient.options", {"model": settings.DASHSCOPE_MODEL})
            )
            print("✅ Agently AgentFactory 初始化成功")
        except Exception as e:
            print(f"⚠️ Agently AgentFactory 初始化失败: {e}")
            print("⚠️ 将使用基础 LLM 服务，Agently 功能暂时不可用")
            self.agent_factory = None


config = Config()
