<template>
  <div class="profile-container">
    <el-card class="profile-card">
      <template #header>
        <div class="card-header">
          <h3>个人信息</h3>
        </div>
      </template>
      
      <el-descriptions :column="2" border>
        <el-descriptions-item label="用户名">
          {{ userInfo.username }}
        </el-descriptions-item>
        
        <el-descriptions-item label="角色">
          <el-tag :type="getRoleType(userInfo.role)">
            {{ getRoleLabel(userInfo.role) }}
          </el-tag>
        </el-descriptions-item>
        
        <el-descriptions-item label="真实姓名">
          {{ userInfo.real_name || '-' }}
        </el-descriptions-item>
        
        <el-descriptions-item label="邮箱">
          {{ userInfo.email || '-' }}
        </el-descriptions-item>
        
        <el-descriptions-item label="手机号">
          {{ userInfo.phone || '-' }}
        </el-descriptions-item>
        
        <el-descriptions-item label="部门">
          {{ userInfo.department || '-' }}
        </el-descriptions-item>
        
        <el-descriptions-item label="账号状态">
          <el-tag :type="userInfo.status === 'active' ? 'success' : 'danger'">
            {{ userInfo.status === 'active' ? '正常' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        
        <el-descriptions-item label="注册时间">
          {{ formatDate(userInfo.created_at) }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    
    <!-- PPT生成配额信息 -->
    <el-card class="quota-card" style="margin-top: 20px">
      <template #header>
        <div class="card-header">
          <h3>PPT生成配额</h3>
        </div>
      </template>
      
      <div class="quota-content">
        <!-- 配额统计 -->
        <el-row :gutter="20" class="quota-stats">
          <el-col :span="8">
            <el-statistic title="配额限制" :value="quotaLimit" />
          </el-col>
          <el-col :span="8">
            <el-statistic title="已使用" :value="userInfo.use_quotas || 0" />
          </el-col>
          <el-col :span="8">
            <el-statistic title="剩余" :value="remainingQuota" />
          </el-col>
        </el-row>
        
        <!-- 进度条 -->
        <div class="quota-progress" v-if="hasQuotaLimit">
          <h4>使用进度</h4>
          <el-progress 
            :percentage="quotaPercentage" 
            :status="quotaStatus"
            :stroke-width="20"
            :text-inside="true"
          />
          <div class="progress-text">
            已使用 {{ userInfo.use_quotas || 0 }} / {{ userInfo.quotas }} 次
          </div>
        </div>
        
        <!-- 无限制提示 -->
        <div v-else class="unlimited-tip">
          <el-alert 
            title="无使用限制" 
            type="success"
            description="您的账号没有PPT生成次数限制，可以无限使用"
            :closable="false"
            show-icon
          />
        </div>
      </div>
    </el-card>
    
    <!-- 密码修改 -->
    <el-card class="password-card" style="margin-top: 20px">
      <template #header>
        <div class="card-header">
          <h3>修改密码</h3>
        </div>
      </template>
      
      <el-form 
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
        style="max-width: 500px"
      >
        <el-form-item label="当前密码" prop="old_password">
          <el-input 
            v-model="passwordForm.old_password" 
            type="password"
            placeholder="请输入当前密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="新密码" prop="new_password">
          <el-input 
            v-model="passwordForm.new_password" 
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirm_password">
          <el-input 
            v-model="passwordForm.confirm_password" 
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleChangePassword">
            修改密码
          </el-button>
          <el-button @click="resetPasswordForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { usersAPI } from '@/api/users'
import { ElMessage } from 'element-plus'

const authStore = useAuthStore()
const passwordFormRef = ref()

// 用户信息
const userInfo = ref({
  ...authStore.user
})

// 密码表单
const passwordForm = reactive({
  old_password: '',
  new_password: '',
  confirm_password: ''
})

// 密码验证规则
const passwordRules = {
  old_password: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  new_password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ],
  confirm_password: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.new_password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算属性
const hasQuotaLimit = computed(() => {
  return userInfo.value.quotas && userInfo.value.quotas > 0
})

const quotaLimit = computed(() => {
  if (!userInfo.value.quotas || userInfo.value.quotas === 0) {
    return '无限制'
  }
  return userInfo.value.quotas
})

const remainingQuota = computed(() => {
  if (!hasQuotaLimit.value) {
    return '无限制'
  }
  const remaining = userInfo.value.quotas - (userInfo.value.use_quotas || 0)
  return Math.max(0, remaining)
})

const quotaPercentage = computed(() => {
  if (!hasQuotaLimit.value) {
    return 0
  }
  const used = userInfo.value.use_quotas || 0
  return Math.min(100, Math.round((used / userInfo.value.quotas) * 100))
})

const quotaStatus = computed(() => {
  if (!hasQuotaLimit.value) {
    return 'success'
  }
  const percentage = quotaPercentage.value
  if (percentage >= 100) return 'exception'
  if (percentage >= 90) return 'warning'
  return 'success'
})

// 获取角色标签
const getRoleLabel = (role) => {
  const roleMap = {
    'super_admin': '超级管理员',
    'admin': '管理员',
    'user': '普通用户'
  }
  return roleMap[role] || role
}

// 获取角色类型
const getRoleType = (role) => {
  const typeMap = {
    'super_admin': 'danger',
    'admin': 'warning',
    'user': 'info'
  }
  return typeMap[role] || 'info'
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

// 刷新用户信息
const refreshUserInfo = async () => {
  try {
    const response = await authStore.getCurrentUser()
    userInfo.value = response
    console.log('用户信息:', userInfo.value)
    console.log('配额信息 - quotas:', userInfo.value.quotas, 'use_quotas:', userInfo.value.use_quotas)
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

// 修改密码
const handleChangePassword = async () => {
  const valid = await passwordFormRef.value.validate()
  if (!valid) return
  
  try {
    await usersAPI.changePassword({
      old_password: passwordForm.old_password,
      new_password: passwordForm.new_password
    })
    
    ElMessage.success('密码修改成功，请重新登录')
    // 清空表单
    resetPasswordForm()
    // 退出登录
    setTimeout(() => {
      authStore.logout()
    }, 1500)
  } catch (error) {
    ElMessage.error(error.response?.data?.detail || '密码修改失败')
  }
}

// 重置密码表单
const resetPasswordForm = () => {
  passwordFormRef.value?.resetFields()
}

// 挂载时刷新用户信息
onMounted(() => {
  refreshUserInfo()
})
</script>

<style scoped>
.profile-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.quota-content {
  padding: 20px 0;
}

.quota-stats {
  margin-bottom: 30px;
}

.quota-progress {
  margin-top: 20px;
}

.quota-progress h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #606266;
}

.progress-text {
  margin-top: 10px;
  font-size: 14px;
  color: #909399;
  text-align: center;
}

.unlimited-tip {
  padding: 20px 0;
}

:deep(.el-statistic__head) {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

:deep(.el-statistic__content) {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-descriptions__content) {
  color: #303133;
}
</style>