# -*- coding: utf-8 -*-
"""
图表数据收集器
使用装饰器模式自动收集图表生成过程中的数据
"""

import functools
import pandas as pd
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
import datetime

logger = logging.getLogger(__name__)


class ChartDataCollector:
    """图表数据收集器"""
    
    def __init__(self, bid: str, session_dir: Path):
        """
        初始化数据收集器
        
        Args:
            bid: 品牌ID
            session_dir: 会话目录
        """
        self.bid = bid
        self.session_dir = Path(session_dir)
        self.collected_data = {}
        
        # 确保目录存在
        self.session_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"初始化数据收集器 - bid: {bid}, session_dir: {session_dir}")
    
    def save_data(self, chart_type: str, data: Any, image_type: str):
        """
        保存数据到CSV文件
        
        Args:
            chart_type: 图表类型
            data: 要保存的数据
            image_type: 图片类型标识
        """
        try:
            csv_path = self.session_dir / f"{image_type}_data.csv"
            
            # 转换数据为DataFrame
            df = self._convert_to_dataframe(data)
            if df is not None and not df.empty:
                # 转换列名为中文
                df = self._translate_columns_to_chinese(df, chart_type)
                
                # 保存CSV（使用UTF-8 with BOM编码，确保中文正确显示）
                df.to_csv(csv_path, index=False, encoding='utf-8-sig')
                logger.info(f"数据已保存到CSV: {csv_path}, 列名: {df.columns.tolist()}")
                
                # 同时保存到内存用于快速访问
                self.collected_data[image_type] = {
                    'chart_type': chart_type,
                    'data': data,
                    'dataframe': df
                }
        
        except Exception as e:
            logger.error(f"保存数据失败 - chart_type: {chart_type}, error: {e}")
    
    def _translate_columns_to_chinese(self, df: pd.DataFrame, chart_type: str) -> pd.DataFrame:
        """
        将英文列名转换为中文
        
        Args:
            df: 原始DataFrame
            chart_type: 图表类型
            
        Returns:
            pd.DataFrame: 转换后的DataFrame
        """
        # 根据图表类型定义列名映射（根据实际数据字段）
        column_mappings = {
            'new_members': {
                'month': '月份',
                'new_members': '新增会员',
                'new_unfollow_members': '取关会员',
                'unfollow_rate': '取关占比',
                'wechat_new_friends': '企微加好友数'
            },
            'member_consumption': {
                'month': '月份',
                'total_actual_amount': '消费金额',
                'prepay_actual_amount': '储值消费金额',
                'actual_amount': '现金消费金额',
                'prepay_ratio': '储值消费占比',
                'cash_ratio': '现金消费占比'
            },
            'avg_consumption': {
                'month': '月份',
                'cash_consume_count': '现金消费笔数',
                'prepay_consume_count': '储值消费笔数',
                'consume_users': '消费人数',
                'total_consume_count': '总消费笔数',
                'avg_consume_amount': '单均消费',
                'avg_contribution': '人均贡献'
            },
            'consumption_num': {
                'month': '月份',
                'consume_users': '消费人数',
                'total_consume_count': '订单数量',
                'charge_count': '充值笔数',
                'consume_frequency': '消费频次'
            },
            'member_charge': {
                'month': '月份',
                'charge_amount': '充值金额',
                'period_charge_amount_unused': '消耗储值',
                'retention_rate': '留存率'
            },
            'first_charge_sale': {
                'month': '月份',
                'first_charge_amount': '首次充值金额',
                'repeat_charge_amount': '再次充值金额',
                'first_consume_amount': '首次消费金额',
                'repeat_consume_amount': '再次消费金额'
            },
            'first_member_number': {
                'month': '月份',
                'first_charge_count': '首次充值人数',
                'repeat_charge_count': '再次充值人数',
                'first_consume_count': '首次消费人数',
                'repeat_consume_count': '再次消费人数'
            },
            'credit_change': {
                'month': '月份',
                'credit_reward': '积分获得',
                'credit_consume': '积分使用'
            },
            'level_consumption': {
                'ccName': '会员等级',
                'perCapitaConsumption': '人均消费额',
                'customerUnitPrice': '客单价',
                'avgConsumFrequency': '平均消费频次',
                'member_count': '会员数量'
            },
            'level_number': {
                'ccName': '会员等级',
                'history_member_count': '历史会员数',
                'final_member_count': '期末会员数'
            },
            'level_order': {
                'ccName': '会员等级',
                'orderMoneyRatio': '消费金额占比',
                'orderNumRatio': '消费订单占比',
                'repurchaseRate': '复购率',
                'member_count': '会员数量'
            },
            'charge_distribution': {
                'charge_cash': '充值档位',
                'charge_count': '充值笔数',
                'charge_users': '充值人数'
            },
            'charge_frequency_distribution': {
                'charge_count_range': '充值频次',
                'user_count': '会员人数'
            },
            'credit_distribution': {
                'range': '积分范围',
                'member_count': '会员数',
                'total_credit': '总积分',
                'record_count': '记录数'
            }
        }
        
        # 获取当前图表类型的映射
        mapping = column_mappings.get(chart_type, {})
        
        if mapping:
            # 重命名列
            new_columns = {}
            for old_col in df.columns:
                new_col = mapping.get(old_col, old_col)
                new_columns[old_col] = new_col
            df = df.rename(columns=new_columns)
            logger.debug(f"列名转换完成 - {chart_type}: {list(new_columns.keys())} -> {list(new_columns.values())}")
        else:
            logger.debug(f"未找到列名映射 - {chart_type}, 保持原列名")
        
        return df
    
    def _convert_to_dataframe(self, data: Any) -> Optional[pd.DataFrame]:
        """
        将各种格式的数据转换为DataFrame
        
        Args:
            data: 原始数据
            
        Returns:
            pd.DataFrame or None
        """
        try:
            if isinstance(data, pd.DataFrame):
                return data
            elif isinstance(data, list) and data:
                # 列表数据
                if isinstance(data[0], dict):
                    return pd.DataFrame(data)
                else:
                    # 简单列表，创建单列DataFrame
                    return pd.DataFrame({'value': data})
            elif isinstance(data, dict):
                # 字典数据
                if all(isinstance(v, dict) for v in data.values()):
                    # 嵌套字典，转换为多行
                    rows = []
                    for key, value in data.items():
                        row = {'key': key}
                        row.update(value)
                        rows.append(row)
                    return pd.DataFrame(rows)
                else:
                    # 简单字典，转换为单行
                    return pd.DataFrame([data])
            else:
                logger.warning(f"无法转换数据类型: {type(data)}")
                return None
        
        except Exception as e:
            logger.error(f"数据转换失败: {e}")
            return None
    
    def get_all_data(self) -> Dict[str, Any]:
        """
        获取所有已收集的数据
        
        Returns:
            Dict: 所有收集的数据
        """
        return self.collected_data
    
    def save_metadata(self, image_type: str, metadata: Dict[str, Any]):
        """
        保存元数据信息（跳过JSON文件生成）
        
        Args:
            image_type: 图片类型
            metadata: 元数据
        """
        # 不再生成JSON文件，只在内存中保存元数据
        self.collected_data[f"{image_type}_metadata"] = metadata
        logger.debug(f"元数据已保存到内存: {image_type}")


def collect_chart_data(chart_type: str, include_comparison: bool = False):
    """
    装饰器：自动收集图表数据
    
    Args:
        chart_type: 图表类型标识
        include_comparison: 是否需要计算同比环比
    
    Returns:
        装饰器函数
    """
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(self, *args, **kwargs):
            # 执行原函数
            result = await func(self, *args, **kwargs)
            
            try:
                # 提取数据参数
                data = None
                image_type = None
                
                # 尝试从位置参数提取
                if len(args) > 0:
                    data = args[0]
                if len(args) > 2:
                    image_type = args[2]
                
                # 尝试从关键字参数提取
                if data is None:
                    data = kwargs.get('data')
                if image_type is None:
                    image_type = kwargs.get('image_type')
                
                # 如果有数据和图片管理器，则收集数据
                if data and image_type and hasattr(self, 'image_manager'):
                    if hasattr(self.image_manager, 'data_collector'):
                        collector = self.image_manager.data_collector
                        
                        # 保存原始数据
                        collector.save_data(chart_type, data, image_type)
                        
                        # 保存元数据
                        metadata = {
                            "chart_type": chart_type,
                            "include_comparison": include_comparison,
                            "timestamp": datetime.datetime.now().isoformat(),
                            "bid": getattr(self, 'bid', None)
                        }
                        
                        # 如果是年度数据，提取年份信息
                        if 'last_year' in image_type:
                            metadata["year_type"] = "last_year"
                        elif 'this_year' in image_type:
                            metadata["year_type"] = "this_year"
                        
                        collector.save_metadata(image_type, metadata)
                        
                        logger.debug(f"数据收集成功 - chart_type: {chart_type}, image_type: {image_type}")
            
            except Exception as e:
                # 数据收集失败不影响主流程
                logger.warning(f"数据收集失败（不影响主流程）: {e}")
            
            return result
        
        # 支持同步函数
        if not asyncio.iscoroutinefunction(func):
            @functools.wraps(func)
            def sync_wrapper(self, *args, **kwargs):
                # 执行原函数
                result = func(self, *args, **kwargs)
                
                try:
                    # 同样的数据收集逻辑
                    data = args[0] if args else kwargs.get('data')
                    image_type = args[2] if len(args) > 2 else kwargs.get('image_type')
                    
                    if data and image_type and hasattr(self, 'image_manager'):
                        if hasattr(self.image_manager, 'data_collector'):
                            collector = self.image_manager.data_collector
                            collector.save_data(chart_type, data, image_type)
                            
                            metadata = {
                                "chart_type": chart_type,
                                "include_comparison": include_comparison,
                                "timestamp": datetime.datetime.now().isoformat()
                            }
                            collector.save_metadata(image_type, metadata)
                
                except Exception as e:
                    logger.warning(f"数据收集失败（不影响主流程）: {e}")
                
                return result
            
            return sync_wrapper
        
        return wrapper
    
    return decorator


# 导入异步支持
import asyncio