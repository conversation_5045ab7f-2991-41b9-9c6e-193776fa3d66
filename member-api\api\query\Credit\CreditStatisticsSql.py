import logging
from typing import List, Dict, Any
from core.database import db

logger = logging.getLogger(__name__)

class CreditStatisticsQueries:
    """积分分布统计SQL查询类"""

    @staticmethod
    async def get_credit_balance_distribution(end_date: str, bid: str) -> List[Dict[str, Any]]:
        """获取会员积分余额分布

        数据库：welife_hydb.dws_user_info_charges_credit_summary_d
        字段：uno（用户编号）, credit_saving（积分余额）, uregistered（注册时间）
        计算方式：
        1. 先筛选注册时间在指定日期之前的用户
        2. 取每个用户的最新一条记录（按ftime降序）
        3. 按积分余额区间分组统计人数,每个区间的总余额
        积分区间：0-199、200-399、400-599、600-799、800-999、1000-1199、1200-1399、1400-1599、1600-1799、1800-1999、>=2000

        Args:
            end_date: 截止日期 (YYYYMMDD格式)，用于过滤注册时间
            bid: 品牌ID

        Returns:
            积分余额分布列表，每个元素包含credit_range和user_count_distinct
        """
        try:
            # 将YYYYMMDD格式转换为YYYY-MM-DD HH:MM:SS格式
            formatted_end_date = f"{end_date[:4]}-{end_date[4:6]}-{end_date[6:8]} 23:59:59"

            sql = f"""
            WITH picked AS (
              SELECT uno, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (PARTITION BY a.uno ORDER BY a.ftime DESC) AS rn
                FROM dws_user_info_charges_credit_summary_d a
                WHERE a.bid = {bid}
                  AND a.uregistered < '{formatted_end_date}'
              ) x
              WHERE rn = 1
            )
            SELECT
              t.credit_range,
              COUNT(DISTINCT t.uno) AS user_count_distinct,
              COUNT(*) AS record_count,
              SUM(t.credit_saving) AS total_credit_saving    -- 每个区间的总余额
            FROM (
              SELECT
                CASE
                  WHEN credit_saving BETWEEN    0 AND  199 THEN 0
                  WHEN credit_saving BETWEEN  200 AND  399 THEN 1
                  WHEN credit_saving BETWEEN  400 AND  599 THEN 2
                  WHEN credit_saving BETWEEN  600 AND  799 THEN 3
                  WHEN credit_saving BETWEEN  800 AND  999 THEN 4
                  WHEN credit_saving BETWEEN 1000 AND 1199 THEN 5
                  WHEN credit_saving BETWEEN 1200 AND 1399 THEN 6
                  WHEN credit_saving BETWEEN 1400 AND 1599 THEN 7
                  WHEN credit_saving BETWEEN 1600 AND 1799 THEN 8
                  WHEN credit_saving BETWEEN 1800 AND 1999 THEN 9
                  ELSE 10
                END AS bucket_idx,
                CASE
                  WHEN credit_saving BETWEEN    0 AND  199 THEN '0-199'
                  WHEN credit_saving BETWEEN  200 AND  399 THEN '200-399'
                  WHEN credit_saving BETWEEN  400 AND  599 THEN '400-599'
                  WHEN credit_saving BETWEEN  600 AND  799 THEN '600-799'
                  WHEN credit_saving BETWEEN  800 AND  999 THEN '800-999'
                  WHEN credit_saving BETWEEN 1000 AND 1199 THEN '1000-1199'
                  WHEN credit_saving BETWEEN 1200 AND 1399 THEN '1200-1399'
                  WHEN credit_saving BETWEEN 1400 AND 1599 THEN '1400-1599'
                  WHEN credit_saving BETWEEN 1600 AND 1799 THEN '1600-1799'
                  WHEN credit_saving BETWEEN 1800 AND 1999 THEN '1800-1999'
                  ELSE '>=2000'
                END AS credit_range,
                uno,
                credit_saving
              FROM picked
            ) t
            GROUP BY t.bucket_idx, t.credit_range
            ORDER BY t.bucket_idx
            """

            logger.info(f"执行积分余额分布查询 - bid: {bid}, 截止日期: {end_date}")
            logger.debug(f"积分余额分布查询SQL: {sql}")

            result = await db.execute_welife_hydb_query(sql)

            # 数据格式转换，确保数值类型正确
            formatted_result = []
            for row in result:
                formatted_result.append({
                    'credit_range': str(row['credit_range']) if row['credit_range'] is not None else '',
                    'user_count_distinct': int(row['user_count_distinct']) if row['user_count_distinct'] is not None else 0,
                    'record_count': int(row['record_count']) if row['record_count'] is not None else 0,
                    'total_credit_saving': float(row['total_credit_saving']) if row['total_credit_saving'] is not None else 0.0
                })

            logger.info(f"积分余额分布查询完成，返回{len(formatted_result)}个区间")
            return formatted_result

        except Exception as e:
            logger.error(f"获取积分余额分布失败: {str(e)}", exc_info=True)
            raise

    @staticmethod
    async def get_credit_balance_by_level(end_date: str, bid: str) -> List[Dict[str, Any]]:
        """获取每个卡等级的积分总余额

        数据库：welife_hydb.dws_user_info_charges_credit_summary_d
        字段：uno（用户编号）, ccname（卡等级名称）, credit_saving（积分余额）, uregistered（注册时间）
        计算方式：
        1. 先筛选注册时间在指定日期之前的用户
        2. 取每个用户的最新一条记录（按ftime降序）
        3. 按卡等级分组，统计积分余额总和和用户数量
        4. 按积分余额总和降序排列

        Args:
            end_date: 截止日期 (YYYYMMDD格式)，用于过滤注册时间
            bid: 品牌ID

        Returns:
            卡等级积分余额列表，每个元素包含ccname、total_credit_saving和user_count
        """
        try:
            # 将YYYYMMDD格式转换为YYYY-MM-DD HH:MM:SS格式
            formatted_end_date = f"{end_date[:4]}-{end_date[4:6]}-{end_date[6:8]} 23:59:59"

            sql = f"""
            WITH picked AS (
              SELECT uno, ccname, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (
                    PARTITION BY a.uno
                    ORDER BY a.ftime DESC
                  ) AS rn
                FROM dws_user_info_charges_credit_summary_d a
                WHERE a.bid = {bid}
                  AND a.uregistered < '{formatted_end_date}'
              ) x
              WHERE x.rn = 1
            )
            SELECT
              ccname,
              SUM(credit_saving) AS total_credit_saving,
              COUNT(*) AS user_count
            FROM picked
            GROUP BY ccname
            ORDER BY total_credit_saving DESC
            """

            logger.info(f"执行卡等级积分余额查询 - bid: {bid}, 截止日期: {end_date}")
            logger.debug(f"卡等级积分余额查询SQL: {sql}")

            result = await db.execute_welife_hydb_query(sql)

            # 数据格式转换，确保数值类型正确
            formatted_result = []
            for row in result:
                formatted_result.append({
                    'ccname': str(row['ccname']) if row['ccname'] is not None else '',
                    'total_credit_saving': int(row['total_credit_saving']) if row['total_credit_saving'] is not None else 0,
                    'user_count': int(row['user_count']) if row['user_count'] is not None else 0
                })

            logger.info(f"卡等级积分余额查询完成，返回{len(formatted_result)}个等级")
            return formatted_result

        except Exception as e:
            logger.error(f"获取卡等级积分余额失败: {str(e)}", exc_info=True)
            raise

    @staticmethod
    async def get_credit_balance_by_age(end_date: str, bid: str) -> List[Dict[str, Any]]:
        """获取各年龄段积分余额分布

        数据库：welife_hydb.dws_user_info_charges_credit_summary_d
        字段：uno（用户编号）, ubirthday（生日）, credit_saving（积分余额）, ftime（记录时间）, uregistered（注册时间）
        计算方式：
        1. 先筛选注册时间在指定日期之前的用户
        2. 取每个用户的最新一条记录（按ftime降序）
        3. 根据生日计算年龄，按年龄段分组统计人数和积分余额总和
        年龄段：0-18、19-23、24-30、31-35、36-40、41-45、46-50、51-55、56-60、61及以上、未填写生日

        Args:
            end_date: 截止日期 (YYYYMMDD格式)，用于过滤注册时间和计算年龄
            bid: 品牌ID

        Returns:
            年龄段积分余额分布列表，每个元素包含age_range、user_count和total_credit_saving
        """
        try:
            # 将YYYYMMDD格式转换为YYYY-MM-DD HH:MM:SS格式
            formatted_end_date = f"{end_date[:4]}-{end_date[4:6]}-{end_date[6:8]} 23:59:59"

            sql = f"""
            WITH filtered AS (
              SELECT uno, ubirthday, credit_saving, ftime
              FROM dws_user_info_charges_credit_summary_d
              WHERE bid = {bid}
                AND uregistered < '{formatted_end_date}'
            ),
            last_ftime AS (
              SELECT uno, MAX(ftime) AS max_ftime
              FROM filtered
              GROUP BY uno
            ),
            picked AS (
              SELECT f.uno, f.ubirthday, f.credit_saving, f.ftime
              FROM filtered f
              JOIN last_ftime lf
                ON f.uno = lf.uno AND f.ftime = lf.max_ftime
            ),
            age_base AS (
              SELECT
                uno,
                credit_saving,
                CASE
                  WHEN ubirthday IS NULL THEN NULL
                  ELSE FLOOR(MONTHS_BETWEEN(TO_DATE(CAST(ftime AS STRING), 'yyyymmdd'), ubirthday) / 12)
                END AS age_years
              FROM picked
            ),
            labeled AS (
              SELECT
                CASE
                  WHEN age_years IS NULL           THEN 99
                  WHEN age_years BETWEEN  0 AND 18 THEN 0
                  WHEN age_years BETWEEN 19 AND 23 THEN 1
                  WHEN age_years BETWEEN 24 AND 30 THEN 2
                  WHEN age_years BETWEEN 31 AND 35 THEN 3
                  WHEN age_years BETWEEN 36 AND 40 THEN 4
                  WHEN age_years BETWEEN 41 AND 45 THEN 5
                  WHEN age_years BETWEEN 46 AND 50 THEN 6
                  WHEN age_years BETWEEN 51 AND 55 THEN 7
                  WHEN age_years BETWEEN 56 AND 60 THEN 8
                  WHEN age_years >= 61             THEN 9
                END AS bucket_idx,
                CASE
                  WHEN age_years IS NULL           THEN '未填写生日'
                  WHEN age_years BETWEEN  0 AND 18 THEN '0-18'
                  WHEN age_years BETWEEN 19 AND 23 THEN '19-23'
                  WHEN age_years BETWEEN 24 AND 30 THEN '24-30'
                  WHEN age_years BETWEEN 31 AND 35 THEN '31-35'
                  WHEN age_years BETWEEN 36 AND 40 THEN '36-40'
                  WHEN age_years BETWEEN 41 AND 45 THEN '41-45'
                  WHEN age_years BETWEEN 46 AND 50 THEN '46-50'
                  WHEN age_years BETWEEN 51 AND 55 THEN '51-55'
                  WHEN age_years BETWEEN 56 AND 60 THEN '56-60'
                  WHEN age_years >= 61             THEN '61及以上'
                END AS age_range,
                credit_saving
              FROM age_base
            )
            SELECT
              age_range,
              COUNT(*) AS user_count,
              SUM(credit_saving) AS total_credit_saving
            FROM labeled
            GROUP BY bucket_idx, age_range
            ORDER BY bucket_idx
            """

            logger.info(f"执行年龄段积分余额分布查询 - bid: {bid}, 截止日期: {end_date}")
            logger.debug(f"年龄段积分余额分布查询SQL: {sql}")

            result = await db.execute_welife_hydb_query(sql)

            # 数据格式转换，确保数值类型正确
            formatted_result = []
            for row in result:
                formatted_result.append({
                    'age_range': str(row['age_range']) if row['age_range'] is not None else '',
                    'user_count': int(row['user_count']) if row['user_count'] is not None else 0,
                    'total_credit_saving': int(row['total_credit_saving']) if row['total_credit_saving'] is not None else 0
                })

            logger.info(f"年龄段积分余额分布查询完成，返回{len(formatted_result)}个年龄段")
            return formatted_result

        except Exception as e:
            logger.error(f"获取年龄段积分余额分布失败: {str(e)}", exc_info=True)
            raise