# -*- coding: utf-8 -*-
"""
PPT报告路由配置
定义PPTreport模块的API端点
"""

import logging
import datetime
import asyncio
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Request, Depends
from pydantic import BaseModel, Field

from core.models import QueryParams, ResponseModel
from auth.middleware.auth_middleware import get_current_user, check_bid_permission, check_sid_permission
from auth.services.quota_service import QuotaService
from auth.services.audit_service import AuditService
from .PPTReport import ppt_report_service
from .task_management import (
    TaskModel, TaskStatus, TaskPriority,
    get_task_queue, TaskManager,
    get_task_scheduler
)

logger = logging.getLogger(__name__)

# 创建路由器（需要认证）
router = APIRouter(dependencies=[Depends(get_current_user)])

# 创建下载专用路由器（不自动验证认证，允许通过token参数验证）
download_router = APIRouter()

def _format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"

    size_mb = size_bytes / (1024 * 1024)
    if size_mb >= 1:
        return f"{size_mb:.1f}MB"
    else:
        size_kb = size_bytes / 1024
        return f"{size_kb:.1f}KB"

class PPTGenerationRequest(BaseModel):
    """PPT生成请求模型"""
    query_type: str = Field(..., description="查询类型")
    bid: str = Field(..., description="品牌ID")
    sid: Optional[str] = Field(None, description="门店ID")
    start_date: Optional[str] = Field(None, description="开始日期")
    end_date: Optional[str] = Field(None, description="结束日期")
    cashier_system: Optional[str] = Field("0", description="收银系统类型：0-无收银系统，1-品智收银")
    merchant_id: Optional[str] = Field(None, description="商户ID（品智收银门店拼音名称）")
    output_filename: Optional[str] = Field(None, description="输出文件名（可选）")

class CustomDataRequest(BaseModel):
    """自定义数据请求模型"""
    custom_data: Dict[str, Any] = Field(..., description="自定义PPT参数数据")
    output_filename: Optional[str] = Field(None, description="输出文件名（可选）")

@router.post("/generate", response_model=ResponseModel)
async def generate_member_report(
    request: PPTGenerationRequest,
    http_request: Request,
    current_user: dict = Depends(get_current_user)
):
    """
    生成会员数据报告PPT（使用任务系统）

    Args:
        request: PPT生成请求
        current_user: 当前登录用户信息
        http_request: HTTP请求对象

    Returns:
        ResponseModel: 统一响应格式
    """
    try:
        logger.info(f"收到PPT生成请求: {request}")
        logger.info(f"当前用户: {current_user.get('username', 'unknown')}")
        
        # 检查用户配额
        from core.database import db
        quota_service = QuotaService(db.task_management_pool)
        username = current_user.get('username')
        
        quota_info = await quota_service.check_user_quota(username)
        if not quota_info['has_quota']:
            logger.warning(f"用户 {username} 配额不足: {quota_info['message']}")
            return ResponseModel(
                code=403,
                message=quota_info['message'],
                data={
                    "quota_info": {
                        "used": quota_info.get('used', 0),
                        "quota": quota_info.get('quota'),
                        "remaining": quota_info.get('remaining', 0)
                    }
                }
            )
        
        # 验证BID权限
        if request.bid:
            has_permission = await check_bid_permission(
                request.bid, "read", current_user
            )
            if not has_permission:
                logger.warning(f"用户 {current_user.get('username')} 无权访问BID {request.bid}")
                return ResponseModel(
                    code=403,
                    message=f"您没有访问品牌 {request.bid} 的权限",
                    data=None
                )
        
        # 验证SID权限（如果提供了SID）
        if request.bid and request.sid:
            has_sid_permission = await check_sid_permission(
                request.bid, request.sid, current_user
            )
            if not has_sid_permission:
                logger.warning(f"用户 {current_user.get('username')} 无权访问SID {request.sid}")
                return ResponseModel(
                    code=403,
                    message=f"您没有访问门店 {request.sid} 的权限",
                    data=None
                )
        
        logger.info("权限验证通过，将请求转发到任务系统处理...")

        # 创建任务模型
        task_name = request.output_filename or f"会员报告_{request.bid}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
        # 确保文件名包含.pptx扩展名
        if not task_name.endswith('.pptx'):
            task_name = f"{task_name}.pptx"
        
        # 获取当前用户的username，写入user_id字段
        username = current_user.get('username', None)
        
        task = TaskModel(
            task_type=request.query_type,  # 使用实际的查询类型
            task_name=task_name,
            user_id=username,  # 将username存储到user_id字段
            bid=request.bid,
            sid=request.sid,
            task_start_ftime=request.start_date,
            task_end_ftime=request.end_date,
            priority=TaskPriority.NORMAL.value,
            report_type="member_report",
            source="api_generate",
            estimated_duration=5  # 预计5分钟
        )
        
        # 添加到任务队列
        task_queue = get_task_queue()
        created_task = await task_queue.add_task(task)
        
        logger.info(f"任务创建成功: ID={created_task.id}, UUID={created_task.task_uuid}")
        
        # 等待任务完成（最多等待10分钟）
        task_manager = TaskManager()
        max_wait_time = 600  # 10分钟
        wait_interval = 2  # 每2秒检查一次
        total_waited = 0
        quota_deducted = False  # 标记配额是否已扣减
        
        while total_waited < max_wait_time:
            await asyncio.sleep(wait_interval)
            total_waited += wait_interval
            
            # 查询任务状态
            current_task = await task_manager.get_task_by_id(created_task.id)
            
            if not current_task:
                logger.error(f"任务 {created_task.id} 丢失")
                break
            
            logger.info(f"任务 {created_task.id} 状态: {current_task.status}, 进度: {current_task.progress}%")
            
            # 如果任务完成
            if current_task.status == TaskStatus.COMPLETED.value:
                logger.info(f"任务 {created_task.id} 完成")
                
                # 任务成功后扣减配额
                if not quota_deducted:
                    await quota_service.use_quota(username, "ppt_generation")
                    quota_deducted = True
                    logger.info(f"任务成功，已扣减用户 {username} 的PPT生成配额")
                
                # 记录PPT生成成功的审计日志
                audit_service = AuditService(db.task_management_pool)
                await audit_service.log_action(
                    action="generate_ppt",
                    user_id=current_user.get("user_id"),
                    username=current_user.get("username"),
                    resource="ppt_report",
                    resource_id=request.bid,
                    request_data={
                        "bid": request.bid,
                        "sid": request.sid,
                        "start_date": request.start_date,
                        "end_date": request.end_date,
                        "query_type": request.query_type,
                        "task_uuid": created_task.task_uuid
                    },
                    response_data={
                        "task_id": created_task.id,
                        "file_url": current_task.ppt_oss_url,
                        "file_size": current_task.file_PPT_size
                    },
                    status="success",
                    ip_address=http_request.client.host if http_request and http_request.client else None,
                    user_agent=http_request.headers.get("user-agent") if http_request else None
                )
                
                # 构建返回结果，兼容原有格式
                result = {
                    "success": True,
                    "data": {
                        "file_url": current_task.ppt_oss_url,
                        "download_url": current_task.ppt_oss_url,
                        "dataDownloadUrl": current_task.excel_oss_url,
                        "file_name": current_task.task_name,
                        "file_size": current_task.file_PPT_size,
                        "object_name": current_task.task_name
                    },
                    "message": "PPT生成成功"
                }
                break
            
            # 如果任务失败
            elif current_task.status == TaskStatus.FAILED.value:
                logger.error(f"任务 {created_task.id} 失败: {current_task.error_info}")
                result = {
                    "success": False,
                    "message": f"PPT生成失败: {current_task.error_info}"
                }
                break
            
            # 如果任务被取消
            elif current_task.status == TaskStatus.CANCELLED.value:
                logger.warning(f"任务 {created_task.id} 被取消")
                result = {
                    "success": False,
                    "message": "任务被取消"
                }
                break
        else:
            # 超时
            logger.error(f"任务 {created_task.id} 执行超时")
            result = {
                "success": False,
                "message": "任务执行超时"
            }

        if result["success"]:
            # 转换为前端期望的格式
            download_url = result["data"].get("file_url") or result["data"].get("download_url")
            file_name = result["data"].get("file_name")
            object_name = result["data"].get("object_name")

            # 详细的调试日志
            logger.info(f"PPT生成结果详情:")
            logger.info(f"  - 文件名: {file_name}")
            logger.info(f"  - 对象名: {object_name}")
            logger.info(f"  - 原始下载URL: {download_url}")
            logger.info(f"  - 所有返回数据: {result['data']}")

            response_data = {
                "downloadUrl": download_url,
                "dataDownloadUrl": result["data"].get("dataDownloadUrl"),  # Excel下载链接
                "previewUrl": None,  # 暂时不支持预览
                "generatedAt": result["data"].get("upload_time") or datetime.datetime.now().isoformat(),
                "slideCount": 12,  # 默认值，后续可以从PPT文件中获取
                "fileSize": _format_file_size(result["data"].get("file_size", 0)),
                "fileName": file_name,
                "objectName": object_name,
                "taskId": created_task.task_uuid,  # 添加任务UUID
                "taskUuid": created_task.task_uuid,  # 兼容性
                "bid": request.bid  # 添加BID
            }

            # 验证下载URL是否有效
            if not download_url:
                logger.warning("PPT生成成功但下载URL为空，尝试生成默认URL")
                if object_name:
                    response_data["downloadUrl"] = f"/api/ppt-report/download/ppt-reports/{object_name}"
                    logger.info(f"使用对象名生成默认下载URL: {response_data['downloadUrl']}")
                elif file_name:
                    response_data["downloadUrl"] = f"/api/ppt-report/download/ppt-reports/{file_name}"
                    logger.info(f"使用文件名生成默认下载URL: {response_data['downloadUrl']}")
                else:
                    logger.error("无法生成下载URL：文件名和对象名都为空")
            else:
                logger.info(f"使用OSS生成的下载URL: {download_url}")

            return ResponseModel(
                code=200,
                message=result["message"],
                data=response_data
            )
        else:
            return ResponseModel(
                code=500,
                message=result["message"],
                data=None
            )

    except Exception as e:
        logger.error(f"生成PPT报告异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"生成PPT报告失败: {str(e)}")

@router.post("/preview", response_model=ResponseModel)
async def get_data_preview(
    request: PPTGenerationRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    获取数据预览（不生成PPT，仅返回数据）

    Args:
        request: PPT生成请求
        current_user: 当前登录用户信息

    Returns:
        ResponseModel: 数据预览结果
    """
    try:
        logger.info(f"收到数据预览请求: {request}")
        
        # 验证BID权限
        if request.bid:
            has_permission = await check_bid_permission(
                request.bid, "read", current_user
            )
            if not has_permission:
                return ResponseModel(
                    code=403,
                    message=f"您没有访问品牌 {request.bid} 的权限",
                    data=None
                )
        
        # 验证SID权限（如果提供了SID）
        if request.bid and request.sid:
            has_sid_permission = await check_sid_permission(
                request.bid, request.sid, current_user
            )
            if not has_sid_permission:
                return ResponseModel(
                    code=403,
                    message=f"您没有访问门店 {request.sid} 的权限",
                    data=None
                )

        # 转换为QueryParams格式
        query_params = QueryParams(
            query_type=request.query_type,
            bid=request.bid,
            sid=request.sid,
            start_date=request.start_date,
            end_date=request.end_date
        )

        result = await ppt_report_service.get_data_preview(query_params)

        if result["success"]:
            return ResponseModel(
                code=200,
                message=result["message"],
                data=result["data"]
            )
        else:
            return ResponseModel(
                code=500,
                message=result["message"],
                data=result["data"]
            )

    except Exception as e:
        logger.error(f"获取数据预览异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取数据预览失败: {str(e)}")

@router.post("/generate-custom", response_model=ResponseModel)
async def generate_with_custom_data(request: CustomDataRequest):
    """
    使用自定义数据生成PPT

    Args:
        request: 自定义数据请求

    Returns:
        ResponseModel: 生成结果
    """
    try:
        logger.info(f"收到自定义数据PPT生成请求")

        result = await ppt_report_service.generate_with_custom_data(
            custom_data=request.custom_data,
            output_filename=request.output_filename
        )

        if result["success"]:
            return ResponseModel(
                code=200,
                message=result["message"],
                data=result["data"]
            )
        else:
            return ResponseModel(
                code=500,
                message=result["message"],
                data=result["data"]
            )

    except Exception as e:
        logger.error(f"使用自定义数据生成PPT异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"使用自定义数据生成PPT失败: {str(e)}")

@router.get("/template/validate", response_model=ResponseModel)
async def validate_template():
    """
    验证PPT模板

    Returns:
        ResponseModel: 验证结果
    """
    try:
        logger.info("收到PPT模板验证请求")

        result = await ppt_report_service.validate_ppt_template()

        if result["valid"]:
            return ResponseModel(
                code=200,
                message=result["message"],
                data=result
            )
        else:
            return ResponseModel(
                code=400,
                message=result["message"],
                data=result
            )

    except Exception as e:
        logger.error(f"验证PPT模板异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"验证PPT模板失败: {str(e)}")

@router.get("/template/info", response_model=ResponseModel)
async def get_template_info():
    """
    获取PPT模板信息

    Returns:
        ResponseModel: 模板信息
    """
    try:
        logger.info("收到获取PPT模板信息请求")

        from services.ppt_service import ppt_service
        template_info = ppt_service.get_template_info()

        return ResponseModel(
            code=200,
            message="获取PPT模板信息成功",
            data=template_info
        )

    except Exception as e:
        logger.error(f"获取PPT模板信息异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取PPT模板信息失败: {str(e)}")

@router.get("/history", response_model=ResponseModel)
async def get_ppt_history(
    current_user: dict = Depends(get_current_user),
    skip: int = 0,
    limit: int = 50,
    bid: Optional[str] = None,
    status: Optional[str] = None
):
    """
    获取用户的PPT生成历史记录
    
    Args:
        current_user: 当前用户信息
        skip: 跳过的记录数
        limit: 返回的记录数限制
        bid: 品牌ID过滤（可选）
        status: 状态过滤（可选）
    
    Returns:
        ResponseModel: 历史记录列表
    """
    try:
        from .History import ppt_history_service
        
        result = await ppt_history_service.get_user_history(
            username=current_user["username"],
            skip=skip,
            limit=limit,
            bid=bid,
            status=status
        )
        
        return ResponseModel(
            code=200,
            message="获取历史记录成功",
            data=result
        )
        
    except Exception as e:
        logger.error(f"获取PPT历史记录异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取历史记录失败: {str(e)}")

@download_router.api_route("/download/{folder:path}", methods=["GET", "HEAD"])
async def download_file(
    folder: str, 
    request: Request = None,
    token: Optional[str] = None
):
    """
    下载PPT文件 - 支持OSS预签名URL重定向和本地文件下载
    支持任务文件夹结构（如 ppt-reports/bid_uuid/filename.pptx）

    Args:
        folder: 文件路径（可能包含子目录）
        token: 可选的认证token（URL参数）

    Returns:
        文件下载响应或重定向到OSS
    """
    try:
        # 如果提供了token参数，验证token
        if token:
            from auth.utils.jwt_handler import verify_token
            payload = verify_token(token)
            if not payload:
                raise HTTPException(status_code=401, detail="Invalid token")
        else:
            # 如果没有token参数，使用标准的依赖注入认证
            # 注意：这里需要通过其他方式验证，因为download端点可能不带Authorization header
            pass
        from fastapi.responses import FileResponse, RedirectResponse
        from services.oss_service import oss_service
        import os
        import urllib.parse

        # 分离路径和文件名
        # folder 可能是 "ppt-reports/bid_uuid/filename.pptx" 的形式
        # 也可能错误地包含重复路径: "ppt-reports/bid_uuid/ppt-reports/bid_uuid/filename.pptx"
        logger.info(f"收到文件下载请求，原始folder参数: {folder}")
        
        # 清理重复的路径
        # 如果路径包含重复的 "ppt-reports/bid_uuid" 模式，只保留最后一个
        path_parts = folder.split('/')
        logger.info(f"原始路径分割结果: {path_parts}")
        
        # 检查是否有重复的路径模式
        if len(path_parts) >= 5:  # 可能包含重复路径
            # 查找是否有重复的 ppt-reports
            ppt_indices = [i for i, part in enumerate(path_parts) if part == 'ppt-reports']
            if len(ppt_indices) > 1:
                # 有重复，使用最后一个ppt-reports开始的路径
                last_ppt_index = ppt_indices[-1]
                path_parts = path_parts[last_ppt_index:]
                logger.info(f"检测到重复路径，清理后: {path_parts}")
        
        # 处理不同的路径格式
        if len(path_parts) >= 3:  # ppt-reports/task_folder/filename
            base_folder = path_parts[0]
            task_folder = path_parts[1]
            filename = '/'.join(path_parts[2:])
            full_folder = f"{base_folder}/{task_folder}"
            logger.info(f"三段式路径: base={base_folder}, task={task_folder}, file={filename}")
        elif len(path_parts) == 2:  # ppt-reports/filename
            base_folder = path_parts[0]
            filename = path_parts[1]
            full_folder = base_folder
            task_folder = None
            logger.info(f"两段式路径: base={base_folder}, file={filename}")
        else:
            # 单个路径，可能是错误的请求
            logger.error(f"无效的路径格式，只有{len(path_parts)}段")
            raise HTTPException(status_code=400, detail="无效的文件路径格式")
        
        # URL解码文件名（处理中文文件名）
        decoded_filename = urllib.parse.unquote(filename)
        logger.info(f"解码后的文件名: {decoded_filename}")

        logger.info(f"路径解析完成: folder={full_folder}, filename={decoded_filename}")

        # 安全检查：防止路径遍历攻击
        if ".." in folder or ".." in decoded_filename:
            logger.warning(f"检测到潜在的路径遍历攻击: {folder}")
            raise HTTPException(status_code=400, detail="无效的文件路径")

        # 构建完整的对象名称
        if task_folder:
            object_name = f"{task_folder}/{decoded_filename}"
            logger.info(f"构建object_name（有task_folder）: {object_name}")
        else:
            object_name = decoded_filename
            logger.info(f"构建object_name（无task_folder）: {object_name}")
        
        # 获取文件信息
        download_info = oss_service.download_file(object_name, base_folder)

        if not download_info["success"]:
            logger.warning(f"文件下载失败: {download_info['error']}")
            raise HTTPException(status_code=404, detail=download_info["error"])

        # 检查下载类型
        download_type = download_info.get("download_type", "local_file")
        storage_type = download_info.get("storage_type", "本地存储")

        logger.info(f"文件下载方式: {storage_type} ({download_type})")

        # OSS预签名URL重定向
        if download_type == "presigned_url":
            presigned_url = download_info["file_url"]
            logger.info(f"重定向到OSS预签名URL: {presigned_url}")
            return RedirectResponse(url=presigned_url, status_code=302)

        # 本地文件下载处理
        file_path = download_info["file_path"]

        if not file_path or not os.path.exists(file_path):
            logger.error(f"本地文件不存在: {file_path}")
            raise HTTPException(status_code=404, detail=f"文件不存在: {filename}")

        # 验证文件大小
        file_size = os.path.getsize(file_path)
        if file_size == 0:
            logger.warning(f"文件大小为0: {file_path}")
            raise HTTPException(status_code=400, detail="文件为空")

        logger.info(f"开始处理本地文件请求: {file_path} (大小: {file_size} bytes)")

        # 处理HEAD请求（用于前端预检查）
        if request and request.method == "HEAD":
            from fastapi.responses import Response

            logger.info(f"处理HEAD请求: {folder}/{decoded_filename}")

            # 正确处理中文文件名编码
            # 使用RFC 5987标准的编码方式
            encoded_filename = urllib.parse.quote(decoded_filename, safe='')
            content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"

            return Response(
                status_code=200,
                headers={
                    "Content-Type": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                    "Content-Length": str(file_size),
                    "Content-Disposition": content_disposition
                }
            )

        # 处理GET请求（实际下载）
        logger.info(f"开始下载文件: {file_path}")
        return FileResponse(
            path=file_path,
            filename=decoded_filename,  # 使用解码后的文件名
            media_type='application/vnd.openxmlformats-officedocument.presentationml.presentation'
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载文件异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"下载文件失败: {str(e)}")

@router.get("/test-download")
async def test_download():
    """
    测试下载功能

    Returns:
        测试文件下载信息
    """
    try:
        from services.oss_service import oss_service

        # 列出可用的文件
        files_info = oss_service.list_files("ppt-reports")

        if files_info["success"] and files_info["files"]:
            return ResponseModel(
                code=200,
                message="获取文件列表成功",
                data={
                    "available_files": files_info["files"],
                    "total_count": files_info["total_count"],
                    "download_base_url": "/api/ppt-report/download/ppt-reports/",
                    "oss_enabled": oss_service.oss_enabled,
                    "storage_type": "OSS云存储" if oss_service.oss_enabled else "本地存储"
                }
            )
        else:
            return ResponseModel(
                code=404,
                message="没有找到可下载的文件",
                data={
                    "available_files": [],
                    "total_count": 0,
                    "upload_path": str(oss_service.upload_path / "ppt-reports"),
                    "oss_enabled": oss_service.oss_enabled,
                    "storage_type": "OSS云存储" if oss_service.oss_enabled else "本地存储"
                }
            )

    except Exception as e:
        logger.error(f"测试下载功能异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"测试下载功能失败: {str(e)}")

@router.get("/oss-status", response_model=ResponseModel)
async def get_oss_status():
    """
    获取OSS服务状态

    Returns:
        ResponseModel: OSS状态信息
    """
    try:
        from services.oss_service import oss_service
        from core.config import settings

        status_info = {
            "oss_available": oss_service.oss_enabled,
            "bucket_configured": settings.is_oss_configured(),
            "local_storage_path": str(oss_service.upload_path),
            "storage_mode": "OSS云存储 + 本地留档" if oss_service.oss_enabled else "仅本地存储"
        }

        if oss_service.oss_enabled:
            try:
                # 测试OSS连接
                bucket_info = oss_service.bucket.get_bucket_info()
                status_info.update({
                    "bucket_name": bucket_info.name,
                    "bucket_location": bucket_info.location,
                    "connection_status": "正常"
                })
            except Exception as e:
                status_info.update({
                    "connection_status": f"连接异常: {str(e)}"
                })

        return ResponseModel(
            code=200,
            message="OSS状态获取成功",
            data=status_info
        )

    except Exception as e:
        logger.error(f"获取OSS状态异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取OSS状态失败: {str(e)}")


# ============ 任务管理相关API ============

@router.post("/task/create", response_model=ResponseModel)
async def create_ppt_task(request: PPTGenerationRequest):
    """
    创建PPT生成任务（异步执行）
    
    Args:
        request: PPT生成请求
        
    Returns:
        ResponseModel: 任务创建结果
    """
    try:
        logger.info(f"收到PPT任务创建请求: {request}")
        
        # 创建任务模型
        task_name = request.output_filename or f"会员报告_{request.bid}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
        # 确保文件名包含.pptx扩展名
        if not task_name.endswith('.pptx'):
            task_name = f"{task_name}.pptx"
        
        task = TaskModel(
            task_type=request.query_type,  # 使用实际的查询类型
            task_name=task_name,
            bid=request.bid,
            sid=request.sid,
            task_start_ftime=request.start_date,
            task_end_ftime=request.end_date,
            priority=TaskPriority.NORMAL.value,
            report_type="member_report",
            source="api",
            estimated_duration=5  # 预计5分钟
        )
        
        # 添加到任务队列
        task_queue = get_task_queue()
        created_task = await task_queue.add_task(task)
        
        # 使用配额（任务创建成功后扣减）
        await quota_service.use_quota(username, "ppt_generation")
        
        logger.info(f"任务创建成功: ID={created_task.id}, UUID={created_task.task_uuid}")
        
        return ResponseModel(
            code=200,
            message="任务创建成功，正在处理中",
            data={
                "task_id": created_task.id,
                "task_uuid": created_task.task_uuid,
                "status": created_task.status,
                "progress": created_task.progress,
                "current_step": created_task.current_step,
                "estimated_duration": created_task.estimated_duration
            }
        )
        
    except Exception as e:
        logger.error(f"创建PPT任务异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"创建任务失败: {str(e)}")


@router.get("/task/{task_id}/status", response_model=ResponseModel)
async def get_task_status(task_id: int):
    """
    获取任务状态
    
    Args:
        task_id: 任务ID
        
    Returns:
        ResponseModel: 任务状态信息
    """
    try:
        task_manager = TaskManager()
        task = await task_manager.get_task_by_id(task_id)
        
        if not task:
            return ResponseModel(
                code=404,
                message="任务不存在",
                data=None
            )
        
        # 构建返回数据
        data = {
            "task_id": task.id,
            "task_uuid": task.task_uuid,
            "task_name": task.task_name,
            "status": task.status,
            "progress": task.progress,
            "current_step": task.current_step,
            "created_at": task.created_at.isoformat() if task.created_at else None,
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "actual_duration": task.actual_duration,
            "retry_count": task.retry_count,
            "max_retry_count": task.max_retry_count
        }
        
        # 如果任务完成，添加结果信息
        if task.status == TaskStatus.COMPLETED.value:
            data.update({
                "result": {
                    "ppt_url": task.ppt_oss_url,
                    "excel_url": task.excel_oss_url,
                    "file_size": task.file_PPT_size
                }
            })
        
        # 如果任务失败，添加错误信息
        if task.status == TaskStatus.FAILED.value and task.error_info:
            data["error_info"] = task.error_info
        
        return ResponseModel(
            code=200,
            message="获取任务状态成功",
            data=data
        )
        
    except Exception as e:
        logger.error(f"获取任务状态异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")


@router.post("/task/{task_id}/cancel", response_model=ResponseModel)
async def cancel_task(task_id: int):
    """
    取消任务
    
    Args:
        task_id: 任务ID
        
    Returns:
        ResponseModel: 取消结果
    """
    try:
        task_queue = get_task_queue()
        success = await task_queue.cancel_task(task_id)
        
        if success:
            return ResponseModel(
                code=200,
                message="任务取消成功",
                data={"task_id": task_id}
            )
        else:
            return ResponseModel(
                code=400,
                message="任务取消失败，可能任务已完成或不存在",
                data=None
            )
        
    except Exception as e:
        logger.error(f"取消任务异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")


@router.get("/task/list", response_model=ResponseModel)
async def list_tasks(
    status: Optional[str] = None,
    limit: int = 20
):
    """
    获取任务列表
    
    Args:
        status: 任务状态筛选
        limit: 返回数量限制
        
    Returns:
        ResponseModel: 任务列表
    """
    try:
        task_manager = TaskManager()
        
        # 获取任务列表
        if status:
            # 验证状态值
            try:
                task_status = TaskStatus(status)
                tasks = await task_manager.get_pending_tasks(limit) if status == "pending" else []
            except ValueError:
                return ResponseModel(
                    code=400,
                    message=f"无效的任务状态: {status}",
                    data=None
                )
        else:
            # 获取所有状态的任务
            tasks = await task_manager.get_pending_tasks(limit)
        
        # 转换为返回格式
        task_list = []
        for task in tasks:
            task_list.append({
                "task_id": task.id,
                "task_uuid": task.task_uuid,
                "task_name": task.task_name,
                "status": task.status,
                "progress": task.progress,
                "current_step": task.current_step,
                "priority": task.priority,
                "created_at": task.created_at.isoformat() if task.created_at else None
            })
        
        return ResponseModel(
            code=200,
            message="获取任务列表成功",
            data={
                "tasks": task_list,
                "total": len(task_list)
            }
        )
        
    except Exception as e:
        logger.error(f"获取任务列表异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")


@router.get("/task/queue/status", response_model=ResponseModel)
async def get_queue_status():
    """
    获取任务队列状态
    
    Returns:
        ResponseModel: 队列状态信息
    """
    try:
        task_queue = get_task_queue()
        queue_status = task_queue.get_queue_status()
        
        # 获取任务统计
        task_manager = TaskManager()
        task_stats = await task_manager.get_task_statistics()
        
        return ResponseModel(
            code=200,
            message="获取队列状态成功",
            data={
                "queue": queue_status,
                "statistics": task_stats
            }
        )
        
    except Exception as e:
        logger.error(f"获取队列状态异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取队列状态失败: {str(e)}")


@router.get("/task/scheduler/status", response_model=ResponseModel)
async def get_scheduler_status():
    """
    获取任务调度器状态
    
    Returns:
        ResponseModel: 调度器状态信息
    """
    try:
        task_scheduler = get_task_scheduler()
        scheduler_status = task_scheduler.get_scheduler_status()
        
        return ResponseModel(
            code=200,
            message="获取调度器状态成功",
            data=scheduler_status
        )
        
    except Exception as e:
        logger.error(f"获取调度器状态异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取调度器状态失败: {str(e)}")


@router.post("/task/{task_id}/retry", response_model=ResponseModel)
async def retry_task(task_id: int):
    """
    手动重试失败的任务
    
    Args:
        task_id: 任务ID
        
    Returns:
        ResponseModel: 重试结果
    """
    try:
        task_scheduler = get_task_scheduler()
        success = await task_scheduler.manually_retry_task(task_id)
        
        if success:
            return ResponseModel(
                code=200,
                message="任务已重置为待处理状态，将会重新执行",
                data={"task_id": task_id}
            )
        else:
            return ResponseModel(
                code=400,
                message="任务重试失败，请检查任务状态",
                data=None
            )
        
    except Exception as e:
        logger.error(f"重试任务异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"重试任务失败: {str(e)}")


@router.delete("/task/cleanup", response_model=ResponseModel)
async def cleanup_old_tasks(days: int = 30):
    """
    清理旧任务记录
    
    Args:
        days: 保留最近多少天的任务记录（默认30天）
        
    Returns:
        ResponseModel: 清理结果
    """
    try:
        task_manager = TaskManager()
        cleaned_count = await task_manager.clean_old_tasks(days)
        
        return ResponseModel(
            code=200,
            message=f"成功清理{cleaned_count}条旧任务记录",
            data={
                "cleaned_count": cleaned_count,
                "retain_days": days
            }
        )
        
    except Exception as e:
        logger.error(f"清理旧任务异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"清理旧任务失败: {str(e)}")