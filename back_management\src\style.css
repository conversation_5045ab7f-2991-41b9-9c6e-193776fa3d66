/* 全局样式 */
:root {
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-size: 14px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* 页面容器 */
.page-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 卡片样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

/* 表格操作列 */
.table-actions {
  display: flex;
  gap: 10px;
}

/* 统计卡片 */
.stat-card {
  text-align: center;
  padding: 20px;
}

.stat-card .stat-value {
  font-size: 28px;
  font-weight: bold;
  color: var(--primary-color);
  margin: 10px 0;
}

.stat-card .stat-label {
  color: #909399;
  font-size: 14px;
}