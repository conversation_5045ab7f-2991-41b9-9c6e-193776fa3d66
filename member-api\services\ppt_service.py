# -*- coding: utf-8 -*-
"""
PPT生成服务
提供统一的PPT生成接口，封装PPT生成逻辑
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional
import datetime

# 设置logger
logger = logging.getLogger(__name__)

# 使用绝对路径导入SimplePptGenerator
import importlib.util

# 获取项目根目录和SimplePptGenerator文件路径
project_root = Path(__file__).parent.parent
simple_ppt_generator_path = project_root / "function" / "ai-pptx" / "data_reports" / "simple_ppt_generator.py"

# 动态导入SimplePptGenerator类
try:
    spec = importlib.util.spec_from_file_location("simple_ppt_generator", simple_ppt_generator_path)
    if spec is None:
        raise ImportError(f"无法找到SimplePptGenerator文件: {simple_ppt_generator_path}")

    simple_ppt_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(simple_ppt_module)
    SimplePptGenerator = simple_ppt_module.SimplePptGenerator

    logger.info(f"成功导入SimplePptGenerator类: {SimplePptGenerator}")

except Exception as e:
    logger.error(f"导入SimplePptGenerator失败: {str(e)}")
    raise ImportError(f"无法导入SimplePptGenerator: {str(e)}")

from core.config import settings
from .oss_service import oss_service

class PPTService:
    """PPT生成服务类"""

    def __init__(self):
        """初始化PPT服务"""
        self.project_root = Path(__file__).parent.parent
        self.template_path = self.project_root / "ppt_template" / "会员数据报告-模板.pptx"
        # 基础输出目录
        self.base_output_dir = self.project_root / "uploads" / "ppt-reports"

        # 确保基础输出目录存在
        self.base_output_dir.mkdir(parents=True, exist_ok=True)

        # 初始化OSS服务
        file_config = settings.get_file_service_config()
        self.oss_service = oss_service
        self.oss_service.config.update(file_config)
        # 确保base_url正确更新
        self.oss_service.base_url = file_config.get("base_url", "http://localhost:8000")
        logger.info(f"OSS服务配置更新 - base_url: {self.oss_service.base_url}")

        logger.info(f"PPT服务初始化完成")
        logger.info(f"  - 模板路径: {self.template_path}")
        logger.info(f"  - 基础输出目录: {self.base_output_dir}")
        logger.info(f"  - OSS存储目录: {self.oss_service.upload_path}/ppt-reports")

        # 清理可能存在的旧的ppt_template目录中的生成文件
        self._cleanup_old_generated_files()

    def _cleanup_old_generated_files(self):
        """清理ppt_template目录中可能存在的旧生成文件"""
        try:
            ppt_template_dir = self.project_root / "ppt_template"
            if ppt_template_dir.exists():
                # 查找所有非模板的pptx文件
                for file_path in ppt_template_dir.glob("*.pptx"):
                    if file_path.name != "会员数据报告-模板.pptx":
                        logger.info(f"清理旧生成文件: {file_path}")
                        file_path.unlink()

                logger.info("ppt_template目录清理完成")
        except Exception as e:
            logger.warning(f"清理旧文件时出错: {e}")

    def generate_ppt_report(self, data_dict: Dict[str, Any], output_filename: Optional[str] = None, task_uuid: Optional[str] = None) -> Dict[str, Any]:
        """
        生成PPT报告

        Args:
            data_dict: 包含所有PPT参数的数据字典
            output_filename: 输出文件名（可选，默认使用时间戳）
            task_uuid: 任务UUID（用于创建独立文件夹）

        Returns:
            Dict: 生成结果，包含成功状态、文件路径等信息
        """
        try:
            logger.info("开始生成PPT报告...")

            # 1. 验证模板文件是否存在
            if not self.template_path.exists():
                error_msg = f"PPT模板文件不存在: {self.template_path}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg,
                    "file_path": None
                }

            # 2. 创建任务专属文件夹
            # 从data_dict中获取bid和task_uuid
            bid = data_dict.get('query_params', {}).get('bid', 'unknown')
            if not task_uuid:
                # 如果没有提供task_uuid，生成一个简单的唯一标识
                import uuid
                task_uuid = str(uuid.uuid4())[:8]
            
            # 创建文件夹名称：bid_uuid格式
            task_folder = f"{bid}_{task_uuid}"
            output_dir = self.base_output_dir / task_folder
            output_dir.mkdir(parents=True, exist_ok=True)
            
            logger.info(f"创建任务文件夹: {output_dir}")
            
            # 3. 生成输出文件名
            if not output_filename:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                output_filename = f"会员数据报告_品牌{bid}_{timestamp}.pptx"
            
            # 确保文件名包含.pptx扩展名
            if not output_filename.endswith('.pptx'):
                output_filename = f"{output_filename}.pptx"

            output_path = output_dir / output_filename

            # 3. 创建PPT生成器
            generator = SimplePptGenerator(str(self.template_path), str(output_path))

            # 4. 验证参数匹配情况
            logger.info("验证PPT模板参数...")
            missing_params, extra_params = generator.validate_template_params(data_dict)

            if missing_params:
                logger.warning(f"缺少的参数: {sorted(missing_params)}")

            if extra_params:
                logger.info(f"额外的参数: {sorted(extra_params)}")

            # 5. 生成PPT报告
            logger.info("开始生成PPT文件...")
            success = generator.generate_report(data_dict)

            if success:
                file_size = output_path.stat().st_size if output_path.exists() else 0
                logger.info(f"PPT报告生成成功 - 文件: {output_path}, 大小: {file_size / 1024:.1f} KB")

                # 直接记录文件信息，不需要重复复制
                # 生成访问URL
                file_url = f"/api/ppt-report/download/ppt-reports/{task_folder}/{output_filename}"
                
                # 如果需要上传到OSS
                oss_upload_success = False
                oss_url = None
                
                if self.oss_service.oss_enabled and self.oss_service.bucket:
                    try:
                        # 上传到OSS，使用任务文件夹路径
                        oss_object_key = f"ppt-reports/{task_folder}/{output_filename}"
                        result = self.oss_service.bucket.put_object_from_file(oss_object_key, str(output_path))
                        oss_url = self.oss_service._generate_oss_url(oss_object_key)
                        oss_upload_success = True
                        logger.info(f"OSS上传成功: {oss_object_key}")
                    except Exception as oss_error:
                        logger.warning(f"OSS上传失败: {oss_error}")
                
                # 构建上传结果
                upload_result = {
                    "success": True,
                    "file_url": oss_url if oss_upload_success else file_url,
                    "file_path": str(output_path),
                    "object_name": f"{task_folder}/{output_filename}",
                    "upload_time": datetime.datetime.now().isoformat(),
                    "task_folder": task_folder
                }

                if upload_result["success"]:
                    logger.info(f"PPT文件上传成功: {upload_result['file_url']}")

                    logger.info(f"文件已保存: {output_path}")

                    return {
                        "success": True,
                        "message": "PPT报告生成成功",
                        "file_path": str(upload_result.get("file_path", output_path)),  # 使用上传后的路径
                        "file_name": output_filename,
                        "file_size": file_size,
                        "file_url": upload_result["file_url"],
                        "download_url": upload_result["file_url"],
                        "object_name": upload_result["object_name"],
                        "task_folder": upload_result.get("task_folder", task_folder),
                        "upload_time": upload_result["upload_time"],
                        "missing_params": list(missing_params),
                        "extra_params": list(extra_params)
                    }
                else:
                    logger.warning(f"PPT文件上传失败: {upload_result.get('error', '未知错误')}")
                    # 即使上传失败，也返回本地文件信息
                    return {
                        "success": True,
                        "message": "PPT报告生成成功，但文件上传失败",
                        "file_path": str(output_path),
                        "file_name": output_filename,
                        "file_size": file_size,
                        "file_url": None,
                        "download_url": None,
                        "upload_error": upload_result.get("error"),
                        "missing_params": list(missing_params),
                        "extra_params": list(extra_params)
                    }
            else:
                error_msg = "PPT报告生成失败"
                logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg,
                    "file_path": None
                }

        except Exception as e:
            error_msg = f"生成PPT报告时发生异常: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "file_path": None
            }

    def validate_template_params(self, data_dict: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证模板参数

        Args:
            data_dict: 数据字典

        Returns:
            Dict: 验证结果
        """
        try:
            if not self.template_path.exists():
                return {
                    "valid": False,
                    "error": f"模板文件不存在: {self.template_path}"
                }

            generator = SimplePptGenerator(str(self.template_path), "temp.pptx")
            missing_params, extra_params = generator.validate_template_params(data_dict)

            return {
                "valid": len(missing_params) == 0,
                "missing_params": list(missing_params),
                "extra_params": list(extra_params),
                "template_params": list(generator.extract_template_params()),
                "data_params": list(data_dict.keys())
            }

        except Exception as e:
            logger.error(f"验证模板参数时发生异常: {str(e)}")
            return {
                "valid": False,
                "error": str(e)
            }

    def get_template_info(self) -> Dict[str, Any]:
        """
        获取模板信息

        Returns:
            Dict: 模板信息
        """
        try:
            if not self.template_path.exists():
                return {
                    "exists": False,
                    "path": str(self.template_path),
                    "error": "模板文件不存在"
                }

            file_size = self.template_path.stat().st_size
            file_mtime = datetime.datetime.fromtimestamp(self.template_path.stat().st_mtime)

            # 提取模板参数
            generator = SimplePptGenerator(str(self.template_path), "temp.pptx")
            template_params = generator.extract_template_params()

            return {
                "exists": True,
                "path": str(self.template_path),
                "file_size": file_size,
                "modified_time": file_mtime.strftime("%Y-%m-%d %H:%M:%S"),
                "template_params": list(template_params),
                "param_count": len(template_params)
            }

        except Exception as e:
            logger.error(f"获取模板信息时发生异常: {str(e)}")
            return {
                "exists": False,
                "path": str(self.template_path),
                "error": str(e)
            }


# 创建全局服务实例
ppt_service = PPTService()