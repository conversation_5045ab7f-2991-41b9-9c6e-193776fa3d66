# -*- coding: utf-8 -*-
"""
PPT图片参数配置文件
专门管理图片相关的常量和路径配置
"""

import logging
from typing import Dict, Any
from pathlib import Path

logger = logging.getLogger(__name__)

class PictureConstants:
    """图片参数常量类"""

    @staticmethod
    def get_image_params(bid: str = None) -> Dict[str, Any]:
        """
        获取图片参数配置

        Args:
            bid: 品牌ID，如果提供则使用动态路径，否则使用静态路径

        Returns:
            Dict: 图片参数字典
        """
        if bid:
            # 使用新的动态图片管理
            try:
                from .picture.PictureSave import create_image_manager
                image_manager = create_image_manager(bid)
                return image_manager.get_all_image_params()
            except Exception as e:
                logger.warning(f"使用动态图片管理失败，回退到静态路径: {e}")

        # 原有的静态路径逻辑作为后备方案
        from pathlib import Path

        # 获取当前文件的目录，然后构建图片目录的绝对路径
        current_file_dir = Path(__file__).parent
        project_root = current_file_dir.parent.parent  # 回到member-api目录
        images_base_path = project_root / "function" / "ai-pptx" / "data_reports" / "images"

        # 确保图片目录存在
        images_base_path.mkdir(parents=True, exist_ok=True)

        return {
            # 图片参数（参数名必须以 image_ 开头）
            "image_test": str(images_base_path / "test.PNG"),
            "image_logo": str(images_base_path / "company_logo.png"),
            "image_chart": str(images_base_path / "data_chart.png"),
            "image_trend": str(images_base_path / "member_trend.png"),
            "image_revenue": str(images_base_path / "revenue_chart.png"),
            "image_coupon": str(images_base_path / "coupon_analysis.png"),
            # 新增会员图片参数
            "image_new_member_add_last_year": str(images_base_path / "new_member_add_last_year.png"),
            "image_new_member_add_this_year": str(images_base_path / "new_member_add_this_year.png"),
            # 会员消费图片参数
            "image_member_consumption_last_year": str(images_base_path / "member_consumption_last_year.png"),
            "image_member_consumption_this_year": str(images_base_path / "member_consumption_this_year.png"),
            # 会员平均消费图片参数
            "image_avg_consumption_last_year": str(images_base_path / "avg_consumption_last_year.png"),
            "image_avg_consumption_this_year": str(images_base_path / "avg_consumption_this_year.png"),

            # 会员消费数量图片参数
            "image_consumption_num_last_year": str(images_base_path / "consumption_num_last_year.png"),
            "image_consumption_num_this_year": str(images_base_path / "consumption_num_this_year.png"),
            # 充值档位分布图片参数
            "image_charge_distribution": str(images_base_path / "charge_distribution.png"),
            # 充值频次分布图片参数
            "image_charge_frequency_distribution": str(images_base_path / "charge_frequency_distribution.png"),
            # 会员充值消费图片参数
            "image_member_charge_last_year": str(images_base_path / "member_charge_last_year.png"),
            "image_member_charge_this_year": str(images_base_path / "member_charge_this_year.png"),
            # 首次充值和消费人数图片参数
            "image_first_member_number_last_year": str(images_base_path / "first_member_number_last_year.png"),
            "image_first_member_number_this_year": str(images_base_path / "first_member_number_this_year.png"),
            # 首次充值和消费金额图片参数
            "image_first_charge_last_year": str(images_base_path / "first_charge_last_year.png"),
            "image_first_charge_this_year": str(images_base_path / "first_charge_this_year.png"),
            # 会员等级消费分析图片参数
            "image_level_consumption": str(images_base_path / "level_consumption.png"),
            # 会员等级订单分析图片参数
            "image_level_order": str(images_base_path / "level_order.png"),
            # 会员等级人数分析图片参数
            "image_level_number": str(images_base_path / "level_number.png"),
            # 会员等级消费分析AI报告参数
            "level_consumption_analysis_report": "会员等级消费分析AI报告",
            # 会员等级订单分析AI报告参数
            "level_order_analysis_report": "会员等级订单分析AI报告",
            # 会员等级人数分析AI报告参数
            "level_number_analysis_report": "会员等级人数分析AI报告",
            # 积分变化图片参数
            "image_credit_change_last_year": str(images_base_path / "credit_change_last_year.png"),
            "image_credit_change_this_year": str(images_base_path / "credit_change_this_year.png"),
            # 积分分布图片参数
            "image_credit_distribution": str(images_base_path / "charge_distribution.png"),

            # 会员消费数量AI分析报告参数
            "consumption_num_last_year_analysis_report": "",
            "consumption_num_this_year_analysis_report": "",
            # 新增会员图片AI分析参数
            "new_member_add_last_year_analysis_report": "去年新增会员数据分析报告",
            "new_member_add_this_year_analysis_report": "今年新增会员数据分析报告",
            # 会员消费图片AI分析参数
            "member_consumption_last_year_analysis_report": "去年会员消费数据分析报告",
            "member_consumption_this_year_analysis_report": "今年会员消费数据分析报告",
            # 会员平均消费图片AI分析参数
            "avg_consumption_last_year_analysis_report": "去年会员平均消费数据分析报告",
            "avg_consumption_this_year_analysis_report": "今年会员平均消费数据分析报告",
            # 会员充值消费图片AI分析参数
            "member_charge_last_year_analysis_report": "去年会员充值消费数据分析报告",
            "member_charge_this_year_analysis_report": "今年会员充值消费数据分析报告",

            # 充值档位分布AI分析参数
            "charge_distribution_analysis_report": "充值档位分布数据分析报告",
            # 充值频次分布AI分析参数
            "charge_frequency_distribution_analysis_report": "充值频次分布数据分析报告",
            # 首次充值和消费人数AI分析参数
            "first_member_number_last_year_analysis_report": "去年首次充值和消费人数分析报告",
            "first_member_number_this_year_analysis_report": "今年首次充值和消费人数分析报告",
            # 首次充值和消费金额AI分析参数
            "first_charge_last_year_analysis_report": "去年首次充值和消费金额分析报告",
            "first_charge_this_year_analysis_report": "今年首次充值和消费金额分析报告",
            # 积分变化AI分析报告参数
            "credit_change_last_year_analysis_report": "去年积分变化数据分析报告",
            "credit_change_this_year_analysis_report": "今年积分变化数据分析报告",
            # 积分分布AI分析报告参数
            "credit_distribution_analysis_report": "积分分布数据分析报告"
        }


# 创建全局实例
picture_constants = PictureConstants()

# 便捷函数
def get_image_params(bid: str = None) -> Dict[str, Any]:
    """
    便捷函数：获取图片参数

    Args:
        bid: 品牌ID，如果提供则使用动态路径

    Returns:
        Dict: 图片参数字典
    """
    return picture_constants.get_image_params(bid)