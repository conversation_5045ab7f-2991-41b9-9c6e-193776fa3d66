# -*- coding: utf-8 -*-
"""
积分分布图表生成器
负责生成积分剩余分布的可视化图表
"""

import logging
from typing import Dict, Any, List, Tuple
from pathlib import Path
import datetime

# 导入matplotlib相关库并设置后端
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import numpy as np

# 导入查询模块
from api.query.Credit.CreditStatisticsSql import CreditStatisticsQueries

# 导入数据收集装饰器
from ...excel import collect_chart_data

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class CreditDistributionPicGenerator:
    """积分分布图表生成器"""

    def __init__(self, bid: str, image_manager):
        """
        初始化图表生成器

        Args:
            bid: 品牌ID
            image_manager: 图片管理器实例
        """
        self.bid = bid
        self.image_manager = image_manager

    def _extract_param(self, query_params, key, default=None):
        """
        从查询参数中提取值

        Args:
            query_params: 查询参数对象或字典
            key: 参数键
            default: 默认值

        Returns:
            参数值
        """
        if hasattr(query_params, key):
            return getattr(query_params, key)
        elif isinstance(query_params, dict):
            return query_params.get(key, default)
        else:
            return default

    async def generate_credit_distribution_chart(self, query_params) -> Dict[str, str]:
        """
        生成积分分布图表

        Args:
            query_params: 查询参数对象或字典

        Returns:
            Dict: 包含图片路径和AI分析的字典
        """
        try:
            logger.info(f"开始生成积分分布图表 - bid: {self.bid}")
            
            # 获取日期参数
            end_date = self._extract_param(query_params, 'end_date', '2025-06-30')
            
            # 获取积分分布数据
            distribution_data = await self._fetch_distribution_data(query_params)
            
            # 结果字典
            result = {}
            
            if not distribution_data:
                logger.warning("积分分布数据为空")
                error_path = self._generate_error_image("credit_distribution", "暂无积分余额分布数据")
                if error_path:
                    result["credit_distribution"] = error_path
            else:
                # 生成图表
                chart_path = await self._generate_chart(distribution_data)
                if chart_path:
                    result["credit_distribution"] = chart_path
            
            # AI分析
            try:
                from .CreditChangeAi import CreditChangeAiAnalyzer
                ai_analyzer = CreditChangeAiAnalyzer()
                
                logger.info("开始生成积分分布AI分析...")
                
                if distribution_data:
                    analysis = await ai_analyzer.analyze_credit_distribution_data(distribution_data)
                    result["credit_distribution_analysis_report"] = analysis
                else:
                    from .CreditChangePromt import get_default_report
                    result["credit_distribution_analysis_report"] = get_default_report("no_data", "distribution")
                
                logger.info("AI分析生成完成")
                
            except Exception as ai_error:
                logger.error(f"AI分析失败: {ai_error}")
                from .CreditChangePromt import get_default_report
                result["credit_distribution_analysis_report"] = get_default_report("ai_error", "distribution")
            
            logger.info(f"积分分布图表生成完成，共 {len(result)} 个结果")
            return result

        except Exception as e:
            logger.error(f"生成积分分布图表失败: {e}")
            return {}

    def _generate_error_image(self, image_type: str, error_message: str) -> str:
        """
        生成错误提示图片

        Args:
            image_type: 图片类型
            error_message: 错误信息

        Returns:
            str: 图片文件路径
        """
        try:
            # 创建图表
            fig, ax = plt.subplots(figsize=(12, 8))
            ax.text(0.5, 0.5, f'数据获取失败\n{error_message}',
                   horizontalalignment='center', verticalalignment='center',
                   fontsize=20, color='red', weight='bold',
                   transform=ax.transAxes)
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')

            # 保存图片
            file_path = self.image_manager.get_image_path(image_type)
            plt.savefig(file_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close(fig)

            logger.info(f"错误图片已生成: {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"生成错误图片失败: {e}")
            return ""

    async def _fetch_distribution_data(self, query_params) -> List[Dict[str, Any]]:
        """
        获取积分分布数据

        Args:
            query_params: 查询参数

        Returns:
            List: 分布数据列表
        """
        try:
            bid = self._extract_param(query_params, 'bid')
            end_date = self._extract_param(query_params, 'end_date', '2025-06-30')
            
            # 转换日期格式为YYYYMMDD
            end_date_db = end_date.replace('-', '')
            
            # 调用SQL查询获取积分分布数据
            logger.info(f"调用get_credit_balance_distribution - bid: {bid}, end_date: {end_date_db}")
            raw_data = await CreditStatisticsQueries.get_credit_balance_distribution(end_date_db, bid)
            
            # 转换数据格式
            distribution_data = []
            for item in raw_data:
                distribution_data.append({
                    'range': item.get('credit_range', ''),
                    'member_count': item.get('user_count_distinct', 0),
                    'total_credit': item.get('total_credit_saving', 0),
                    'record_count': item.get('record_count', 0)
                })
            
            # 确保所有档位都有数据（补充缺失的档位）
            expected_ranges = [
                '0-199', '200-399', '400-599', '600-799', '800-999',
                '1000-1199', '1200-1399', '1400-1599', '1600-1799', 
                '1800-1999', '>=2000'
            ]
            
            existing_ranges = {item['range'] for item in distribution_data}
            for range_label in expected_ranges:
                if range_label not in existing_ranges:
                    distribution_data.append({
                        'range': range_label,
                        'member_count': 0,
                        'total_credit': 0,
                        'record_count': 0
                    })
            
            # 按照档位顺序排序
            range_order = {r: i for i, r in enumerate(expected_ranges)}
            distribution_data.sort(key=lambda x: range_order.get(x['range'], 999))
            
            logger.info(f"积分分布数据获取完成，共{len(distribution_data)}个档位")
            return distribution_data

        except Exception as e:
            logger.error(f"获取积分分布数据失败: {e}")
            # 返回默认的空档位数据
            default_ranges = [
                '0-199', '200-399', '400-599', '600-799', '800-999',
                '1000-1199', '1200-1399', '1400-1599', '1600-1799', 
                '1800-1999', '>=2000'
            ]
            return [{'range': r, 'member_count': 0, 'total_credit': 0, 'record_count': 0} 
                    for r in default_ranges]

    @collect_chart_data("credit_distribution", include_comparison=False)
    async def _generate_chart(self, data: List[Dict[str, Any]]) -> str:
        """
        生成积分分布图表

        Args:
            data: 分布数据列表

        Returns:
            str: 图片文件路径
        """
        try:
            if not data:
                logger.warning("数据为空，无法生成图表")
                return ""

            # 创建图表
            fig, ax1 = plt.subplots(figsize=(14, 8))

            # 准备数据
            ranges = [item['range'] for item in data]
            # 将总积分转换为万元单位（如果需要的话）
            total_credits = [item['total_credit'] for item in data]
            member_counts = [item['member_count'] for item in data]

            # 设置X轴位置
            x_pos = np.arange(len(ranges))

            # 绘制柱状图（积分余额总量）
            bars = ax1.bar(x_pos, total_credits, color='#4472C4', alpha=0.8, label='积分余额总量')
            
            # 设置第一个Y轴（积分） - Y轴标签为黑色
            ax1.set_xlabel('积分余额区间', fontsize=12)
            ax1.set_ylabel('积分余额总量', fontsize=12)
            ax1.tick_params(axis='y')
            ax1.set_xticks(x_pos)
            ax1.set_xticklabels(ranges, rotation=0, ha='center')
            
            # 创建第二个Y轴（人数）
            ax2 = ax1.twinx()
            
            # 绘制折线图（会员人数）
            line = ax2.plot(x_pos, member_counts, color='#E74C3C', marker='o', 
                           linewidth=2, markersize=8, label='会员人数')
            
            # 设置第二个Y轴（人数） - Y轴标签为黑色
            ax2.set_ylabel('会员人数', fontsize=12)
            ax2.tick_params(axis='y')
            
            # 添加数值标签
            for i, (bar, credit, count) in enumerate(zip(bars, total_credits, member_counts)):
                # 柱状图标签
                if credit > 0:
                    height = bar.get_height()
                    ax1.text(bar.get_x() + bar.get_width()/2., height,
                            f'{credit:,.0f}',
                            ha='center', va='bottom', fontsize=9, color='#4472C4')
                
                # 折线图标签
                if count > 0:
                    ax2.text(x_pos[i], count, f'{count}',
                            ha='center', va='bottom', fontsize=9, color='#E74C3C')
            
            # 不显示标题（图注会在上方）
            # ax1.set_title('积分余额分布', fontsize=16, fontweight='bold', pad=20)
            
            # 添加网格
            ax1.grid(True, alpha=0.3, axis='y')
            
            # 添加图例 - 放在图表外部顶部
            lines1, labels1 = ax1.get_legend_handles_labels()
            lines2, labels2 = ax2.get_legend_handles_labels()
            ax1.legend(lines1 + lines2, labels1 + labels2, 
                      bbox_to_anchor=(0.5, 1.02), loc='lower center',
                      ncol=2, fontsize=11)
            
            # 添加数据表格
            self._add_data_table(data, ax1)
            
            # 调整布局
            plt.tight_layout()
            
            # 保存图片（使用credit_distribution而不是charge_distribution）
            save_path = self.image_manager.get_image_path("credit_distribution")
            logger.info(f"准备保存图片到: {save_path}")
            
            # 确保保存目录存在
            save_dir = Path(save_path).parent
            save_dir.mkdir(parents=True, exist_ok=True)
            
            # 保存图片
            plt.savefig(save_path, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()
            
            logger.info(f"积分分布图表已生成: {save_path}")
            return save_path

        except Exception as e:
            logger.error(f"生成积分分布图表失败: {e}")
            return ""

    def _add_data_table(self, data: List[Dict[str, Any]], ax):
        """
        在图表下方添加数据表格

        Args:
            data: 数据列表
            ax: 坐标轴对象
        """
        try:
            # 准备表格数据
            ranges = [item['range'] for item in data]
            total_credits = [f"{item['total_credit']:,}" for item in data]
            member_counts = [f"{item['member_count']:,}" for item in data]
            
            # 构建表格数据
            table_data = [
                ranges,  # 第一行：积分档位
                total_credits,  # 第二行：剩余积分总额
                member_counts  # 第三行：对应人数
            ]
            
            # 行标签
            row_labels = ['积分区间', '余额总量', '会员人数']
            
            # 创建表格
            table = ax.table(
                cellText=table_data,
                rowLabels=row_labels,
                cellLoc='center',
                loc='bottom',
                bbox=[0, -0.45, 1, 0.35]  # 位置和大小
            )
            
            # 设置表格样式
            table.auto_set_font_size(False)
            table.set_fontsize(9)
            table.scale(1, 1.3)
            
            # 设置行标签样式
            for i in range(len(row_labels)):
                table[(i, -1)].set_facecolor('#4472C4')
                table[(i, -1)].set_text_props(weight='bold', color='white')
            
            # 设置数据单元格样式
            for i in range(len(row_labels)):
                for j in range(len(ranges)):
                    if i == 0:  # 档位行
                        table[(i, j)].set_facecolor('#E7F3FF')
                        table[(i, j)].set_text_props(weight='bold')
                    elif i % 2 == 1:  # 奇数行
                        table[(i, j)].set_facecolor('#F8F9FA')
                    else:  # 偶数行
                        table[(i, j)].set_facecolor('white')
            
            logger.info(f"已添加数据表格，包含 {len(ranges)} 个档位数据")
            
        except Exception as e:
            logger.error(f"添加数据表格失败: {e}")


# 工厂函数
def create_credit_distribution_pic_generator(bid: str, image_manager) -> CreditDistributionPicGenerator:
    """
    创建积分分布图表生成器

    Args:
        bid: 品牌ID
        image_manager: 图片管理器实例

    Returns:
        CreditDistributionPicGenerator: 图表生成器实例
    """
    return CreditDistributionPicGenerator(bid, image_manager)