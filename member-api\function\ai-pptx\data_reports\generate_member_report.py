#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
会员数据报告生成脚本

使用方法:
1. 修改 config.py 中的会员数据
2. 运行此脚本: python generate_member_report.py
3. 生成的报告将保存在 ppt_template 目录下，文件名包含时间戳

作者: AI Assistant
日期: 2024-07-21
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入配置和生成器
from data_reports.config import (
    TEMPLATE_PATH, 
    MEMBER_DATA, 
    get_full_output_path,
    print_current_config
)
from data_reports.simple_ppt_generator import SimplePptGenerator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('member_report_generation.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def main():
    """主函数"""
    try:
        print("🚀 开始生成会员数据报告...")
        
        # 1. 显示当前配置
        print_current_config()
        
        # 2. 检查模板文件
        if not os.path.exists(TEMPLATE_PATH):
            print(f"❌ 错误: 模板文件不存在 - {TEMPLATE_PATH}")
            print("请确保模板文件路径正确!")
            return False
        
        # 3. 获取输出路径
        output_path = get_full_output_path()
        
        # 4. 创建输出目录（如果不存在）
        output_dir = os.path.dirname(output_path)
        os.makedirs(output_dir, exist_ok=True)
        
        # 5. 创建生成器
        generator = SimplePptGenerator(TEMPLATE_PATH, output_path)
        
        # 6. 验证参数匹配
        print("\n🔍 验证模板参数...")
        generator.print_validation_report(MEMBER_DATA)
        
        # 7. 生成报告
        print("\n📊 开始生成报告...")
        success = generator.generate_report(MEMBER_DATA)
        
        if success:
            print(f"\n✅ 报告生成成功!")
            print(f"📁 文件位置: {output_path}")
            print(f"📏 文件大小: {os.path.getsize(output_path) / 1024:.1f} KB")
            
            # 检查文件是否真的存在
            if os.path.exists(output_path):
                print("✅ 文件已确认存在")
            else:
                print("❌ 警告: 文件可能未正确保存")
                
        else:
            print("\n❌ 报告生成失败!")
            print("请检查日志文件 member_report_generation.log 获取详细错误信息")
            return False
            
    except Exception as e:
        logger.error(f"生成报告时发生错误: {e}")
        print(f"\n❌ 发生错误: {e}")
        return False
    
    return True

def interactive_mode():
    """交互模式，允许用户临时修改数据"""
    print("\n🔧 进入交互模式...")
    print("您可以临时修改会员数据（不会保存到配置文件）")
    print("直接按回车使用默认值")
    
    # 复制原始数据
    temp_data = MEMBER_DATA.copy()
    
    try:
        # 时间范围
        time_frame = input(f"时间范围 (当前: {temp_data['time_frame']}): ").strip()
        if time_frame:
            temp_data['time_frame'] = time_frame
        
        # 会员总量
        total_members = input(f"会员总量 (当前: {temp_data['total_members']}): ").strip()
        if total_members and total_members.isdigit():
            temp_data['total_members'] = total_members
        
        # 新增会员
        new_members = input(f"新增会员 (当前: {temp_data['new_members']}): ").strip()
        if new_members and new_members.isdigit():
            temp_data['new_members'] = new_members
            # 重新计算历史会员
            historical = int(temp_data['total_members']) - int(new_members)
            temp_data['historical_members'] = str(historical)
        
        return temp_data
        
    except KeyboardInterrupt:
        print("\n\n用户取消操作")
        return None

if __name__ == "__main__":
    print("=" * 60)
    print("📊 会员数据报告生成器")
    print("=" * 60)
    
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        # 交互模式
        temp_data = interactive_mode()
        if temp_data:
            # 临时替换数据
            original_data = MEMBER_DATA.copy()
            MEMBER_DATA.clear()
            MEMBER_DATA.update(temp_data)
            
            success = main()
            
            # 恢复原始数据
            MEMBER_DATA.clear()
            MEMBER_DATA.update(original_data)
        else:
            print("操作已取消")
    else:
        # 普通模式
        success = main()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 任务完成!")
    else:
        print("💥 任务失败!")
    print("=" * 60)
