<template>
  <div class="init-container">
    <div class="init-card">
      <div class="init-header">
        <el-icon class="init-icon"><Setting /></el-icon>
        <h1>系统初始化</h1>
        <p>创建超级管理员账号</p>
      </div>

      <el-form 
        ref="formRef"
        :model="form" 
        :rules="rules" 
        label-width="120px"
        class="init-form"
      >
        <el-form-item label="用户名" prop="username">
          <el-input 
            v-model="form.username" 
            placeholder="请输入用户名（建议使用admin）"
            prefix-icon="User"
          />
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input 
            v-model="form.password" 
            type="password" 
            placeholder="请输入密码（至少8位）"
            show-password
            prefix-icon="Lock"
          />
        </el-form-item>

        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input 
            v-model="form.confirmPassword" 
            type="password" 
            placeholder="请再次输入密码"
            show-password
            prefix-icon="Lock"
          />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input 
            v-model="form.email" 
            placeholder="请输入邮箱地址"
            prefix-icon="Message"
          />
        </el-form-item>

        <el-form-item label="真实姓名" prop="real_name">
          <el-input 
            v-model="form.real_name" 
            placeholder="请输入真实姓名"
            prefix-icon="User"
          />
        </el-form-item>

        <el-form-item>
          <el-button 
            type="primary" 
            @click="handleSubmit"
            :loading="loading"
            class="submit-btn"
          >
            创建超级管理员
          </el-button>
        </el-form-item>
      </el-form>

      <div class="init-tips">
        <el-alert 
          title="提示"
          type="info"
          :closable="false"
        >
          <ul>
            <li>超级管理员拥有系统的最高权限</li>
            <li>请妥善保管账号密码信息</li>
            <li>系统只能有一个超级管理员初始化</li>
          </ul>
        </el-alert>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Setting, User, Lock, Message } from '@element-plus/icons-vue'
import axios from 'axios'

const router = useRouter()
const formRef = ref()
const loading = ref(false)

const form = reactive({
  username: 'admin',
  password: '',
  confirmPassword: '',
  email: '<EMAIL>',
  real_name: '系统管理员'
})

const validatePassword = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入密码'))
  } else if (value.length < 8) {
    callback(new Error('密码长度不能少于8位'))
  } else {
    callback()
  }
}

const validateConfirmPassword = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== form.password) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

const rules = reactive({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, message: '用户名至少3个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, validator: validatePassword, trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, validator: validateConfirmPassword, trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  real_name: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ]
})

// 检查是否已有超级管理员
const checkSuperAdmin = async () => {
  try {
    const response = await axios.get('/api/auth/check-super-admin')
    if (response.data.initialized) {
      ElMessage.warning('系统已初始化，即将跳转到登录页')
      setTimeout(() => {
        router.push('/login')
      }, 2000)
    }
  } catch (error) {
    console.error('检查超级管理员失败:', error)
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (!valid) return
    
    try {
      await ElMessageBox.confirm(
        '确认创建超级管理员账号？创建后将无法通过此页面再次创建。',
        '确认创建',
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      
      loading.value = true
      
      const response = await axios.post('/api/auth/init-super-admin', {
        username: form.username,
        password: form.password,
        email: form.email,
        real_name: form.real_name
      })
      
      if (response.data.success) {
        ElMessage.success('超级管理员创建成功！')
        
        // 保存token
        if (response.data.token) {
          localStorage.setItem('admin_access_token', response.data.token.access_token)
          if (response.data.token.refresh_token) {
            localStorage.setItem('admin_refresh_token', response.data.token.refresh_token)
          }
        }
        
        // 跳转到首页
        setTimeout(() => {
          router.push('/')
        }, 1500)
      } else {
        ElMessage.error(response.data.message || '创建失败')
      }
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error(error.response?.data?.detail || '创建失败，请重试')
      }
    } finally {
      loading.value = false
    }
  })
}

onMounted(() => {
  checkSuperAdmin()
})
</script>

<style scoped>
.init-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.init-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 600px;
  padding: 40px;
}

.init-header {
  text-align: center;
  margin-bottom: 40px;
}

.init-icon {
  font-size: 48px;
  color: #667eea;
  margin-bottom: 20px;
}

.init-header h1 {
  font-size: 28px;
  color: #333;
  margin: 10px 0;
}

.init-header p {
  color: #666;
  font-size: 16px;
}

.init-form {
  margin-bottom: 30px;
}

.submit-btn {
  width: 100%;
  height: 45px;
  font-size: 16px;
}

.init-tips {
  margin-top: 30px;
}

.init-tips ul {
  margin: 10px 0;
  padding-left: 20px;
}

.init-tips li {
  margin: 5px 0;
  color: #666;
}

:deep(.el-form-item__label) {
  font-weight: bold;
}

:deep(.el-input__wrapper) {
  height: 40px;
}
</style>