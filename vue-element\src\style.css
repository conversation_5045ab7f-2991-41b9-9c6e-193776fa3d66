/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 基础样式 */
html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f5f7fa;
}

#app {
  height: 100vh;
  overflow: auto;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 文本选择样式 */
::selection {
  background-color: #409eff;
  color: white;
}

/* 链接样式 */
a {
  color: #409eff;
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: #337ecc;
}

/* 表格样式优化 */
.el-table {
  font-size: 14px;
}

.el-table th {
  background-color: #fafafa !important;
  color: #606266 !important;
  font-weight: 600 !important;
}

.el-table td {
  border-bottom: 1px solid #ebeef5;
}

.el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: #fafafa;
}

/* 卡片样式优化 */
.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: none;
}

.el-card__header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f2f5;
  background-color: #fafafa;
}

.el-card__body {
  padding: 20px;
}

/* 按钮样式优化 */
.el-button {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.el-button--primary {
  background: linear-gradient(135deg, #409eff 0%, #5dade2 100%);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #337ecc 0%, #4a90c2 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

/* 表单样式优化 */
.el-form-item__label {
  font-weight: 500;
  color: #606266;
}

.el-input__wrapper {
  border-radius: 6px;
  transition: all 0.3s ease;
}

.el-input__wrapper:hover {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

.el-input__wrapper.is-focus {
  box-shadow: 0 0 0 1px #409eff inset;
}

/* 选择器样式优化 */
.el-select .el-input__wrapper {
  border-radius: 6px;
}

.el-select-dropdown {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 标签样式优化 */
.el-tag {
  border-radius: 4px;
  font-weight: 500;
  border: none;
}

.el-tag--primary {
  background-color: #ecf5ff;
  color: #409eff;
}

.el-tag--success {
  background-color: #f0f9ff;
  color: #67c23a;
}

.el-tag--warning {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.el-tag--danger {
  background-color: #fef0f0;
  color: #f56c6c;
}

/* 消息提示样式优化 */
.el-message {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 加载样式优化 */
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.9);
}

.el-loading-spinner {
  color: #409eff;
}

/* 空状态样式 */
.el-empty {
  padding: 40px 0;
}

.el-empty__image {
  width: 160px;
  height: 160px;
}

.el-empty__description {
  color: #909399;
  font-size: 14px;
  margin-top: 16px;
}

/* 面包屑样式 */
.el-breadcrumb {
  font-size: 14px;
}

.el-breadcrumb__item {
  color: #606266;
}

.el-breadcrumb__item:last-child .el-breadcrumb__inner {
  color: #303133;
  font-weight: 500;
}

/* 菜单样式优化 */
.el-menu {
  border: none;
}

.el-menu-item {
  border-radius: 6px;
  margin: 4px 8px;
  transition: all 0.3s ease;
}

.el-menu-item:hover {
  background-color: rgba(64, 158, 255, 0.1) !important;
  color: #409eff !important;
}

.el-menu-item.is-active {
  background-color: #409eff !important;
  color: white !important;
}

/* 工具提示样式 */
.el-tooltip__popper {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 响应式工具类 */
@media (max-width: 768px) {
  .hidden-sm-and-down {
    display: none !important;
  }
}

@media (max-width: 992px) {
  .hidden-md-and-down {
    display: none !important;
  }
}

/* 动画类 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(20px);
  opacity: 0;
}

/* 自定义工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-16 {
  margin-bottom: 16px;
}

.mb-24 {
  margin-bottom: 24px;
}

.mt-16 {
  margin-top: 16px;
}

.mt-24 {
  margin-top: 24px;
}

.p-16 {
  padding: 16px;
}

.p-24 {
  padding: 24px;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}
