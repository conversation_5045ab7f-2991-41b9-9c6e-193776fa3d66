# -*- coding: utf-8 -*-
"""
PPT报告历史记录服务
用于管理和查询用户的PPT生成历史
"""

import logging
import pymysql
from typing import Dict, Any, List, Optional
from datetime import datetime
from contextlib import contextmanager
from core.config import settings

logger = logging.getLogger(__name__)


class PPTHistoryService:
    """PPT历史记录服务类"""
    
    def __init__(self):
        # 根据环境获取表名
        self.is_beta = settings.ENVIRONMENT.lower() in ['beta', 'test', 'testing']
        self.table_suffix = '_beta' if self.is_beta else ''
        self.table_name = f'member_report_tasks{self.table_suffix}'
        logger.info(f"PPT历史记录服务初始化，使用表名: {self.table_name}")
        
        self.status_map = {
            'pending': '等待中',
            'running': '生成中',
            'completed': '已完成',
            'failed': '失败',
            'cancelled': '已取消'
        }
    
    @contextmanager
    def get_db_connection(self):
        """获取数据库连接"""
        connection = None
        try:
            connection = pymysql.connect(
                host=settings.TASK_MANAGEMENT_DB_HOST,
                port=settings.TASK_MANAGEMENT_DB_PORT,
                user=settings.TASK_MANAGEMENT_DB_USER,
                password=settings.TASK_MANAGEMENT_DB_PASSWORD,
                database=settings.TASK_MANAGEMENT_DB_NAME,
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            yield connection
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            raise
        finally:
            if connection:
                connection.close()
    
    async def get_user_history(
        self,
        username: str,
        skip: int = 0,
        limit: int = 50,
        bid: Optional[str] = None,
        status: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取用户的PPT生成历史记录
        
        Args:
            username: 用户名
            skip: 跳过的记录数
            limit: 返回的记录数限制
            bid: 品牌ID过滤（可选）
            status: 状态过滤（可选）
        
        Returns:
            包含历史记录列表和总数的字典
        """
        try:
            # 构建查询条件 - 使用user_id字段
            where_conditions = ["user_id = %s"]
            params = [username]
            
            if bid:
                where_conditions.append("bid = %s")
                params.append(bid)
            
            if status:
                where_conditions.append("status = %s")
                params.append(status)
            
            where_clause = " AND ".join(where_conditions)
            
            # 查询SQL - 使用动态表名
            query = f"""
                SELECT 
                    id,
                    task_uuid as task_id,
                    user_id,
                    bid,
                    sid,
                    ppt_oss_url,
                    excel_oss_url,
                    status,
                    task_start_ftime,
                    task_end_ftime,
                    created_at,
                    started_at,
                    completed_at,
                    error_info,
                    progress as completed_steps,
                    100 as total_steps,
                    task_name as query_params
                FROM {self.table_name}
                WHERE {where_clause}
                ORDER BY created_at DESC
                LIMIT %s OFFSET %s
            """
            
            # 获取总数
            count_query = f"""
                SELECT COUNT(*) as total
                FROM {self.table_name}
                WHERE {where_clause}
            """
            
            with self.get_db_connection() as conn:
                with conn.cursor() as cursor:
                    # 获取总数
                    cursor.execute(count_query, params)
                    total_result = cursor.fetchone()
                    total = total_result["total"] if total_result else 0
                    
                    # 获取历史记录
                    params.extend([limit, skip])
                    cursor.execute(query, params)
                    rows = cursor.fetchall()
                    
                    # 格式化数据
                    history_list = self._format_history_list(rows)
            
            return {
                'total': total,
                'list': history_list,
                'skip': skip,
                'limit': limit
            }
            
        except Exception as e:
            logger.error(f"获取用户PPT历史记录失败: {str(e)}", exc_info=True)
            raise Exception(f"获取历史记录失败: {str(e)}")
    
    async def get_task_detail(self, task_id: str, username: str) -> Optional[Dict[str, Any]]:
        """
        获取单个任务的详细信息
        
        Args:
            task_id: 任务UUID
            username: 用户名（用于权限验证）
        
        Returns:
            任务详细信息或None
        """
        try:
            query = f"""
                SELECT 
                    id,
                    task_uuid as task_id,
                    user_id,
                    bid,
                    sid,
                    ppt_oss_url,
                    excel_oss_url,
                    status,
                    task_start_ftime,
                    task_end_ftime,
                    created_at,
                    started_at,
                    completed_at,
                    error_info,
                    progress as completed_steps,
                    100 as total_steps,
                    task_name as query_params,
                    result_file_url as result_data
                FROM {self.table_name}
                WHERE task_uuid = %s AND user_id = %s
            """
            
            with self.get_db_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(query, (task_id, username))
                    row = cursor.fetchone()
                    
                    if not row:
                        return None
                    
                    # 格式化单个记录
                    history_item = self._format_history_item(row)
                    
                    # 添加额外的详细信息
                    if row.get('query_params'):
                        import json
                        try:
                            history_item['query_params'] = json.loads(row['query_params'])
                        except:
                            history_item['query_params'] = row['query_params']
                    
                    if row.get('result_data'):
                        import json
                        try:
                            history_item['result_data'] = json.loads(row['result_data'])
                        except:
                            history_item['result_data'] = row['result_data']
                    
                    return history_item
            
        except Exception as e:
            logger.error(f"获取任务详情失败: {str(e)}", exc_info=True)
            raise Exception(f"获取任务详情失败: {str(e)}")
    
    async def delete_task(self, task_id: str, username: str) -> bool:
        """
        删除指定的任务记录
        
        Args:
            task_id: 任务UUID
            username: 用户名（用于权限验证）
        
        Returns:
            是否删除成功
        """
        try:
            # 只能删除已完成或失败的任务
            check_query = f"""
                SELECT status FROM {self.table_name}
                WHERE task_uuid = %s AND user_id = %s
            """
            
            delete_query = f"""
                DELETE FROM {self.table_name}
                WHERE task_uuid = %s AND user_id = %s 
                AND status IN ('completed', 'failed', 'cancelled')
            """
            
            with self.get_db_connection() as conn:
                with conn.cursor() as cursor:
                    # 检查任务状态
                    cursor.execute(check_query, (task_id, username))
                    result = cursor.fetchone()
                    
                    if not result:
                        raise Exception("任务不存在或无权限删除")
                    
                    if result['status'] not in ['completed', 'failed', 'cancelled']:
                        raise Exception(f"不能删除状态为 {self.status_map.get(result['status'], result['status'])} 的任务")
                    
                    # 执行删除
                    cursor.execute(delete_query, (task_id, username))
                    conn.commit()
                    
                    return cursor.rowcount > 0
            
        except Exception as e:
            logger.error(f"删除任务失败: {str(e)}", exc_info=True)
            raise Exception(f"删除任务失败: {str(e)}")
    
    async def get_statistics(self, username: str) -> Dict[str, Any]:
        """
        获取用户的PPT生成统计信息
        
        Args:
            username: 用户名
        
        Returns:
            统计信息字典
        """
        try:
            query = f"""
                SELECT 
                    COUNT(*) as total_count,
                    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
                    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count,
                    COUNT(CASE WHEN status = 'running' THEN 1 END) as running_count,
                    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
                    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_count,
                    COUNT(CASE WHEN DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as week_count,
                    COUNT(CASE WHEN DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 END) as month_count
                FROM {self.table_name}
                WHERE user_id = %s
            """
            
            with self.get_db_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(query, (username,))
                    result = cursor.fetchone()
                    
                    if not result:
                        return {
                            'total_count': 0,
                            'completed_count': 0,
                            'failed_count': 0,
                            'running_count': 0,
                            'pending_count': 0,
                            'today_count': 0,
                            'week_count': 0,
                            'month_count': 0
                        }
                    
                    return dict(result)
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {str(e)}", exc_info=True)
            raise Exception(f"获取统计信息失败: {str(e)}")
    
    def _format_history_list(self, rows: List[Dict]) -> List[Dict[str, Any]]:
        """格式化历史记录列表"""
        return [self._format_history_item(row) for row in rows]
    
    def _format_history_item(self, row: Dict) -> Dict[str, Any]:
        """格式化单个历史记录"""
        # 处理task_start_ftime和task_end_ftime，它们是字符串格式
        task_start = row.get('task_start_ftime') or '-'
        task_end = row.get('task_end_ftime') or '-'
        
        return {
            'id': row.get('id'),
            'task_id': row.get('task_id'),
            'username': row.get('user_id'),  # 返回user_id作为username
            'bid': row.get('bid'),
            'sid': row.get('sid') or '-',
            'ppt_oss_url': row.get('ppt_oss_url') or '',
            'excel_oss_url': row.get('excel_oss_url') or '',
            'status': row.get('status'),
            'status_text': self.status_map.get(row.get('status'), row.get('status')),
            'task_start_ftime': task_start if task_start != '-' else '-',
            'task_end_ftime': task_end if task_end != '-' else '-',
            'created_at': row.get('created_at').strftime('%Y-%m-%d %H:%M:%S') if row.get('created_at') else '-',
            'started_at': row.get('started_at').strftime('%Y-%m-%d %H:%M:%S') if row.get('started_at') else '-',
            'completed_at': row.get('completed_at').strftime('%Y-%m-%d %H:%M:%S') if row.get('completed_at') else '-',
            'error_info': row.get('error_info') or '',
            'progress': f"{row.get('completed_steps')}/{row.get('total_steps')}" if row.get('total_steps') else '-',
            # 计算耗时
            'duration': self._calculate_duration(row.get('started_at'), row.get('completed_at'))
        }
    
    def _calculate_duration(self, started_at: Optional[datetime], completed_at: Optional[datetime]) -> str:
        """计算任务耗时"""
        if not started_at or not completed_at:
            return '-'
        
        duration = completed_at - started_at
        total_seconds = int(duration.total_seconds())
        
        if total_seconds < 60:
            return f"{total_seconds}秒"
        elif total_seconds < 3600:
            minutes = total_seconds // 60
            seconds = total_seconds % 60
            return f"{minutes}分{seconds}秒"
        else:
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            return f"{hours}小时{minutes}分"


# 创建服务实例
ppt_history_service = PPTHistoryService()