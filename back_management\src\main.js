import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

import App from './App.vue'
import router from './router'
import './style.css'

const app = createApp(App)

// 必须先初始化Pinia，因为router需要使用store
app.use(createPinia())

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 然后初始化router
app.use(router)

// 最后初始化ElementPlus
app.use(ElementPlus, {
  locale: zhCn,
})

// 直接挂载应用，认证初始化在路由守卫中处理
app.mount('#app')