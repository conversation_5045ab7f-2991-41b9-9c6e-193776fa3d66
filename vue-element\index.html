<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="会员报表系统 - 支持多时间维度的会员数据查询与对比分析" />
    <meta name="keywords" content="会员报表,数据分析,周报,月报,季报,半年报" />
    <meta name="author" content="Member Report Team" />
    <title>会员报表系统</title>
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- 样式预加载 -->
    <style>
      /* 页面加载动画 */
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        color: white;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
      }
      
      .loading-text {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 8px;
      }
      
      .loading-subtitle {
        font-size: 14px;
        opacity: 0.8;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* 隐藏加载动画 */
      .loading-hidden {
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.5s ease, visibility 0.5s ease;
      }
      
      /* 基础样式重置 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #f5f7fa;
      }
      
      #app {
        min-height: 100vh;
      }
    </style>
  </head>
  <body>
    <!-- 页面加载动画 -->
    <div id="loading">
      <div class="loading-spinner"></div>
      <div class="loading-text">会员报表系统</div>
      <div class="loading-subtitle">正在加载中...</div>
    </div>
    
    <!-- Vue应用挂载点 -->
    <div id="app"></div>
    
    <!-- 主脚本 -->
    <script type="module" src="/src/main.js"></script>
    
    <!-- 加载完成后隐藏动画 -->
    <script>
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.classList.add('loading-hidden');
            setTimeout(function() {
              loading.remove();
            }, 500);
          }
        }, 1000); // 延迟1秒显示加载效果
      });
      
      // 错误处理
      window.addEventListener('error', function(e) {
        console.error('页面加载错误:', e.error);
      });
      
      // 未处理的Promise错误
      window.addEventListener('unhandledrejection', function(e) {
        console.error('未处理的Promise错误:', e.reason);
      });
    </script>
  </body>
</html>
