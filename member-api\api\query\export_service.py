"""
数据导出服务
支持将查询结果导出为Excel格式
"""
import io
import re
import logging
from typing import Dict, Any, Optional
from datetime import datetime
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter

from core.models import QueryParams
from constant import MEMBER_BASE_MODULE, MEMBER_CONSUME_MODULE, MEMBER_CHARGE_MODULE

logger = logging.getLogger(__name__)


class ExportService:
    """数据导出服务类"""
    
    def __init__(self):
        self.member_data_service = None
    
    def _get_member_data_service(self):
        """延迟导入，避免循环依赖"""
        if self.member_data_service is None:
            from .MemberDataQuery import member_data_service
            self.member_data_service = member_data_service
        return self.member_data_service
    
    async def export_member_data(
        self,
        query_params: QueryParams,
        export_type: str = "all"
    ) -> bytes:
        """
        导出会员数据为Excel
        
        Args:
            query_params: 查询参数
            export_type: 导出类型 (all/member_base/member_consume/member_charge/pinzhi_cashier)
            
        Returns:
            Excel文件的字节流
        """
        try:
            # 获取服务实例
            service = self._get_member_data_service()
            
            # 根据导出类型获取数据
            if export_type == "all":
                data = await service.get_all_member_data(query_params)
            elif export_type == "member_base":
                data = await service.member_base_service.get_member_base_data(query_params)
            elif export_type == "member_consume":
                data = await service.member_consume_service.get_member_consume_data(query_params)
            elif export_type == "member_charge":
                data = await service.member_charge_service.get_member_charge_data(query_params)
            elif export_type == "pinzhi_cashier":
                data = await service.pinzhi_cashier_service.get_pinzhi_cashier_data(query_params)
            else:
                raise ValueError(f"不支持的导出类型: {export_type}")
            
            # 打印数据结构用于调试
            logger.info(f"导出数据类型: {export_type}")
            if data:
                logger.info(f"数据类型: {type(data)}")
                if hasattr(data, '__dict__'):
                    logger.info(f"数据属性: {list(data.__dict__.keys())}")
                    # 打印第一个属性的详细信息
                    for key in list(data.__dict__.keys())[:1]:
                        attr_value = getattr(data, key)
                        if attr_value and hasattr(attr_value, '__dict__'):
                            logger.info(f"属性 {key} 的子属性: {list(attr_value.__dict__.keys())}")
            
            # 创建Excel工作簿
            workbook = Workbook()
            
            # 根据数据类型创建不同的工作表
            if export_type == "all" and data:
                # 导出所有数据时，创建多个工作表
                self._create_member_base_sheet(workbook, data.member_base if hasattr(data, 'member_base') else None)
                self._create_member_consume_sheet(workbook, data.member_consume if hasattr(data, 'member_consume') else None)
                self._create_member_charge_sheet(workbook, data.member_charge if hasattr(data, 'member_charge') else None)
                if hasattr(data, 'pinzhi_cashier') and data.pinzhi_cashier:
                    self._create_pinzhi_sheet(workbook, data.pinzhi_cashier)
                
                # 删除默认的空工作表
                if "Sheet" in workbook.sheetnames:
                    workbook.remove(workbook["Sheet"])
            else:
                # 导出单个模块数据
                if export_type == "member_base":
                    self._create_member_base_sheet(workbook, data)
                elif export_type == "member_consume":
                    self._create_member_consume_sheet(workbook, data)
                elif export_type == "member_charge":
                    self._create_member_charge_sheet(workbook, data)
                elif export_type == "pinzhi_cashier":
                    self._create_pinzhi_sheet(workbook, data)
                
                # 删除默认的空工作表
                if "Sheet" in workbook.sheetnames:
                    workbook.remove(workbook["Sheet"])
            
            # 添加查询信息工作表
            self._create_query_info_sheet(workbook, query_params)
            
            # 保存到字节流
            output = io.BytesIO()
            workbook.save(output)
            output.seek(0)
            
            return output.getvalue()
            
        except Exception as e:
            logger.error(f"导出数据失败: {e}")
            raise
    
    def _create_member_base_sheet(self, workbook: Workbook, data: Optional[Any]):
        """创建会员基础数据工作表"""
        ws = workbook.create_sheet("会员基础数据")
        
        # 设置表头样式
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        
        # 添加表头
        headers = ["指标名称", "实际值", "单位", "环比值", "环比变化率", "同比值", "同比变化率", "计算逻辑"]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
        
        # 添加数据
        if data:
            row = 2
            for field_name, field_config in MEMBER_BASE_MODULE.items():
                key = field_config.get('key', '')
                # 将驼峰转换为下划线格式
                attr_name = re.sub('([A-Z])', r'_\1', key).lower().lstrip('_')
                if hasattr(data, attr_name):
                    item_data = getattr(data, attr_name)
                    if item_data:
                        ws.cell(row=row, column=1, value=field_name)
                        ws.cell(row=row, column=2, value=item_data.value if hasattr(item_data, 'value') else 0)
                        ws.cell(row=row, column=3, value=item_data.unit if hasattr(item_data, 'unit') else '')
                        ws.cell(row=row, column=4, value=item_data.chain_comparison[0] if hasattr(item_data, 'chain_comparison') and item_data.chain_comparison else 0)
                        ws.cell(row=row, column=5, value=item_data.chain_change_rate[0] if hasattr(item_data, 'chain_change_rate') and item_data.chain_change_rate else '0%')
                        ws.cell(row=row, column=6, value=item_data.year_over_year if hasattr(item_data, 'year_over_year') else 0)
                        ws.cell(row=row, column=7, value=item_data.year_over_year_rate if hasattr(item_data, 'year_over_year_rate') else '0%')
                        ws.cell(row=row, column=8, value=field_config.get('logic', ''))
                        row += 1
        
        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def _create_member_consume_sheet(self, workbook: Workbook, data: Optional[Any]):
        """创建会员消费数据工作表"""
        ws = workbook.create_sheet("会员消费数据")
        
        # 设置表头样式
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        
        # 添加表头
        headers = ["指标名称", "实际值", "单位", "环比值", "环比变化率", "同比值", "同比变化率", "计算逻辑"]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
        
        # 添加数据
        if data:
            row = 2
            for field_name, field_config in MEMBER_CONSUME_MODULE.items():
                key = field_config.get('key', '')
                # 将驼峰转换为下划线格式
                attr_name = re.sub('([A-Z])', r'_\1', key).lower().lstrip('_')
                if hasattr(data, attr_name):
                    item_data = getattr(data, attr_name)
                    if item_data:
                        ws.cell(row=row, column=1, value=field_name)
                        ws.cell(row=row, column=2, value=item_data.value if hasattr(item_data, 'value') else 0)
                        ws.cell(row=row, column=3, value=item_data.unit if hasattr(item_data, 'unit') else '')
                        ws.cell(row=row, column=4, value=item_data.chain_comparison[0] if hasattr(item_data, 'chain_comparison') and item_data.chain_comparison else 0)
                        ws.cell(row=row, column=5, value=item_data.chain_change_rate[0] if hasattr(item_data, 'chain_change_rate') and item_data.chain_change_rate else '0%')
                        ws.cell(row=row, column=6, value=item_data.year_over_year if hasattr(item_data, 'year_over_year') else 0)
                        ws.cell(row=row, column=7, value=item_data.year_over_year_rate if hasattr(item_data, 'year_over_year_rate') else '0%')
                        ws.cell(row=row, column=8, value=field_config.get('logic', ''))
                        row += 1
        
        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def _create_member_charge_sheet(self, workbook: Workbook, data: Optional[Any]):
        """创建会员充值数据工作表"""
        ws = workbook.create_sheet("会员充值数据")
        
        # 设置表头样式
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        
        # 添加表头
        headers = ["指标名称", "实际值", "单位", "环比值", "环比变化率", "同比值", "同比变化率", "计算逻辑"]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
        
        # 添加数据
        if data:
            row = 2
            for field_name, field_config in MEMBER_CHARGE_MODULE.items():
                key = field_config.get('key', '')
                # 将驼峰转换为下划线格式
                attr_name = re.sub('([A-Z])', r'_\1', key).lower().lstrip('_')
                if hasattr(data, attr_name):
                    item_data = getattr(data, attr_name)
                    if item_data:
                        ws.cell(row=row, column=1, value=field_name)
                        ws.cell(row=row, column=2, value=item_data.value if hasattr(item_data, 'value') else 0)
                        ws.cell(row=row, column=3, value=item_data.unit if hasattr(item_data, 'unit') else '')
                        ws.cell(row=row, column=4, value=item_data.chain_comparison[0] if hasattr(item_data, 'chain_comparison') and item_data.chain_comparison else 0)
                        ws.cell(row=row, column=5, value=item_data.chain_change_rate[0] if hasattr(item_data, 'chain_change_rate') and item_data.chain_change_rate else '0%')
                        ws.cell(row=row, column=6, value=item_data.year_over_year if hasattr(item_data, 'year_over_year') else 0)
                        ws.cell(row=row, column=7, value=item_data.year_over_year_rate if hasattr(item_data, 'year_over_year_rate') else '0%')
                        ws.cell(row=row, column=8, value=field_config.get('logic', ''))
                        row += 1
        
        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def _create_pinzhi_sheet(self, workbook: Workbook, data: Optional[Any]):
        """创建品智收银数据工作表"""
        ws = workbook.create_sheet("品智收银数据")
        
        # 设置表头样式
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        
        # 添加表头
        headers = ["指标名称", "实际值", "单位", "备注"]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
        
        # 添加数据（品智数据结构可能不同，需要根据实际情况调整）
        if data:
            row = 2
            if isinstance(data, dict):
                for key, value in data.items():
                    ws.cell(row=row, column=1, value=key)
                    ws.cell(row=row, column=2, value=str(value))
                    row += 1
        
        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def _create_query_info_sheet(self, workbook: Workbook, query_params: QueryParams):
        """创建查询信息工作表"""
        ws = workbook.create_sheet("查询信息", 0)  # 放在第一个位置
        
        # 设置样式
        title_font = Font(bold=True, size=14)
        label_font = Font(bold=True)
        
        # 添加标题
        ws.cell(row=1, column=1, value="会员数据导出报告").font = title_font
        
        # 添加查询参数信息
        row = 3
        info_items = [
            ("导出时间", datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
            ("品牌ID", query_params.bid or "全部"),
            ("门店ID", query_params.sid or "全部"),
            ("开始日期", query_params.start_date or "未指定"),
            ("结束日期", query_params.end_date or "未指定"),
            ("查询类型", query_params.query_type or "自定义"),
        ]
        
        for label, value in info_items:
            ws.cell(row=row, column=1, value=label).font = label_font
            ws.cell(row=row, column=2, value=value)
            row += 1
        
        # 自动调整列宽
        ws.column_dimensions['A'].width = 15
        ws.column_dimensions['B'].width = 30


# 创建服务实例
export_service = ExportService()