# 会员报表系统数据库表结构文档

## 数据库：ai_yuce

### 认证模块表 (member-api\auth\database\complete_schema.sql)

#### 1. 用户表 (member_report_users)

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|--------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | - | 用户ID |
| username | VARCHAR(50) | NOT NULL, UNIQUE | - | 用户名 |
| email | VARCHAR(100) | UNIQUE | NULL | 邮箱 |
| password | VARCHAR(255) | NOT NULL | - | 密码哈希 |
| real_name | VARCHAR(100) | - | NULL | 真实姓名 |
| phone | VARCHAR(20) | - | NULL | 电话 |
| department | VARCHAR(100) | - | NULL | 部门 |
| role | ENUM('super_admin','admin','user') | NOT NULL | 'user' | 角色：超级管理员/管理员/普通用户 |
| account_type | ENUM('regular_agent','direct_agent','aoqiwei_operation','merchant_super_admin','merchant_user') | - | 'merchant_user' | 账户类别：常规代理商/直营代理商/奥琦玮直管运营/商户超级管理员/商户普通号 |
| expire_time | DATETIME | - | NULL | 账户到期时间（NULL表示永久有效） |
| status | ENUM('active','inactive','banned') | NOT NULL | 'active' | 状态：激活/禁用/封禁 |
| quotas | INT | - | 0 | PPT生成配额限制（0表示无限制） |
| use_quotas | INT | - | 0 | PPT已使用次数 |
| avatar_url | VARCHAR(500) | - | NULL | 头像URL |
| last_login_time | DATETIME | - | NULL | 最后登录时间 |
| login_count | INT | - | 0 | 登录次数 |
| failed_login_count | INT | - | 0 | 失败登录次数 |
| last_failed_login_time | DATETIME | - | NULL | 最后失败登录时间 |
| created_at | DATETIME | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |
| deleted_at | DATETIME | - | NULL | 删除时间 |

#### 2. 会话表 (member_report_sessions)

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|--------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | - | 会话ID |
| user_id | INT | NOT NULL, FOREIGN KEY | - | 用户ID |
| token | VARCHAR(500) | NOT NULL, UNIQUE | - | JWT令牌 |
| refresh_token | VARCHAR(500) | UNIQUE | NULL | 刷新令牌 |
| refresh_expires_at | DATETIME | - | NULL | 刷新令牌过期时间 |
| ip_address | VARCHAR(45) | - | NULL | IP地址 |
| user_agent | TEXT | - | NULL | 用户代理 |
| is_active | BOOLEAN | - | TRUE | 是否活跃 |
| expires_at | DATETIME | NOT NULL | - | 过期时间 |
| created_at | DATETIME | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

#### 3. 审计日志表 (member_report_audit_logs)

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|--------|------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | - | 日志ID |
| user_id | INT | - | NULL | 用户ID |
| username | VARCHAR(50) | - | NULL | 用户名 |
| action | VARCHAR(100) | NOT NULL | - | 操作动作 |
| resource | VARCHAR(200) | - | NULL | 资源 |
| resource_id | VARCHAR(100) | - | NULL | 资源ID |
| ip_address | VARCHAR(45) | - | NULL | IP地址 |
| user_agent | TEXT | - | NULL | 用户代理 |
| request_data | JSON | - | NULL | 请求数据 |
| response_data | JSON | - | NULL | 响应数据 |
| status | ENUM('success','failure') | - | NULL | 状态 |
| error_message | TEXT | - | NULL | 错误信息 |
| created_at | DATETIME | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

#### 4. BID配置表 (member_report_bid_configs)

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|--------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | - | 配置ID |
| bid | VARCHAR(50) | NOT NULL, UNIQUE | - | BID代码 |
| bid_name | VARCHAR(100) | NOT NULL | '' | BID名称 |
| description | TEXT | - | NULL | 描述 |
| business_type | ENUM('fast_food','casual_dining','fine_dining') | - | NULL | 业态标签：快餐/休闲餐/正餐 |
| owner_username | VARCHAR(50) | FOREIGN KEY | NULL | 所有者用户名 |
| status | ENUM('pending','approved','rejected','active','inactive') | NOT NULL | 'pending' | 状态 |
| apply_reason | TEXT | - | NULL | 申请理由 |
| reject_reason | TEXT | - | NULL | 拒绝理由 |
| approved_by | VARCHAR(50) | - | NULL | 审批人用户名 |
| approved_at | DATETIME | - | NULL | 审批时间 |
| sid_list | JSON | - | NULL | SID列表 |
| contact_name | VARCHAR(100) | - | NULL | 联系人 |
| contact_phone | VARCHAR(20) | - | NULL | 联系电话 |
| contact_email | VARCHAR(100) | - | NULL | 联系邮箱 |
| company_name | VARCHAR(200) | - | NULL | 公司名称 |
| industry | VARCHAR(100) | - | NULL | 行业 |
| remark | TEXT | - | NULL | 备注 |
| created_at | DATETIME | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |
| deleted_at | DATETIME | - | NULL | 删除时间 |

#### 5. 用户BID权限关联表 (member_report_user_bid_permissions)

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|--------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | - | 权限ID |
| username | VARCHAR(50) | NOT NULL, FOREIGN KEY | - | 用户名 |
| bid | VARCHAR(50) | NOT NULL, FOREIGN KEY | - | BID代码 |
| permissions | JSON | - | NULL | 权限列表 |
| sid_filter | JSON | - | NULL | SID过滤器 |
| granted_by | VARCHAR(50) | - | NULL | 授权人 |
| grant_reason | TEXT | - | NULL | 授权原因 |
| expire_at | DATETIME | - | NULL | 过期时间 |
| status | ENUM('active','inactive') | NOT NULL | 'active' | 状态 |
| created_at | DATETIME | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

### 任务管理模块表 (member-api\api\PPTreport\task_management\database_init.py)

#### 6. PPT任务表 (member_report_tasks)

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|--------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | - | 任务ID |
| task_uuid | VARCHAR(36) | UNIQUE, NOT NULL | - | 任务UUID |
| user_id | VARCHAR(50) | - | NULL | 用户标识 |
| bid | VARCHAR(50) | - | NULL | BID代码 |
| sid | VARCHAR(255) | - | NULL | SID标识 |
| task_type | VARCHAR(50) | - | NULL | 任务类型 |
| task_name | VARCHAR(200) | - | NULL | 任务名称 |
| file_excel_size | BIGINT | - | 0 | Excel文件大小 |
| file_PPT_size | BIGINT | - | 0 | PPT文件大小 |
| priority | TINYINT | - | 1 | 优先级 |
| result_file_url | TEXT | - | NULL | 结果文件URL |
| ppt_oss_url | TEXT | - | NULL | PPT OSS URL |
| excel_oss_url | TEXT | - | NULL | Excel OSS URL |
| report_type | VARCHAR(50) | - | NULL | 报告类型 |
| status | ENUM('pending','processing','completed','failed','cancelled') | - | 'pending' | 状态 |
| progress | TINYINT | - | 0 | 进度 |
| current_step | VARCHAR(100) | - | NULL | 当前步骤 |
| task_start_ftime | VARCHAR(20) | - | NULL | 开始时间 |
| task_end_ftime | VARCHAR(20) | - | NULL | 结束时间 |
| tokens_used | INT | - | 0 | Token消耗 |
| estimated_duration | INT | - | NULL | 预计时长 |
| actual_duration | INT | - | NULL | 实际时长 |
| created_at | DATETIME | - | CURRENT_TIMESTAMP | 创建时间 |
| started_at | DATETIME | - | NULL | 开始时间 |
| completed_at | DATETIME | - | NULL | 完成时间 |
| error_info | JSON | - | NULL | 错误信息 |
| retry_count | TINYINT | - | 0 | 重试次数 |
| max_retry_count | TINYINT | - | 3 | 最大重试 |
| parent_task_id | INT | - | NULL | 父任务ID |
| notification_config | JSON | - | NULL | 通知配置 |
| source | VARCHAR(50) | - | NULL | 任务来源 |


## 索引说明

### member_report_users
- PRIMARY KEY: `id`
- UNIQUE: `username`, `email`
- INDEX: `idx_username`, `idx_email`, `idx_status`, `idx_role`, `idx_account_type`, `idx_expire_time`, `idx_deleted_at`

### member_report_sessions
- PRIMARY KEY: `id`
- UNIQUE: `token`, `refresh_token`
- INDEX: `idx_token(100)`, `idx_user_id`, `idx_expires_at`

### member_report_audit_logs
- PRIMARY KEY: `id`
- INDEX: `idx_user_id`, `idx_action`, `idx_status`, `idx_created_at`

### member_report_bid_configs
- PRIMARY KEY: `id`
- UNIQUE: `bid`
- INDEX: `idx_bid`, `idx_status`, `idx_deleted_at`

### member_report_user_bid_permissions
- PRIMARY KEY: `id`
- UNIQUE: `unique_user_bid(username, bid)`
- INDEX: `idx_username`, `idx_bid`, `idx_status`

### member_report_tasks
- PRIMARY KEY: `id`
- UNIQUE: `task_uuid`
- INDEX: `idx_task_uuid`, `idx_user_id`, `idx_bid`, `idx_status`, `idx_priority_status`, `idx_created_at`, `idx_parent_task_id`


## 外键关系

1. **member_report_sessions**
   - `user_id` → `member_report_users(id)` ON DELETE CASCADE

2. **member_report_bid_configs**
   - `owner_username` → `member_report_users(username)` ON DELETE SET NULL ON UPDATE CASCADE

3. **member_report_user_bid_permissions**
   - `username` → `member_report_users(username)` ON DELETE CASCADE ON UPDATE CASCADE
   - `bid` → `member_report_bid_configs(bid)` ON DELETE CASCADE ON UPDATE CASCADE

## 注意事项

1. **表创建位置分离**：
   - 认证相关表（用户、会话、审计、BID配置、权限）在 `member-api\auth\database\complete_schema.sql` 中创建
   - 任务管理表（任务表）在 `member-api\api\PPTreport\task_management\database_init.py` 中创建
   - 避免重复创建同一张表

2. **字符集和排序规则**：
   - 所有表使用 `utf8mb4` 字符集
   - 排序规则为 `utf8mb4_unicode_ci`（简化版）或 `utf8mb4_general_ci`（扩展版）

3. **存储引擎**：
   - 所有表使用 `InnoDB` 引擎，支持事务和外键