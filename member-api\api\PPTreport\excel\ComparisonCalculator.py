# -*- coding: utf-8 -*-
"""
同比环比计算器
负责计算数据的同比和环比增长率
"""

import pandas as pd
import numpy as np
import logging
from typing import List, Optional, Union

logger = logging.getLogger(__name__)


class ComparisonCalculator:
    """同比环比计算器"""
    
    @staticmethod
    def calculate_year_on_year(
        current_data: pd.DataFrame,
        last_year_data: pd.DataFrame,
        value_columns: List[str]
    ) -> pd.DataFrame:
        """
        计算同比数据
        同比 = (本期数 - 去年同期数) / 去年同期数 × 100%
        
        Args:
            current_data: 当前年份数据
            last_year_data: 去年数据
            value_columns: 需要计算同比的列
            
        Returns:
            pd.DataFrame: 包含同比数据的DataFrame
        """
        try:
            result = current_data.copy()
            
            # 确保数据有月份列用于对齐
            if 'month' not in current_data.columns and 'month' not in last_year_data.columns:
                # 如果没有月份列，假设数据按顺序排列
                current_data['month'] = range(1, len(current_data) + 1)
                last_year_data['month'] = range(1, len(last_year_data) + 1)
            
            # 对每个值列计算同比
            for col in value_columns:
                if col not in current_data.columns:
                    logger.warning(f"当前数据中没有列: {col}")
                    continue
                
                if col not in last_year_data.columns:
                    logger.warning(f"去年数据中没有列: {col}")
                    result[f"{col}_同比"] = "N/A"
                    continue
                
                yoy_col = f"{col}_同比"
                result[yoy_col] = None
                
                # 按月份对齐数据
                for idx, row in current_data.iterrows():
                    try:
                        month = row.get('month', idx + 1)
                        
                        # 查找去年同月数据
                        last_year_month_data = last_year_data[
                            last_year_data.get('month', range(1, len(last_year_data) + 1)) == month
                        ]
                        
                        if not last_year_month_data.empty:
                            last_value = last_year_month_data.iloc[0][col]
                            current_value = row[col]
                            
                            # 计算同比
                            if pd.notna(last_value) and pd.notna(current_value):
                                if float(last_value) != 0:
                                    yoy = ((float(current_value) - float(last_value)) / float(last_value)) * 100
                                    result.at[idx, yoy_col] = f"{yoy:.2f}%"
                                else:
                                    # 如果去年为0，今年不为0，显示新增
                                    if float(current_value) != 0:
                                        result.at[idx, yoy_col] = "新增"
                                    else:
                                        result.at[idx, yoy_col] = "0.00%"
                            else:
                                result.at[idx, yoy_col] = "N/A"
                        else:
                            # 没有去年同期数据
                            result.at[idx, yoy_col] = "无同期"
                    
                    except Exception as e:
                        logger.warning(f"计算同比时出错: {e}")
                        result.at[idx, yoy_col] = "错误"
            
            return result
            
        except Exception as e:
            logger.error(f"计算同比失败: {e}")
            return current_data
    
    @staticmethod
    def calculate_month_on_month(
        data: pd.DataFrame,
        value_columns: List[str]
    ) -> pd.DataFrame:
        """
        计算环比数据
        环比 = (本期数 - 上期数) / 上期数 × 100%
        
        Args:
            data: 原始数据
            value_columns: 需要计算环比的列
            
        Returns:
            pd.DataFrame: 包含环比数据的DataFrame
        """
        try:
            result = data.copy()
            
            # 对每个值列计算环比
            for col in value_columns:
                if col not in data.columns:
                    logger.warning(f"数据中没有列: {col}")
                    continue
                
                mom_col = f"{col}_环比"
                result[mom_col] = None
                
                # 获取列值
                values = data[col].values
                
                for i in range(len(values)):
                    try:
                        if i == 0:
                            # 第一个月没有环比
                            result.at[i, mom_col] = "-"
                        else:
                            prev_value = values[i - 1]
                            curr_value = values[i]
                            
                            if pd.notna(prev_value) and pd.notna(curr_value):
                                if float(prev_value) != 0:
                                    mom = ((float(curr_value) - float(prev_value)) / float(prev_value)) * 100
                                    result.at[i, mom_col] = f"{mom:.2f}%"
                                else:
                                    # 如果上期为0，本期不为0，显示新增
                                    if float(curr_value) != 0:
                                        result.at[i, mom_col] = "新增"
                                    else:
                                        result.at[i, mom_col] = "0.00%"
                            else:
                                result.at[i, mom_col] = "N/A"
                    
                    except Exception as e:
                        logger.warning(f"计算环比时出错: {e}")
                        result.at[i, mom_col] = "错误"
            
            return result
            
        except Exception as e:
            logger.error(f"计算环比失败: {e}")
            return data
    
    @staticmethod
    def calculate_growth_rate(
        current_value: Union[int, float],
        previous_value: Union[int, float]
    ) -> Optional[str]:
        """
        计算增长率
        
        Args:
            current_value: 当前值
            previous_value: 之前的值
            
        Returns:
            str: 格式化的增长率字符串
        """
        try:
            if previous_value == 0:
                if current_value == 0:
                    return "0.00%"
                else:
                    return "新增"
            
            growth_rate = ((current_value - previous_value) / previous_value) * 100
            return f"{growth_rate:.2f}%"
            
        except Exception:
            return "N/A"
    
    @staticmethod
    def handle_missing_data(
        data: pd.DataFrame,
        fill_value: Union[str, int, float] = "N/A"
    ) -> pd.DataFrame:
        """
        处理缺失数据
        
        Args:
            data: 原始数据
            fill_value: 填充值
            
        Returns:
            pd.DataFrame: 处理后的数据
        """
        return data.fillna(fill_value)
    
    @staticmethod
    def format_percentage(value: Union[int, float], decimal_places: int = 2) -> str:
        """
        格式化百分比显示
        
        Args:
            value: 数值
            decimal_places: 小数位数
            
        Returns:
            str: 格式化的百分比字符串
        """
        try:
            return f"{float(value):.{decimal_places}f}%"
        except:
            return "N/A"
    
    @staticmethod
    def prepare_comparison_data(
        data: pd.DataFrame,
        date_column: str = 'date',
        group_column: Optional[str] = None
    ) -> pd.DataFrame:
        """
        准备用于比较的数据
        
        Args:
            data: 原始数据
            date_column: 日期列名
            group_column: 分组列名（可选）
            
        Returns:
            pd.DataFrame: 准备好的数据
        """
        try:
            result = data.copy()
            
            # 确保日期列是datetime类型
            if date_column in result.columns:
                result[date_column] = pd.to_datetime(result[date_column])
                
                # 提取月份信息
                result['year'] = result[date_column].dt.year
                result['month'] = result[date_column].dt.month
                result['year_month'] = result[date_column].dt.strftime('%Y-%m')
            
            # 如果有分组列，按分组排序
            if group_column and group_column in result.columns:
                result = result.sort_values([group_column, date_column])
            elif date_column in result.columns:
                result = result.sort_values(date_column)
            
            return result
            
        except Exception as e:
            logger.error(f"准备比较数据失败: {e}")
            return data
    
    @staticmethod
    def aggregate_by_period(
        data: pd.DataFrame,
        period: str = 'month',
        value_columns: List[str] = None,
        agg_func: str = 'sum'
    ) -> pd.DataFrame:
        """
        按时间周期聚合数据
        
        Args:
            data: 原始数据
            period: 聚合周期 ('month', 'quarter', 'year')
            value_columns: 需要聚合的值列
            agg_func: 聚合函数 ('sum', 'mean', 'count', etc.)
            
        Returns:
            pd.DataFrame: 聚合后的数据
        """
        try:
            if 'date' not in data.columns:
                logger.warning("数据中没有date列，无法聚合")
                return data
            
            # 确保日期列是datetime类型
            data['date'] = pd.to_datetime(data['date'])
            
            # 设置聚合键
            if period == 'month':
                data['period'] = data['date'].dt.to_period('M')
            elif period == 'quarter':
                data['period'] = data['date'].dt.to_period('Q')
            elif period == 'year':
                data['period'] = data['date'].dt.to_period('Y')
            else:
                logger.warning(f"不支持的聚合周期: {period}")
                return data
            
            # 确定要聚合的列
            if value_columns is None:
                # 自动选择数值列
                value_columns = data.select_dtypes(include=[np.number]).columns.tolist()
                if 'period' in value_columns:
                    value_columns.remove('period')
            
            # 执行聚合
            aggregated = data.groupby('period')[value_columns].agg(agg_func).reset_index()
            
            # 将period转换回字符串
            aggregated['period'] = aggregated['period'].astype(str)
            
            return aggregated
            
        except Exception as e:
            logger.error(f"聚合数据失败: {e}")
            return data