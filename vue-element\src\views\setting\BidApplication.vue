<template>
  <div class="bid-application-container">
    <h2>BID配置申请</h2>
    
    <!-- 申请表单 -->
    <el-card class="form-card" v-if="showForm">
      <template #header>
        <div class="card-header">
          <span>新增BID申请</span>
          <el-button type="text" @click="showForm = false">取消</el-button>
        </div>
      </template>
      
      <el-form 
        ref="formRef"
        :model="applicationForm" 
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="BID代码" prop="bid">
              <el-input 
                v-model="applicationForm.bid" 
                placeholder="请输入BID代码"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="BID名称" prop="bid_name">
              <el-input 
                v-model="applicationForm.bid_name" 
                placeholder="请输入BID名称"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="applicationForm.description" 
            type="textarea"
            :rows="3"
            placeholder="请输入BID描述"
            maxlength="500"
          />
        </el-form-item>
        
        <el-form-item label="申请理由" prop="apply_reason">
          <el-input 
            v-model="applicationForm.apply_reason" 
            type="textarea"
            :rows="3"
            placeholder="请说明申请该BID的原因"
            maxlength="500"
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="公司名称" prop="company_name">
              <el-input 
                v-model="applicationForm.company_name" 
                placeholder="请输入公司名称"
                maxlength="200"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属行业" prop="industry">
              <el-input 
                v-model="applicationForm.industry" 
                placeholder="请输入所属行业"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="联系人" prop="contact_name">
              <el-input 
                v-model="applicationForm.contact_name" 
                placeholder="请输入联系人姓名"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系电话" prop="contact_phone">
              <el-input 
                v-model="applicationForm.contact_phone" 
                placeholder="请输入联系电话"
                maxlength="20"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系邮箱" prop="contact_email">
              <el-input 
                v-model="applicationForm.contact_email" 
                placeholder="请输入联系邮箱"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="业态类型" prop="business_type">
          <el-select
            v-model="applicationForm.business_type"
            placeholder="请选择业态类型"
          >
            <el-option label="快餐" value="fast_food" />
            <el-option label="休闲餐" value="casual_dining" />
            <el-option label="正餐" value="fine_dining" />
          </el-select>
          <span style="color: red; margin-left: 5px">*</span>
        </el-form-item>
        
        <el-form-item label="SID列表" prop="sid_list">
          <el-tag
            v-for="sid in applicationForm.sid_list"
            :key="sid"
            closable
            @close="removeSid(sid)"
            style="margin-right: 10px; margin-bottom: 10px;"
          >
            {{ sid }}
          </el-tag>
          <el-input
            v-if="showSidInput"
            ref="sidInputRef"
            v-model="newSid"
            size="small"
            style="width: 200px;"
            @keyup.enter="handleAddSid"
            @blur="handleAddSid"
          />
          <el-button
            v-else
            size="small"
            @click="showSidInput = true"
          >
            + 添加SID
          </el-button>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitApplication">{{ isEditMode ? '保存修改' : '提交申请' }}</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 申请列表 -->
    <el-card class="list-card">
      <template #header>
        <div class="card-header">
          <span>我的BID申请</span>
          <el-button type="primary" @click="showForm = true" v-if="!showForm">
            <el-icon><Plus /></el-icon>
            新增申请
          </el-button>
        </div>
      </template>
      
      <el-table 
        :data="applicationList" 
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column prop="bid" label="BID代码" width="120" />
        <el-table-column prop="bid_name" label="BID名称" min-width="150" />
        <el-table-column prop="business_type" label="业态" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.business_type" :type="getBusinessTypeTag(row.business_type)">
              {{ getBusinessTypeLabel(row.business_type) }}
            </el-tag>
            <span v-else style="color: #909399">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="company_name" label="公司名称" min-width="150" />
        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="apply_reason" label="申请理由" min-width="200" show-overflow-tooltip />
        <el-table-column prop="reject_reason" label="拒绝原因" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-if="row.reject_reason" class="reject-reason">
              {{ row.reject_reason }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="申请时间" width="160" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <!-- 待审批状态可以修改和撤销 -->
            <template v-if="row.status === 'pending'">
              <el-button type="primary" size="small" @click="editApplication(row)">修改</el-button>
              <el-button type="danger" size="small" @click="cancelApplication(row)">撤销</el-button>
            </template>
            <!-- 已拒绝状态可以重新申请 -->
            <template v-else-if="row.status === 'rejected'">
              <el-button type="primary" size="small" @click="resubmitApplication(row)">重新申请</el-button>
            </template>
            <!-- 已批准状态可以查看详情 -->
            <template v-else-if="row.status === 'approved' || row.status === 'active'">
              <el-button type="info" size="small" @click="viewDetail(row)">查看详情</el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        style="margin-top: 20px"
      />
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import api from '@/api'

export default {
  name: 'BidApplication',
  
  components: {
    Plus
  },
  
  setup() {
    const loading = ref(false)
    const showForm = ref(false)
    const showSidInput = ref(false)
    const formRef = ref(null)
    const sidInputRef = ref(null)
    const newSid = ref('')
    const isEditMode = ref(false)
    const editingBid = ref(null)
    
    // 申请表单
    const applicationForm = reactive({
      bid: '',
      bid_name: '',
      description: '',
      business_type: null,
      apply_reason: '',
      company_name: '',
      industry: '',
      contact_name: '',
      contact_phone: '',
      contact_email: '',
      sid_list: []
    })
    
    // 表单验证规则
    const rules = {
      bid: [
        { required: true, message: '请输入BID代码', trigger: 'blur' },
        { pattern: /^[A-Za-z0-9_-]+$/, message: 'BID只能包含字母、数字、下划线和横线', trigger: 'blur' }
      ],
      bid_name: [
        { required: true, message: '请输入BID名称', trigger: 'blur' }
      ],
      business_type: [
        { required: true, message: '请选择业态类型', trigger: 'change' }
      ],
      apply_reason: [
        { required: true, message: '请输入申请理由', trigger: 'blur' }
      ],
      company_name: [
        { required: true, message: '请输入公司名称', trigger: 'blur' }
      ],
      contact_name: [
        { required: true, message: '请输入联系人姓名', trigger: 'blur' }
      ],
      contact_phone: [
        { required: true, message: '请输入联系电话', trigger: 'blur' },
        { pattern: /^[\d-]+$/, message: '电话格式不正确', trigger: 'blur' }
      ],
      contact_email: [
        { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
      ]
    }
    
    // 列表数据
    const applicationList = ref([])
    const currentPage = ref(1)
    const pageSize = ref(10)
    const total = ref(0)
    
    // 获取申请列表
    const fetchApplicationList = async () => {
      loading.value = true
      try {
        const response = await api.get('/setting/bid-application/list', {
          params: {
            page: currentPage.value,
            page_size: pageSize.value
          }
        })
        
        if (response.code === 200) {
          applicationList.value = response.data.items || []
          total.value = response.data.total || 0
        }
      } catch (error) {
        console.error('获取申请列表失败:', error)
        ElMessage.error('获取申请列表失败')
      } finally {
        loading.value = false
      }
    }
    
    // 提交申请
    const submitApplication = async () => {
      const valid = await formRef.value.validate()
      if (!valid) return
      
      try {
        loading.value = true
        let response
        
        if (isEditMode.value && editingBid.value) {
          // 编辑模式 - 更新申请
          response = await api.put(`/setting/bid-application/${editingBid.value}/update`, applicationForm)
        } else {
          // 新增模式 - 提交新申请
          response = await api.post('/setting/bid-application/apply', applicationForm)
        }
        
        if (response.code === 200) {
          ElMessage.success(isEditMode.value ? '申请修改成功' : '申请提交成功，等待审批')
          showForm.value = false
          resetForm()
          fetchApplicationList()
        } else {
          ElMessage.error(response.message || (isEditMode.value ? '修改申请失败' : '申请提交失败'))
        }
      } catch (error) {
        console.error(isEditMode.value ? '修改申请失败:' : '提交申请失败:', error)
        ElMessage.error(isEditMode.value ? '修改申请失败' : '提交申请失败')
      } finally {
        loading.value = false
        isEditMode.value = false
        editingBid.value = null
      }
    }
    
    // 添加SID
    const handleAddSid = () => {
      if (newSid.value && !applicationForm.sid_list.includes(newSid.value)) {
        applicationForm.sid_list.push(newSid.value)
      }
      newSid.value = ''
      showSidInput.value = false
    }
    
    // 移除SID
    const removeSid = (sid) => {
      const index = applicationForm.sid_list.indexOf(sid)
      if (index > -1) {
        applicationForm.sid_list.splice(index, 1)
      }
    }
    
    // 重置表单
    const resetForm = () => {
      formRef.value?.resetFields()
      Object.assign(applicationForm, {
        bid: '',
        bid_name: '',
        description: '',
        business_type: null,
        apply_reason: '',
        company_name: '',
        industry: '',
        contact_name: '',
        contact_phone: '',
        contact_email: '',
        sid_list: []
      })
      isEditMode.value = false
      editingBid.value = null
    }
    
    // 撤销申请
    const cancelApplication = async (row) => {
      try {
        await ElMessageBox.confirm('确定要撤销该申请吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const response = await api.delete(`/setting/bid-application/${row.bid}/cancel`)
        
        if (response.code === 200) {
          ElMessage.success('撤销成功')
          fetchApplicationList()
        } else {
          ElMessage.error(response.message || '撤销失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('撤销失败')
        }
      }
    }
    
    // 重新申请
    const resubmitApplication = async (row) => {
      Object.assign(applicationForm, {
        bid: row.bid,
        bid_name: row.bid_name,
        description: row.description,
        business_type: row.business_type || null,
        apply_reason: '',
        company_name: row.company_name,
        industry: row.industry,
        contact_name: row.contact_name,
        contact_phone: row.contact_phone,
        contact_email: row.contact_email,
        sid_list: row.sid_list || []
      })
      showForm.value = true
    }
    
    // 获取状态类型
    const getStatusType = (status) => {
      const map = {
        pending: 'warning',
        approved: 'success',
        rejected: 'danger',
        active: 'success',
        inactive: 'info'
      }
      return map[status] || 'info'
    }
    
    // 获取状态文本
    const getStatusText = (status) => {
      const map = {
        pending: '待审批',
        approved: '已批准',
        rejected: '已拒绝',
        active: '已激活',
        inactive: '已停用'
      }
      return map[status] || status
    }
    
    // 分页处理
    const handleSizeChange = (val) => {
      pageSize.value = val
      fetchApplicationList()
    }
    
    const handleCurrentChange = (val) => {
      currentPage.value = val
      fetchApplicationList()
    }
    
    // 编辑申请
    const editApplication = (row) => {
      Object.assign(applicationForm, row)
      isEditMode.value = true
      editingBid.value = row.bid
      showForm.value = true
    }
    
    // 查看详情
    const viewDetail = (row) => {
      ElMessageBox.alert(
        `<div>
          <p><strong>BID代码：</strong>${row.bid}</p>
          <p><strong>BID名称：</strong>${row.bid_name}</p>
          <p><strong>业态类型：</strong>${getBusinessTypeLabel(row.business_type) || '-'}</p>
          <p><strong>公司名称：</strong>${row.company_name}</p>
          <p><strong>状态：</strong>${getStatusText(row.status)}</p>
          <p><strong>批准人：</strong>${row.approved_by || '-'}</p>
          <p><strong>批准时间：</strong>${row.approved_at || '-'}</p>
        </div>`,
        'BID详情',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '关闭'
        }
      )
    }
    
    // 获取业态标签
    const getBusinessTypeLabel = (type) => {
      const typeMap = {
        'fast_food': '快餐',
        'casual_dining': '休闲餐',
        'fine_dining': '正餐'
      }
      return typeMap[type] || type
    }
    
    // 获取业态标签样式
    const getBusinessTypeTag = (type) => {
      const tagMap = {
        'fast_food': 'success',
        'casual_dining': 'warning',
        'fine_dining': 'danger'
      }
      return tagMap[type] || 'info'
    }
    
    // 监听SID输入框显示
    const watchSidInput = () => {
      if (showSidInput.value) {
        nextTick(() => {
          sidInputRef.value?.focus()
        })
      }
    }
    
    onMounted(() => {
      fetchApplicationList()
    })
    
    return {
      loading,
      showForm,
      showSidInput,
      formRef,
      sidInputRef,
      newSid,
      applicationForm,
      rules,
      applicationList,
      currentPage,
      pageSize,
      total,
      submitApplication,
      handleAddSid,
      removeSid,
      resetForm,
      cancelApplication,
      resubmitApplication,
      getStatusType,
      getStatusText,
      getBusinessTypeLabel,
      getBusinessTypeTag,
      handleSizeChange,
      handleCurrentChange,
      editApplication,
      viewDetail,
      fetchApplicationList
    }
  }
}
</script>

<style scoped>
.bid-application-container {
  padding: 20px;
}

.form-card {
  margin-bottom: 20px;
}

.list-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reject-reason {
  color: #f56c6c;
  font-size: 12px;
}
</style>