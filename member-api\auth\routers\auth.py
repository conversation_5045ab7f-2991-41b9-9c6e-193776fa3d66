"""
认证路由
处理登录、登出、Token刷新等接口
"""
import logging
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Header, Request
from fastapi.responses import JSONResponse

from core.database import db
from ..schemas.auth import (
    LoginRequest, LoginResponse, RefreshTokenRequest, 
    LogoutRequest, SuperAdminInit
)
from ..schemas.user import UserResponse, UserPasswordUpdate
from ..services.auth_service import AuthService
from ..middleware.auth_middleware import get_current_user, get_optional_user
from ..database.init_auth_database import AuthDatabaseInitializer

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/auth",
    tags=["认证"]
)


@router.post("/login", response_model=LoginResponse, summary="用户登录")
async def login(login_data: LoginRequest, request: Request):
    """
    用户登录接口
    
    - **username**: 用户名或邮箱
    - **password**: 密码
    - **remember_me**: 是否记住登录状态
    """
    try:
        # 获取客户端IP和User-Agent
        client_ip = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")
        
        auth_service = AuthService(db.task_management_pool)
        result = await auth_service.login(login_data, ip_address=client_ip, user_agent=user_agent)
        
        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=result.message
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登录异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录失败，请稍后重试"
        )


@router.post("/logout", summary="用户登出")
async def logout(
    logout_data: Optional[LogoutRequest] = None,
    authorization: Optional[str] = Header(None),
    current_user: Optional[dict] = Depends(get_optional_user)
):
    """
    用户登出接口
    
    - **token**: 要注销的Token（可选）
    - **all_devices**: 是否登出所有设备
    """
    try:
        # 获取Token
        token = None
        if logout_data and logout_data.token:
            token = logout_data.token
        elif authorization:
            # 从Header中提取Token
            if authorization.startswith("Bearer "):
                token = authorization[7:]
        
        if not token:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={"success": True, "message": "已登出"}
            )
        
        auth_service = AuthService(db.task_management_pool)
        all_devices = logout_data.all_devices if logout_data else False
        
        success = await auth_service.logout(token, all_devices)
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": success,
                "message": "登出成功" if success else "登出失败"
            }
        )
        
    except Exception as e:
        logger.error(f"登出异常: {e}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={"success": True, "message": "已登出"}
        )


@router.post("/refresh", summary="刷新Token")
async def refresh_token(refresh_data: RefreshTokenRequest):
    """
    刷新Token接口
    
    使用refresh_token获取新的access_token
    """
    try:
        auth_service = AuthService(db.task_management_pool)
        token_response = await auth_service.refresh_token(refresh_data.refresh_token)
        
        if not token_response:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="刷新Token无效或已过期"
            )
        
        return token_response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"刷新Token异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="刷新Token失败"
        )


@router.get("/me", response_model=UserResponse, summary="获取当前用户信息")
async def get_current_user_info(current_user: dict = Depends(get_current_user)):
    """
    获取当前登录用户信息
    
    需要在请求头中提供有效的Token
    """
    try:
        # 从数据库获取最新的用户信息
        auth_service = AuthService(db.task_management_pool)
        user = await auth_service._get_user_by_id(current_user['user_id'])
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 获取用户的BID权限
        from ..services.bid_permission_service import BidPermissionService
        bid_permission_service = BidPermissionService(db.task_management_pool)
        user_bid_permissions = await bid_permission_service.get_user_bid_permissions(user['username'])
        
        # 构建响应
        import json
        return UserResponse(
            id=user['id'],
            username=user['username'],
            email=user['email'],
            real_name=user['real_name'],
            phone=user['phone'],
            department=user['department'],
            role=user['role'],
            status=user['status'],
            user_bid_permissions=user_bid_permissions if user_bid_permissions else [],  # 使用新格式权限
            quotas=user['quotas'] if user['quotas'] else 0,
            use_quotas=user['use_quotas'] if user['use_quotas'] else 0,
            avatar_url=user['avatar_url'],
            last_login_time=user['last_login_time'].strftime("%Y-%m-%d %H:%M:%S") if user['last_login_time'] else None,
            login_count=user['login_count'],
            created_at=user['created_at'].strftime("%Y-%m-%d %H:%M:%S"),
            updated_at=user['updated_at'].strftime("%Y-%m-%d %H:%M:%S")
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户信息异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户信息失败"
        )


@router.get("/check-super-admin", summary="检查是否存在超级管理员")
async def check_super_admin():
    """
    检查系统是否已经初始化超级管理员
    
    返回:
    - **initialized**: 是否已初始化
    - **message**: 提示信息
    """
    try:
        initializer = AuthDatabaseInitializer(db.task_management_pool)
        has_super_admin = await initializer.check_super_admin_exists()
        
        return {
            "initialized": has_super_admin,
            "message": "系统已初始化" if has_super_admin else "请初始化超级管理员"
        }
        
    except Exception as e:
        logger.error(f"检查超级管理员异常: {e}")
        return {
            "initialized": False,
            "message": "检查失败"
        }


@router.post("/init-super-admin", response_model=LoginResponse, summary="初始化超级管理员")
async def init_super_admin(init_data: SuperAdminInit):
    """
    初始化超级管理员账户
    
    只能在系统没有超级管理员时调用
    
    - **username**: 用户名
    - **password**: 密码（至少8位，包含字母和数字）
    - **email**: 邮箱
    - **real_name**: 真实姓名
    - **init_key**: 初始化密钥（如果配置了）
    """
    try:
        # 检查是否已存在超级管理员
        initializer = AuthDatabaseInitializer(db.task_management_pool)
        if await initializer.check_super_admin_exists():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="超级管理员已存在，无法重复初始化"
            )
        
        # 初始化超级管理员
        auth_service = AuthService(db.task_management_pool)
        result = await auth_service.init_super_admin(init_data)
        
        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.message
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"初始化超级管理员异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="初始化失败，请稍后重试"
        )


@router.post("/change-password", summary="修改密码")
async def change_password(
    password_data: UserPasswordUpdate,
    current_user: dict = Depends(get_current_user)
):
    """
    修改当前用户密码
    
    - **old_password**: 当前密码
    - **new_password**: 新密码（至少6位）
    """
    try:
        from ..services.user_service import UserService
        user_service = UserService(db.task_management_pool)
        
        # 修改密码
        success = await user_service.update_password(
            current_user['user_id'], 
            password_data
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="密码修改失败"
            )
        
        return {
            "success": True,
            "message": "密码修改成功"
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"修改密码异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="密码修改失败，请稍后重试"
        )


@router.get("/verify", summary="验证Token有效性")
async def verify_token(current_user: dict = Depends(get_current_user)):
    """
    验证Token是否有效
    
    返回:
    - **valid**: Token是否有效
    - **user_id**: 用户ID
    - **username**: 用户名
    - **role**: 用户角色
    """
    return {
        "valid": True,
        "user_id": current_user.get("user_id"),
        "username": current_user.get("username"),
        "role": current_user.get("role")
    }