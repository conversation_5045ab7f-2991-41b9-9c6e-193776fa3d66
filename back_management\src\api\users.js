import request from './request'

export const usersAPI = {
  // 获取用户列表
  getUsers(params) {
    return request({
      url: '/admin/users',
      method: 'get',
      params
    })
  },

  // 获取单个用户
  getUser(userId) {
    return request({
      url: `/admin/users/${userId}`,
      method: 'get'
    })
  },

  // 创建用户
  createUser(data) {
    return request({
      url: '/admin/users',
      method: 'post',
      data
    })
  },

  // 更新用户
  updateUser(userId, data) {
    return request({
      url: `/admin/users/${userId}`,
      method: 'put',
      data
    })
  },

  // 删除用户
  deleteUser(userId) {
    return request({
      url: `/admin/users/${userId}`,
      method: 'delete'
    })
  },

  // 重置用户密码
  resetPassword(userId, newPassword) {
    return request({
      url: `/admin/users/${userId}/reset-password`,
      method: 'post',
      data: { new_password: newPassword }
    })
  },

  // 更新用户状态
  updateUserStatus(userId, status) {
    return request({
      url: `/admin/users/${userId}/status`,
      method: 'patch',
      data: { status }
    })
  },

  // 更新用户配额
  updateUserQuota(userId, quotaData) {
    return request({
      url: `/admin/users/${userId}/quota`,
      method: 'patch',
      data: quotaData
    })
  },

  // 获取用户统计
  getUserStatistics() {
    return request({
      url: '/admin/users/statistics',
      method: 'get'
    })
  },

  // 修改当前用户密码
  changePassword(data) {
    return request({
      url: '/auth/change-password',
      method: 'post',
      data
    })
  },

  // 获取仪表盘统计数据
  getDashboardStatistics() {
    return request({
      url: '/admin/dashboard/statistics',
      method: 'get'
    })
  }
}