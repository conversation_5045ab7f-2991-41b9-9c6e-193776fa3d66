# -*- coding: utf-8 -*-
"""
会员平均消费数据图片生成模块
生成会员现金消费笔数、会员储值消费笔数、会员单均消费、会员人均贡献的组合图表
"""

import datetime
import logging
from typing import Dict, Any, List, Tuple

# 设置matplotlib后端（在导入pyplot之前）
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import numpy as np

# 导入智能标签管理器
from .SmartLabels import add_smart_bar_labels, add_smart_line_labels

# 导入数据收集装饰器
from ..excel import collect_chart_data

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class AvgConsumptionPicGenerator:
    """会员平均消费数据图片生成器"""

    def __init__(self, bid: str, image_manager):
        """
        初始化图片生成器

        Args:
            bid: 品牌ID
            image_manager: 图片管理器实例
        """
        self.bid = bid
        self.image_manager = image_manager

    def _extract_param(self, query_params, key, default=None):
        """
        从查询参数中提取值

        Args:
            query_params: 查询参数（对象或字典）
            key: 参数键
            default: 默认值

        Returns:
            参数值
        """
        if hasattr(query_params, key):
            return getattr(query_params, key)
        elif isinstance(query_params, dict):
            return query_params.get(key, default)
        else:
            return default

    async def generate_avg_consumption_charts(self, query_params) -> Dict[str, str]:
        """
        生成会员平均消费数据图表

        Args:
            query_params: 查询参数（对象或字典）

        Returns:
            Dict: 包含两张图片路径和AI分析的字典
        """
        try:
            logger.info(f"开始生成会员平均消费数据图表 - bid: {self.bid}")
            logger.info(f"查询参数类型: {type(query_params)}")
            logger.info(f"图片管理器会话目录: {self.image_manager.session_dir}")

            # 计算时间范围
            current_date = datetime.datetime.now()
            end_date = self._extract_param(query_params, 'end_date', '2025-06-30')
            query_end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d")

            logger.info(f"时间参数 - 当前日期: {current_date}, 查询结束日期: {query_end_date}")

            # 计算去年和今年的时间范围
            last_year = query_end_date.year - 1
            this_year = query_end_date.year

            # 去年：完整的12个月
            last_year_ranges = self._generate_monthly_ranges(last_year, 1, 12)

            # 今年：从1月到查询结束月份（如果是当前年份，则到前一天）
            if this_year == current_date.year:
                # 如果是当前年份，计算到昨天
                yesterday = current_date - datetime.timedelta(days=1)
                end_month = yesterday.month
            else:
                # 如果不是当前年份，使用查询结束日期的月份
                end_month = query_end_date.month

            this_year_ranges = self._generate_monthly_ranges(this_year, 1, end_month)

            # 获取数据
            logger.info(f"开始获取数据 - 去年范围: {len(last_year_ranges)}个月, 今年范围: {len(this_year_ranges)}个月")
            last_year_data = await self._fetch_monthly_data(last_year_ranges, query_params)
            this_year_data = await self._fetch_monthly_data(this_year_ranges, query_params)

            logger.info(f"数据获取结果 - 去年: {len(last_year_data)}条, 今年: {len(this_year_data)}条")

            # 初始化结果字典
            result = {}

            # 如果数据获取失败，生成错误图片
            if not last_year_data:
                logger.warning("去年数据获取失败，生成错误图片")
                error_path = self._generate_error_image("avg_consumption_last_year", "去年数据获取失败")
                if error_path:
                    result["avg_consumption_last_year"] = error_path

            if not this_year_data:
                logger.warning("今年数据获取失败，生成错误图片")
                error_path = self._generate_error_image("avg_consumption_this_year", "今年数据获取失败")
                if error_path:
                    result["avg_consumption_this_year"] = error_path

            logger.info(f"最终数据 - 去年: {len(last_year_data)}条, 今年: {len(this_year_data)}条")

            # 只要有数据结构就生成图片（包括全零数据，显示无数据状态）
            if last_year_data:
                last_year_path = await self._generate_chart(
                    last_year_data,
                    f"{last_year}年会员平均消费数据分析",
                    "avg_consumption_last_year"
                )
                if last_year_path:
                    result["avg_consumption_last_year"] = last_year_path

            if this_year_data:
                this_year_path = await self._generate_chart(
                    this_year_data,
                    f"{this_year}年会员平均消费数据分析",
                    "avg_consumption_this_year"
                )
                if this_year_path:
                    result["avg_consumption_this_year"] = this_year_path

            # 生成AI分析
            try:
                from .PictureAi import PictureAiAnalyzer
                ai_analyzer = PictureAiAnalyzer()

                logger.info("开始生成会员平均消费数据AI分析...")

                # 无论数据是否完整，都尝试生成AI分析
                if last_year_data and this_year_data:
                    # 数据完整，生成完整分析
                    logger.info("数据完整，生成完整AI分析")
                    ai_analysis = await ai_analyzer.generate_all_avg_consumption_analysis(this_year_data, last_year_data)
                    result.update(ai_analysis)
                elif this_year_data:
                    # 只有今年数据，生成今年分析和默认去年分析
                    logger.info("只有今年数据，生成部分AI分析")
                    this_year_analysis = await ai_analyzer.analyze_avg_consumption_this_year_data(this_year_data, [])
                    result.update({
                        "avg_consumption_last_year_analysis_report": "1、去年平均消费数据缺失，无法进行详细分析。\n2、建议完善数据收集机制，确保历史数据完整性。\n3、可通过其他渠道补充去年同期数据作为对比基准。\n4、重点关注今年消费趋势，制定针对性运营策略。",
                        "avg_consumption_this_year_analysis_report": this_year_analysis
                    })
                elif last_year_data:
                    # 只有去年数据，生成去年分析和默认今年分析
                    logger.info("只有去年数据，生成部分AI分析")
                    last_year_analysis = await ai_analyzer.analyze_avg_consumption_last_year_data(last_year_data)
                    result.update({
                        "avg_consumption_last_year_analysis_report": last_year_analysis,
                        "avg_consumption_this_year_analysis_report": "1、今年平均消费数据缺失，无法进行当期分析。\n2、建议立即启动数据收集，确保实时监控会员消费情况。\n3、基于去年数据制定今年消费目标和策略。\n4、加强数据统计和分析能力建设。"
                    })
                else:
                    # 数据都缺失，生成默认分析
                    logger.warning("数据完全缺失，生成默认AI分析")
                    result.update({
                        "avg_consumption_last_year_analysis_report": "1、历史平均消费数据缺失，无法进行趋势分析。\n2、建议立即建立完善的数据收集和统计体系。\n3、制定数据恢复计划，尽可能补充历史数据。\n4、建立数据质量监控机制，确保未来数据完整性。",
                        "avg_consumption_this_year_analysis_report": "1、当前平均消费数据缺失，无法评估会员消费情况。\n2、建议紧急启动数据收集工作，确保业务监控正常。\n3、制定应急数据获取方案，通过多渠道收集消费数据。\n4、建立数据备份和恢复机制，防止数据丢失。"
                    })

                logger.info("会员平均消费数据AI分析生成完成")

            except Exception as ai_error:
                logger.error(f"生成AI分析失败: {ai_error}")
                import traceback
                traceback.print_exc()
                result.update({
                    "avg_consumption_last_year_analysis_report": "1、AI分析系统暂时不可用，请稍后重试。\n2、建议检查AI服务连接状态和配置。\n3、可暂时使用人工分析替代AI分析功能。\n4、联系技术支持解决AI分析问题。",
                    "avg_consumption_this_year_analysis_report": "1、AI分析服务异常，无法生成智能分析报告。\n2、建议检查系统日志，排查AI服务故障原因。\n3、可使用历史分析模板进行手动分析。\n4、尽快恢复AI分析功能，确保报告质量。"
                })

            logger.info(f"会员平均消费数据图表和分析生成完成，共生成 {len(result)} 个结果")
            return result

        except Exception as e:
            logger.error(f"生成会员平均消费数据图表失败: {e}")
            return {}

    def _has_valid_avg_consumption_data(self, data: List[Dict[str, Any]]) -> bool:
        """
        检查是否有有效的平均消费数据

        Args:
            data: 月度数据列表

        Returns:
            bool: 是否有有效数据
        """
        if not data:
            return False

        # 检查是否有任何月份有消费数据
        for item in data:
            consume_users = item.get('consume_users', 0)
            total_consume_count = item.get('total_consume_count', 0)
            cash_consume_count = item.get('cash_consume_count', 0)
            prepay_consume_count = item.get('prepay_consume_count', 0)
            avg_consume_amount = item.get('avg_consume_amount', 0)
            avg_contribution = item.get('avg_contribution', 0)

            if (consume_users > 0 or total_consume_count > 0 or cash_consume_count > 0 or
                prepay_consume_count > 0 or avg_consume_amount > 0 or avg_contribution > 0):
                return True

        return False

    def _generate_error_image(self, image_type: str, error_message: str) -> str:
        """
        生成错误提示图片

        Args:
            image_type: 图片类型
            error_message: 错误信息

        Returns:
            str: 图片保存路径
        """
        try:
            # 创建错误图片
            fig, ax = plt.subplots(figsize=(12, 8))
            ax.text(0.5, 0.5, f'数据生成失败\n{error_message}',
                   horizontalalignment='center', verticalalignment='center',
                   fontsize=20, color='red', weight='bold',
                   transform=ax.transAxes)
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')

            # 保存图片
            file_path = self.image_manager.get_image_path(image_type)
            plt.savefig(file_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close(fig)

            logger.info(f"错误图片生成完成: {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"生成错误图片失败: {e}")
            return ""

    def _generate_monthly_ranges(self, year: int, start_month: int, end_month: int) -> List[Tuple[str, str, str]]:
        """
        生成月度时间范围

        Args:
            year: 年份
            start_month: 开始月份
            end_month: 结束月份

        Returns:
            List: [(月份标签, 开始日期, 结束日期), ...]
        """
        ranges = []

        for month in range(start_month, end_month + 1):
            # 计算月份的第一天和最后一天
            first_day = datetime.date(year, month, 1)

            # 计算下个月的第一天，然后减去一天得到当月最后一天
            if month == 12:
                next_month_first = datetime.date(year + 1, 1, 1)
            else:
                next_month_first = datetime.date(year, month + 1, 1)

            last_day = next_month_first - datetime.timedelta(days=1)

            # 如果是当前月份且是今年，需要截止到昨天
            if year == datetime.datetime.now().year and month == datetime.datetime.now().month:
                yesterday = datetime.datetime.now().date() - datetime.timedelta(days=1)
                if yesterday < last_day:
                    last_day = yesterday

            month_label = f"{year}年{month}月"
            start_date = first_day.strftime("%Y-%m-%d")
            end_date = last_day.strftime("%Y-%m-%d")

            ranges.append((month_label, start_date, end_date))

        return ranges

    async def _fetch_monthly_data(self, time_ranges: List[Tuple[str, str, str]], query_params) -> List[Dict[str, Any]]:
        """
        获取月度平均消费数据

        Args:
            time_ranges: 时间范围列表
            query_params: 查询参数

        Returns:
            List: 月度平均消费数据列表
        """
        try:
            from api.query.MemberConsumeSql import MemberConsumeSqlQueries, MemberConsumeCalculator
            from core.database import db

            monthly_data = []
            bid = self._extract_param(query_params, 'bid')
            sid = self._extract_param(query_params, 'sid', None)

            for month_label, start_date, end_date in time_ranges:
                logger.info(f"获取 {month_label} 平均消费数据: {start_date} 到 {end_date}")

                try:
                    # 转换日期格式为YYYYMMDD
                    start_date_db = start_date.replace('-', '')
                    end_date_db = end_date.replace('-', '')

                    # 1. 获取基础消费数据（用于计算总实收金额和消费笔数）
                    base_sql = MemberConsumeSqlQueries.build_dwoutput_consume_base_sql(
                        start_date_db, end_date_db, bid, sid
                    )
                    base_result = await db.execute_dwoutput_one(base_sql)
                    base_result = base_result if base_result else {}

                    # 2. 获取储值使用详情（用于计算总实收金额）
                    detail_sql = MemberConsumeSqlQueries.build_dwoutput_consume_detail_sql(
                        start_date_db, end_date_db, bid, sid
                    )
                    detail_result = await db.execute_dwoutput_one(detail_sql)
                    detail_result = detail_result if detail_result else {}

                    # 3. 获取会员现金消费笔数和储值消费笔数（从基础数据中提取）
                    cash_consume_count = int(base_result.get('total_consume_cash_pv', 0) or 0)
                    prepay_consume_count = int(base_result.get('total_prepay_pv', 0) or 0)

                    # 4. 获取会员消费人数（用于人均贡献计算）
                    consume_uv_sql = MemberConsumeSqlQueries.build_dwoutput_consume_uv_sql(
                        start_date_db, end_date_db, bid, sid
                    )
                    consume_uv_result = await db.execute_dwoutput_one(consume_uv_sql)
                    consume_users = int(consume_uv_result.get('total_consume_uv', 0) or 0) if consume_uv_result else 0

                    # 5. 获取会员总消费笔数（用于单均消费计算）
                    total_consume_count = int(base_result.get('total_consume_pv', 0) or 0)

                    # 5. 计算各项指标
                    # 5.1 计算总实收金额（用于单均消费和人均贡献计算）
                    consume_cash_real = base_result.get('total_consume_cash_real', 0) or 0
                    prepay_used_real = detail_result.get('total_prepay_used_real', 0) or 0

                    # 为单均消费和人均贡献计算保留元单位的金额
                    consume_cash_real_yuan = float(consume_cash_real) / 100 if consume_cash_real else 0.0
                    prepay_used_real_yuan = float(prepay_used_real) / 100 if prepay_used_real else 0.0
                    total_real_income_yuan = MemberConsumeCalculator.calculate_total_real_income(
                        consume_cash_real_yuan, prepay_used_real_yuan
                    )

                    # 6.2 计算会员人均贡献（使用会员消费人数作为基数）
                    avg_contribution = MemberConsumeCalculator.calculate_avg_contribution_per_user(
                        total_real_income_yuan, consume_users
                    )

                    # 6.3 计算会员单均消费（使用会员总消费笔数作为基数）
                    avg_consume_amount = MemberConsumeCalculator.calculate_avg_consume_amount(
                        total_real_income_yuan, total_consume_count
                    )

                    logger.info(f"{month_label} 数据: 会员现金消费笔数{cash_consume_count}笔, 会员储值消费笔数{prepay_consume_count}笔, "
                              f"会员消费人数{consume_users}人, 会员总消费笔数{total_consume_count}笔, "
                              f"单均消费{avg_consume_amount:.2f}元, 人均贡献{avg_contribution:.2f}元")

                    monthly_data.append({
                        'month': month_label,
                        'cash_consume_count': cash_consume_count,  # 会员现金消费笔数（笔）
                        'prepay_consume_count': prepay_consume_count,  # 会员储值消费笔数（笔）
                        'consume_users': consume_users,  # 会员消费人数（人）
                        'total_consume_count': total_consume_count,  # 会员总消费笔数（笔）
                        'avg_consume_amount': avg_consume_amount,  # 会员单均消费（元/笔）
                        'avg_contribution': avg_contribution  # 会员人均贡献（元/人）
                    })

                except Exception as month_error:
                    logger.error(f"获取 {month_label} 平均消费数据失败: {month_error}")
                    # 添加失败的月份数据，避免图表中断
                    monthly_data.append({
                        'month': month_label,
                        'cash_consume_count': 0,
                        'prepay_consume_count': 0,
                        'consume_users': 0,
                        'total_consume_count': 0,
                        'avg_consume_amount': 0.0,
                        'avg_contribution': 0.0
                    })

            logger.info(f"平均消费数据获取完成，共 {len(monthly_data)} 个月的数据")
            return monthly_data

        except Exception as e:
            logger.error(f"获取月度平均消费数据失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    @collect_chart_data("avg_consumption", include_comparison=True)
    async def _generate_chart(self, data: List[Dict[str, Any]], title: str, image_type: str) -> str:
        """
        生成平均消费数据组合图表

        Args:
            data: 月度数据
            title: 图表标题
            image_type: 图片类型

        Returns:
            str: 图片保存路径
        """
        try:
            if not data:
                logger.warning("数据为空，无法生成图表")
                return ""

            # 提取数据
            months = [item['month'] for item in data]
            cash_consume_counts = [item['cash_consume_count'] for item in data]
            prepay_consume_counts = [item['prepay_consume_count'] for item in data]
            avg_consume_amounts = [item['avg_consume_amount'] for item in data]
            avg_contributions = [item['avg_contribution'] for item in data]

            # 创建图表
            fig, ax1 = plt.subplots(figsize=(14, 8))

            # 设置X轴位置
            x_pos = range(len(months))
            x = np.arange(len(months))

            # 绘制柱状图（左Y轴 - 人数/笔数）
            bar_width = 0.25

            # 绘制柱状图（会员现金消费笔数和会员储值消费笔数）
            bars1 = ax1.bar([x - bar_width for x in x_pos], cash_consume_counts,
                           bar_width, label='会员现金消费笔数', color='#4472C4', alpha=0.8)
            bars2 = ax1.bar([x + bar_width for x in x_pos], prepay_consume_counts,
                           bar_width, label='会员储值消费笔数', color='#E74C3C', alpha=0.8)

            # 设置左Y轴
            ax1.set_xlabel('月份', fontsize=12)
            ax1.set_ylabel('笔数', fontsize=12)

            ax1.set_xticks(x_pos)
            # 去掉年份前缀，只保留月份，水平显示
            month_labels = [month.split('-')[-1] + '月' if '-' in month else month for month in months]
            ax1.set_xticklabels(month_labels, rotation=0, ha='center')
            ax1.grid(True, alpha=0.3)

            # 创建右Y轴（单均消费和人均贡献）
            ax2 = ax1.twinx()

            # 绘制折线图（会员单均消费和会员人均贡献）
            line1 = ax2.plot(x_pos, avg_consume_amounts, 'o-', color='#000000', linewidth=2,
                            markersize=6, label='会员单均消费')
            line2 = ax2.plot(x_pos, avg_contributions, 's-', color='#32CD32', linewidth=2,
                            markersize=6, label='会员人均贡献')

            # 设置右Y轴
            ax2.set_ylabel('单均消费/人均贡献（元）', fontsize=12)
            # 设置右Y轴范围，处理全零数据的情况
            max_amount_value = max(avg_consume_amounts + avg_contributions) if (avg_consume_amounts + avg_contributions) and max(avg_consume_amounts + avg_contributions) > 0 else 10
            ax2.set_ylim(0, max_amount_value * 1.2)

            # 在柱状图上添加数值标签（智能避免重叠）
            max_bar_value = max(cash_consume_counts + prepay_consume_counts) if (cash_consume_counts + prepay_consume_counts) else 10

            # 为两组柱状图分别添加智能标签
            cash_x_pos = [i - bar_width for i in range(len(cash_consume_counts))]
            prepay_x_pos = [i + bar_width for i in range(len(prepay_consume_counts))]

            add_smart_bar_labels(ax1, cash_consume_counts, max_bar_value, cash_x_pos, fontsize=9, decimal_places=0, reset_positions=True)
            add_smart_bar_labels(ax1, prepay_consume_counts, max_bar_value, prepay_x_pos, fontsize=9, decimal_places=0, reset_positions=False)

            # 在折线图上添加数值标签（智能避免重叠）
            max_line_value = max(avg_consume_amounts + avg_contributions) if (avg_consume_amounts + avg_contributions) else 1

            # 为两条折线分别添加智能标签
            add_smart_line_labels(ax2, avg_consume_amounts, max_line_value, fontsize=10, color='#000000', decimal_places=2, reset_positions=False)
            add_smart_line_labels(ax2, avg_contributions, max_line_value, fontsize=10, color='#32CD32', decimal_places=2, reset_positions=False)

            # 合并图例 - 放在图表外部顶部
            lines1, labels1 = ax1.get_legend_handles_labels()
            lines2, labels2 = ax2.get_legend_handles_labels()
            ax1.legend(lines1 + lines2, labels1 + labels2,
                      bbox_to_anchor=(0.5, 1.02), loc='lower center',
                      ncol=len(labels1 + labels2), fontsize=11)

            # 添加数据表格
            self._add_avg_consumption_data_table(data, ax1)

            # 调整布局
            plt.tight_layout()

            # 保存图片
            file_path = self.image_manager.get_image_path(image_type)
            plt.savefig(file_path, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close(fig)

            logger.info(f"平均消费数据图表生成完成: {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"生成平均消费数据图表失败: {e}")
            import traceback
            traceback.print_exc()
            return ""

    def _add_value_labels(self, ax, bars, values):
        """
        为柱状图添加数值标签

        Args:
            ax: 坐标轴对象
            bars: 柱状图对象
            values: 数值列表
        """
        for bar, value in zip(bars, values):
            if value > 0:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                       f'{value:,.0f}', ha='center', va='bottom', fontsize=9)

    def _add_line_labels(self, ax, x_positions, values, color):
        """
        为折线图添加数值标签

        Args:
            ax: 坐标轴对象
            x_positions: x轴位置
            values: 数值列表
            color: 标签颜色
        """
        for x, value in zip(x_positions, values):
            if value > 0:
                ax.text(x, value + max(values)*0.02, f'{value:.2f}',
                       ha='center', va='bottom', fontsize=9, color=color, fontweight='bold')

    def _add_avg_consumption_data_table(self, data: List[Dict[str, Any]], ax):
        """
        在图表下方添加数据表格（横向布局，时间为横轴）

        Args:
            data: 数据列表
            ax: 坐标轴对象
        """
        try:
            if not data:
                return

            # 准备横向表格数据
            months = [item['month'] for item in data]
            cash_consume_counts_data = [f"{item['cash_consume_count']:,}" for item in data]
            prepay_consume_counts_data = [f"{item['prepay_consume_count']:,}" for item in data]
            avg_amounts = [f"{item['avg_consume_amount']:,.2f}" for item in data]
            avg_contributions = [f"{item['avg_contribution']:,.2f}" for item in data]

            # 构建横向表格数据：第一行是月份，后面四行是数据
            table_data = [
                months,  # 第一行：月份
                cash_consume_counts_data,  # 第二行：会员现金消费笔数
                prepay_consume_counts_data,  # 第三行：会员储值消费笔数
                avg_amounts,  # 第四行：会员单均消费
                avg_contributions  # 第五行：会员人均贡献
            ]

            # 行标题（左侧标签）
            row_labels = ['月份', '会员现金消费笔数(笔)', '会员储值消费笔数(笔)', '会员单均消费(元/笔)', '会员人均贡献(元/人)']

            # 创建表格
            table = ax.table(
                cellText=table_data,
                rowLabels=row_labels,
                cellLoc='center',
                loc='bottom',
                bbox=[0, -0.9, 1, 0.6]  # [x, y, width, height] - 向下移动避免遮挡图表
            )

            # 设置表格样式
            table.auto_set_font_size(False)
            table.set_fontsize(9)  # 增大字体大小提高可读性
            table.scale(1, 1.2)

            # 设置行标题样式（左侧列）
            for i in range(len(row_labels)):
                table[(i, -1)].set_facecolor('#4472C4')
                table[(i, -1)].set_text_props(weight='bold', color='white')

            # 设置数据单元格样式
            for i in range(len(row_labels)):
                for j in range(len(months)):
                    if i == 0:  # 月份行使用浅蓝色
                        table[(i, j)].set_facecolor('#E7F3FF')
                        table[(i, j)].set_text_props(weight='bold')
                    elif i % 2 == 1:  # 奇数行使用浅灰色
                        table[(i, j)].set_facecolor('#F8F9FA')
                    else:  # 偶数行使用白色
                        table[(i, j)].set_facecolor('white')

            logger.info(f"横向数据表格创建完成，包含 {len(months)} 个月的数据")

        except Exception as e:
            logger.error(f"添加数据表格失败: {e}")



def create_avg_consumption_pic_generator(bid: str, image_manager):
    """
    创建会员平均消费数据图片生成器

    Args:
        bid: 品牌ID
        image_manager: 图片管理器实例

    Returns:
        AvgConsumptionPicGenerator: 图片生成器实例
    """
    return AvgConsumptionPicGenerator(bid, image_manager)