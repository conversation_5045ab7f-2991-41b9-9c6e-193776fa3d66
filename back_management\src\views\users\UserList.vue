<template>
  <div class="user-list-container">
    <h2 class="page-title">用户管理</h2>
    
    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" @submit.prevent="handleSearch">
        <el-form-item label="用户名">
          <el-input
            v-model="searchForm.username"
            placeholder="请输入用户名"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        
        <el-form-item label="角色">
          <el-select
            v-model="searchForm.role"
            placeholder="全部角色"
            clearable
          >
            <el-option label="超级管理员" value="super_admin" />
            <el-option label="管理员" value="admin" />
            <el-option label="普通用户" value="user" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="全部状态"
            clearable
          >
            <el-option label="启用" value="active" />
            <el-option label="禁用" value="inactive" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><RefreshRight /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增用户
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 用户列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="userList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="用户名" min-width="120" />
        <el-table-column prop="role" label="角色" width="120">
          <template #default="{ row }">
            <el-tag :type="getRoleType(row.role)">
              {{ getRoleLabel(row.role) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="account_type" label="账户类别" width="150">
          <template #default="{ row }">
            <el-tag :type="getAccountTypeStyle(row.account_type)">
              {{ getAccountTypeLabel(row.account_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="expire_time" label="到期时间" width="160">
          <template #default="{ row }">
            <span v-if="row.expire_time" :style="{ color: isExpired(row.expire_time) ? '#F56C6C' : '#67C23A' }">
              {{ formatDateTime(row.expire_time) }}
            </span>
            <span v-else style="color: #909399">永久有效</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="PPT生成配额" min-width="200">
          <template #default="{ row }">
            <div class="quota-info">
              <!-- 只有设置了配额限制时才显示进度条 -->
              <el-progress
                v-if="row.quotas && row.quotas > 0"
                :percentage="getPPTQuotaPercentage(row)"
                :status="getPPTQuotaStatus(row)"
              />
              <span style="font-size: 12px; color: #909399">
                已用: {{ getPPTUsedQuota(row) }} / 限额: {{ getPPTQuotaLimit(row) }}
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              text
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              type="warning"
              size="small"
              text
              @click="handleResetPassword(row)"
            >
              重置密码
            </el-button>
            <el-button
              :type="row.status === 'active' ? 'danger' : 'success'"
              size="small"
              text
              @click="handleToggleStatus(row)"
            >
              {{ row.status === 'active' ? '禁用' : '启用' }}
            </el-button>
            <el-button
              type="danger"
              size="small"
              text
              @click="handleDelete(row)"
              :disabled="row.role === 'super_admin'"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        style="margin-top: 20px"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </el-card>
    
    <!-- 用户编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      @closed="resetForm"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="userForm.username"
            placeholder="请输入用户名"
            :disabled="isEdit"
          />
        </el-form-item>
        
        <el-form-item label="密码" prop="password" v-if="!isEdit">
          <el-input
            v-model="userForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="真实姓名" prop="real_name">
          <el-input
            v-model="userForm.real_name"
            placeholder="请输入真实姓名"
          />
        </el-form-item>
        
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" placeholder="请选择角色">
            <el-option label="管理员" value="admin" />
            <el-option label="普通用户" value="user" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="账户类别" prop="account_type">
          <el-select v-model="userForm.account_type" placeholder="请选择账户类别">
            <el-option label="常规代理商" value="regular_agent" />
            <el-option label="直营代理商" value="direct_agent" />
            <el-option label="奥琦玮直管运营" value="aoqiwei_operation" />
            <el-option label="商户超级管理员" value="merchant_super_admin" />
            <el-option label="商户普通号" value="merchant_user" />
          </el-select>
        </el-form-item>
        
        <el-form-item v-if="dialogMode === 'add'" label="有效期设置" prop="expireMode">
          <el-radio-group v-model="userForm.expireMode" style="margin-bottom: 10px">
            <el-radio label="permanent">永久有效</el-radio>
            <el-radio label="duration">设置时长</el-radio>
            <el-radio label="deadline">设置截止时间</el-radio>
          </el-radio-group>
          
          <div v-if="userForm.expireMode === 'duration'" style="margin-top: 10px">
            <el-input-number
              v-model="userForm.duration_days"
              :min="1"
              :max="36500"
              :step="30"
              :precision="0"
              controls-position="right"
              style="width: 200px"
            />
            <span style="margin-left: 10px; color: #909399; font-size: 12px">
              天数
            </span>
          </div>
          
          <div v-if="userForm.expireMode === 'deadline'" style="margin-top: 10px">
            <el-date-picker
              v-model="userForm.expire_time"
              type="datetime"
              placeholder="选择到期时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </div>
        </el-form-item>
        
        <el-form-item v-if="dialogMode === 'edit'" label="到期时间" prop="expire_time">
          <el-date-picker
            v-model="userForm.expire_time"
            type="datetime"
            placeholder="选择到期时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
          <span style="margin-left: 10px; color: #909399; font-size: 12px">
            留空表示永久有效
          </span>
        </el-form-item>
        
        <el-form-item label="PPT生成配额" prop="ppt_quota">
          <el-input-number
            v-model="userForm.ppt_quota"
            :min="0"
            :max="999999"
            :step="1"
            :precision="0"
            controls-position="right"
            style="width: 200px"
          />
          <span style="margin-left: 10px; color: #909399; font-size: 12px">
            0表示无限制，其他数值为每月PPT生成次数限制
          </span>
        </el-form-item>
        
        <el-form-item label="BID权限" prop="bid_access">
          <el-select
            v-model="userForm.bid_access"
            multiple
            placeholder="请选择可访问的BID"
            style="width: 100%"
          >
            <el-option
              v-for="bid in bidOptions"
              :key="bid.bid"
              :label="`${bid.bid} - ${bid.name}`"
              :value="bid.bid"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="userForm.status">
            <el-radio label="active">启用</el-radio>
            <el-radio label="inactive">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 重置密码对话框 -->
    <el-dialog
      v-model="resetPasswordVisible"
      title="重置密码"
      width="400px"
    >
      <el-form
        ref="resetPasswordFormRef"
        :model="resetPasswordForm"
        :rules="resetPasswordRules"
        label-width="100px"
      >
        <el-form-item label="用户名">
          <el-input v-model="resetPasswordForm.username" disabled />
        </el-form-item>
        
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="resetPasswordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="resetPasswordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="resetPasswordVisible = false">取消</el-button>
          <el-button type="primary" @click="handleResetPasswordSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { usersAPI } from '@/api/users'
import { bidAPI } from '@/api/bid'
import { ElMessage, ElMessageBox } from 'element-plus'

// 搜索表单
const searchForm = reactive({
  username: '',
  role: '',
  status: ''
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 用户列表
const userList = ref([])
const loading = ref(false)
const selectedUsers = ref([])

// 对话框
const dialogVisible = ref(false)
const dialogTitle = ref('新增用户')
const isEdit = ref(false)
const dialogMode = ref('add') // 'add' or 'edit'
const userFormRef = ref()

// 用户表单
const userForm = reactive({
  id: null,
  username: '',
  password: '',
  real_name: '',
  role: 'user',
  account_type: 'merchant_user',
  expireMode: 'permanent', // permanent, duration, deadline
  duration_days: null,
  expire_time: null,
  status: 'active',
  ppt_quota: 0,
  bid_access: []
})

// 表单验证规则
const userRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 重置密码
const resetPasswordVisible = ref(false)
const resetPasswordFormRef = ref()
const resetPasswordForm = reactive({
  userId: null,
  username: '',
  newPassword: '',
  confirmPassword: ''
})

// 重置密码验证规则
const resetPasswordRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (value !== resetPasswordForm.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// BID选项
const bidOptions = ref([])

// 获取用户列表
const fetchUserList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      limit: pagination.pageSize,
      ...searchForm
    }
    
    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === '') {
        delete params[key]
      }
    })
    
    const response = await usersAPI.getUsers(params)
    userList.value = response.items || []
    pagination.total = response.total || 0
  } catch (error) {
    ElMessage.error('获取用户列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 获取BID选项
const fetchBidOptions = async () => {
  try {
    const response = await bidAPI.getBidConfigs({ limit: 100 })
    bidOptions.value = response.items || []
  } catch (error) {
    console.error('获取BID选项失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchUserList()
}

// 重置搜索
const handleReset = () => {
  searchForm.username = ''
  searchForm.role = ''
  searchForm.status = ''
  handleSearch()
}

// 新增用户
const handleAdd = () => {
  isEdit.value = false
  dialogMode.value = 'add'
  dialogTitle.value = '新增用户'
  dialogVisible.value = true
}

// 编辑用户
const handleEdit = (row) => {
  isEdit.value = true
  dialogMode.value = 'edit'
  dialogTitle.value = '编辑用户'
  // 从user_bid_permissions提取BID列表
  const bidList = row.user_bid_permissions ? 
    row.user_bid_permissions.map(perm => perm.bid) : []
  
  Object.assign(userForm, {
    id: row.id,
    username: row.username,
    real_name: row.real_name || '',
    role: row.role,
    account_type: row.account_type || 'merchant_user',
    expire_time: row.expire_time || null,
    duration_days: null, // 编辑时不使用duration_days
    status: row.status,
    ppt_quota: row.quotas || 0,  // 直接使用数值
    bid_access: bidList
  })
  dialogVisible.value = true
}

// 提交表单
const handleSubmit = async () => {
  const valid = await userFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  try {
    if (isEdit.value) {
      await usersAPI.updateUser(userForm.id, {
        real_name: userForm.real_name,
        role: userForm.role,
        account_type: userForm.account_type,
        expire_time: userForm.expire_time,
        status: userForm.status,
        quotas: userForm.ppt_quota || 0,  // 直接传递数值
        bid_permissions: userForm.bid_access.map(bid => ({
          bid: bid,
          permissions: ['read', 'write']  // 默认授予读写权限
        }))
      })
      ElMessage.success('更新成功')
    } else {
      const createData = {
        username: userForm.username,
        password: userForm.password,
        real_name: userForm.real_name,
        role: userForm.role,
        account_type: userForm.account_type,
        status: userForm.status,
        quotas: userForm.ppt_quota || 0,  // 直接传递数值
        bid_permissions: userForm.bid_access.map(bid => ({
          bid: bid,
          permissions: ['read', 'write']  // 默认授予读写权限
        }))
      }
      
      // 根据有效期设置模式处理
      if (userForm.expireMode === 'duration' && userForm.duration_days) {
        createData.duration_days = userForm.duration_days
      } else if (userForm.expireMode === 'deadline' && userForm.expire_time) {
        createData.expire_time = userForm.expire_time
      }
      // permanent模式不传递任何时间字段，后端会设置为NULL（永久有效）
      
      await usersAPI.createUser(createData)
      ElMessage.success('创建成功')
    }
    dialogVisible.value = false
    fetchUserList()
  } catch (error) {
    ElMessage.error(error.response?.data?.detail || '操作失败')
  }
}

// 重置表单
const resetForm = () => {
  userFormRef.value?.resetFields()
  Object.assign(userForm, {
    id: null,
    username: '',
    password: '',
    real_name: '',
    role: 'user',
    account_type: 'merchant_user',
    expireMode: 'permanent',
    duration_days: null,
    expire_time: null,
    status: 'active',
    ppt_quota: 0,
    bid_access: []
  })
  dialogMode.value = 'add'
}

// 切换用户状态
const handleToggleStatus = async (row) => {
  const newStatus = row.status === 'active' ? 'inactive' : 'active'
  const action = newStatus === 'active' ? '启用' : '禁用'
  
  try {
    await ElMessageBox.confirm(
      `确定要${action}用户 ${row.username} 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await usersAPI.updateUserStatus(row.id, newStatus)
    ElMessage.success(`${action}成功`)
    fetchUserList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`${action}失败`)
    }
  }
}

// 重置密码
const handleResetPassword = (row) => {
  resetPasswordForm.userId = row.id
  resetPasswordForm.username = row.username
  resetPasswordForm.newPassword = ''
  resetPasswordForm.confirmPassword = ''
  resetPasswordVisible.value = true
}

// 提交重置密码
const handleResetPasswordSubmit = async () => {
  const valid = await resetPasswordFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  try {
    await usersAPI.resetPassword(resetPasswordForm.userId, resetPasswordForm.newPassword)
    ElMessage.success('密码重置成功')
    resetPasswordVisible.value = false
  } catch (error) {
    ElMessage.error('密码重置失败')
  }
}

// 删除用户
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 ${row.username} 吗？删除后数据不可恢复！`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await usersAPI.deleteUser(row.id)
    ElMessage.success('删除成功')
    fetchUserList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 批量选择
const handleSelectionChange = (selection) => {
  selectedUsers.value = selection
}

// 分页大小改变
const handleSizeChange = (val) => {
  pagination.pageSize = val
  fetchUserList()
}

// 页码改变
const handlePageChange = (val) => {
  pagination.page = val
  fetchUserList()
}

// 获取角色标签
const getRoleLabel = (role) => {
  const roleMap = {
    'super_admin': '超级管理员',
    'admin': '管理员',
    'user': '普通用户'
  }
  return roleMap[role] || role
}

// 获取角色类型
const getRoleType = (role) => {
  const typeMap = {
    'super_admin': 'danger',
    'admin': 'warning',
    'user': 'info'
  }
  return typeMap[role] || 'info'
}

// PPT配额相关函数
const getPPTQuotaLimit = (row) => {
  // 如果quotas为0或null/undefined，显示"无限制"
  if (!row.quotas || row.quotas === 0) {
    return '无限制'
  }
  return row.quotas
}

const getPPTUsedQuota = (row) => {
  return row.use_quotas || 0
}

const getPPTQuotaPercentage = (row) => {
  // 如果没有配额限制（0或null），不显示进度
  if (!row.quotas || row.quotas === 0) {
    return 0
  }
  const used = row.use_quotas || 0
  return Math.min(100, Math.round((used / row.quotas) * 100))
}

const getPPTQuotaStatus = (row) => {
  // 如果没有配额限制，总是返回success
  if (!row.quotas || row.quotas === 0) {
    return 'success'
  }
  const percentage = getPPTQuotaPercentage(row)
  if (percentage >= 90) return 'exception'
  if (percentage >= 70) return 'warning'
  return 'success'
}

// 已移除查询配额相关函数，系统只有PPT生成配额

// 格式化日期时间
const formatDateTime = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

// 获取账户类别标签
const getAccountTypeLabel = (type) => {
  const typeMap = {
    'regular_agent': '常规代理商',
    'direct_agent': '直营代理商',
    'aoqiwei_operation': '奥琦玮直管运营',
    'merchant_super_admin': '商户超级管理员',
    'merchant_user': '商户普通号'
  }
  return typeMap[type] || type || '商户普通号'
}

// 获取账户类别样式
const getAccountTypeStyle = (type) => {
  const styleMap = {
    'regular_agent': 'primary',
    'direct_agent': 'success',
    'aoqiwei_operation': 'danger',
    'merchant_super_admin': 'warning',
    'merchant_user': 'info'
  }
  return styleMap[type] || 'info'
}

// 判断账户是否过期
const isExpired = (expireTime) => {
  if (!expireTime) return false
  return new Date(expireTime) < new Date()
}

onMounted(() => {
  fetchUserList()
  fetchBidOptions()
})
</script>

<style scoped>
.user-list-container {
  padding: 20px;
}

.page-title {
  margin: 0 0 20px 0;
  font-size: 24px;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  min-height: 500px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.quota-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.quota-info .el-progress {
  width: 100%;
}
</style>