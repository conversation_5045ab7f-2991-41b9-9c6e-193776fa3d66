<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配额显示测试 - Element Plus</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>
    <style>
        #app {
            padding: 20px;
        }
        .quota-info {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div id="app">
        <h1>配额显示测试</h1>
        
        <div class="test-section">
            <h2>测试数据</h2>
            <el-table :data="testData" style="width: 100%">
                <el-table-column prop="username" label="用户名" width="120" />
                <el-table-column prop="quotas" label="原始quotas值" width="120" />
                <el-table-column prop="use_quotas" label="原始use_quotas值" width="140" />
                <el-table-column label="PPT生成配额" min-width="250">
                    <template #default="{ row }">
                        <div class="quota-info">
                            <!-- 只有设置了配额限制时才显示进度条 -->
                            <el-progress
                                v-if="row.quotas && row.quotas > 0"
                                :percentage="getPPTQuotaPercentage(row)"
                                :status="getPPTQuotaStatus(row)"
                            />
                            <span style="font-size: 12px; color: #909399">
                                已用: {{ getPPTUsedQuota(row) }} / 限额: {{ getPPTQuotaLimit(row) }}
                            </span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="计算结果" width="180">
                    <template #default="{ row }">
                        <div>
                            <div>百分比: {{ getPPTQuotaPercentage(row) }}%</div>
                            <div>状态: {{ getPPTQuotaStatus(row) }}</div>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <div class="test-section">
            <h2>配额编辑测试</h2>
            <el-form :model="editForm" label-width="120px" style="max-width: 600px">
                <el-form-item label="用户名">
                    <el-input v-model="editForm.username" />
                </el-form-item>
                <el-form-item label="PPT生成配额">
                    <el-input-number
                        v-model="editForm.ppt_quota"
                        :min="0"
                        :max="999999"
                        :step="1"
                        :precision="0"
                        controls-position="right"
                        style="width: 200px"
                    />
                    <span style="margin-left: 10px; color: #909399; font-size: 12px">
                        0表示无限制，其他数值为每月PPT生成次数限制
                    </span>
                </el-form-item>
                <el-form-item label="已使用次数">
                    <el-input-number
                        v-model="editForm.use_quotas"
                        :min="0"
                        :max="999999"
                        :step="1"
                        :precision="0"
                        controls-position="right"
                        style="width: 200px"
                    />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="addTestData">添加到测试数据</el-button>
                </el-form-item>
            </el-form>
        </div>

        <div class="test-section">
            <h2>函数测试结果</h2>
            <el-descriptions :column="2" border>
                <el-descriptions-item label="测试用例1: quotas=0, use_quotas=50">
                    {{ testFunction({ quotas: 0, use_quotas: 50 }) }}
                </el-descriptions-item>
                <el-descriptions-item label="测试用例2: quotas=null, use_quotas=30">
                    {{ testFunction({ quotas: null, use_quotas: 30 }) }}
                </el-descriptions-item>
                <el-descriptions-item label="测试用例3: quotas=100, use_quotas=50">
                    {{ testFunction({ quotas: 100, use_quotas: 50 }) }}
                </el-descriptions-item>
                <el-descriptions-item label="测试用例4: quotas=100, use_quotas=95">
                    {{ testFunction({ quotas: 100, use_quotas: 95 }) }}
                </el-descriptions-item>
            </el-descriptions>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            data() {
                return {
                    testData: [
                        { username: '无限制用户1', quotas: 0, use_quotas: 100 },
                        { username: '无限制用户2', quotas: null, use_quotas: 50 },
                        { username: '正常用户', quotas: 100, use_quotas: 50 },
                        { username: '警告用户', quotas: 100, use_quotas: 75 },
                        { username: '危险用户', quotas: 100, use_quotas: 95 },
                        { username: '超额用户', quotas: 100, use_quotas: 120 },
                        { username: '新用户', quotas: 200, use_quotas: 0 }
                    ],
                    editForm: {
                        username: '测试用户',
                        ppt_quota: 0,
                        use_quotas: 0
                    }
                };
            },
            methods: {
                // PPT配额相关函数
                getPPTQuotaLimit(row) {
                    // 如果quotas为0或null/undefined，显示"无限制"
                    if (!row.quotas || row.quotas === 0) {
                        return '无限制';
                    }
                    return row.quotas;
                },
                
                getPPTUsedQuota(row) {
                    return row.use_quotas || 0;
                },
                
                getPPTQuotaPercentage(row) {
                    // 如果没有配额限制（0或null），不显示进度
                    if (!row.quotas || row.quotas === 0) {
                        return 0;
                    }
                    const used = row.use_quotas || 0;
                    return Math.min(100, Math.round((used / row.quotas) * 100));
                },
                
                getPPTQuotaStatus(row) {
                    // 如果没有配额限制，总是返回success
                    if (!row.quotas || row.quotas === 0) {
                        return 'success';
                    }
                    const percentage = this.getPPTQuotaPercentage(row);
                    if (percentage >= 90) return 'exception';
                    if (percentage >= 70) return 'warning';
                    return 'success';
                },
                
                addTestData() {
                    this.testData.push({
                        username: this.editForm.username,
                        quotas: this.editForm.ppt_quota,
                        use_quotas: this.editForm.use_quotas
                    });
                    ElMessage.success('添加成功');
                },
                
                testFunction(row) {
                    return `限额: ${this.getPPTQuotaLimit(row)}, 已用: ${this.getPPTUsedQuota(row)}, 百分比: ${this.getPPTQuotaPercentage(row)}%, 状态: ${this.getPPTQuotaStatus(row)}`;
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>