"""
认证相关的Pydantic模型
"""
from typing import Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator
from .user import UserRole, UserResponse


class LoginRequest(BaseModel):
    """登录请求模型"""
    username: str = Field(..., description="用户名或邮箱")
    password: str = Field(..., description="密码")
    remember_me: bool = Field(False, description="记住我")
    
    @validator('username')
    def validate_username(cls, v):
        """验证用户名"""
        if not v or len(v.strip()) == 0:
            raise ValueError('用户名不能为空')
        return v.strip()


class TokenData(BaseModel):
    """Token数据模型"""
    user_id: int
    username: str
    role: UserRole
    bid_permissions: Optional[Dict[str, Any]] = None
    exp: Optional[datetime] = None


class TokenResponse(BaseModel):
    """Token响应模型"""
    access_token: str = Field(..., description="访问Token")
    refresh_token: Optional[str] = Field(None, description="刷新Token")
    token_type: str = Field("Bearer", description="Token类型")
    expires_in: int = Field(..., description="过期时间（秒）")


class LoginResponse(BaseModel):
    """登录响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="消息")
    user: Optional[UserResponse] = Field(None, description="用户信息")
    token: Optional[TokenResponse] = Field(None, description="Token信息")


class RefreshTokenRequest(BaseModel):
    """刷新Token请求"""
    refresh_token: str = Field(..., description="刷新Token")


class LogoutRequest(BaseModel):
    """登出请求"""
    token: Optional[str] = Field(None, description="要注销的Token")
    all_devices: bool = Field(False, description="是否登出所有设备")


class PasswordResetRequest(BaseModel):
    """密码重置请求"""
    email: str = Field(..., description="注册邮箱")
    
    @validator('email')
    def validate_email(cls, v):
        """验证邮箱"""
        if '@' not in v:
            raise ValueError('请输入有效的邮箱地址')
        return v.lower()


class PasswordResetConfirm(BaseModel):
    """确认密码重置"""
    reset_token: str = Field(..., description="重置Token")
    new_password: str = Field(..., min_length=6, description="新密码")
    
    @validator('new_password')
    def validate_password(cls, v):
        """验证密码强度"""
        if len(v) < 6:
            raise ValueError('密码长度至少6位')
        return v


class SuperAdminInit(BaseModel):
    """超级管理员初始化"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    password: str = Field(..., min_length=6, description="密码")
    email: str = Field(..., description="邮箱")
    real_name: str = Field(..., description="真实姓名")
    init_key: Optional[str] = Field(None, description="初始化密钥（如果配置了）")
    
    @validator('password')
    def validate_password(cls, v):
        """验证密码强度"""
        if len(v) < 8:
            raise ValueError('超级管理员密码长度至少8位')
        # 检查是否包含数字和字母
        has_digit = any(c.isdigit() for c in v)
        has_letter = any(c.isalpha() for c in v)
        if not (has_digit and has_letter):
            raise ValueError('密码必须包含字母和数字')
        return v


class SessionInfo(BaseModel):
    """会话信息"""
    session_id: int
    user_id: int
    token: str
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    expires_at: datetime
    is_active: bool
    created_at: datetime
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.strftime("%Y-%m-%d %H:%M:%S") if v else None
        }


class PermissionCheck(BaseModel):
    """权限检查请求"""
    user_id: int
    resource_type: str = Field(..., description="资源类型")
    resource_id: Optional[str] = Field(None, description="资源ID")
    action: str = Field(..., description="操作类型")
    bid: Optional[str] = Field(None, description="BID")
    sid: Optional[str] = Field(None, description="SID")


class PermissionCheckResponse(BaseModel):
    """权限检查响应"""
    allowed: bool = Field(..., description="是否允许")
    reason: Optional[str] = Field(None, description="原因")
    required_permission: Optional[str] = Field(None, description="需要的权限")