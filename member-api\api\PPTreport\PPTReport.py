# -*- coding: utf-8 -*-
"""
PPT报告生成服务
协调各个模块完成PPT生成
"""

import logging
import datetime
from typing import Dict, Any, Optional

from core.models import QueryParams
from services.ppt_service import ppt_service
from .DataAcquisition import data_acquisition_service
from .constants import PPTConstants
from .PicAcquisition import pic_acquisition_service

logger = logging.getLogger(__name__)

class PPTReportService:
    """PPT报告生成服务类"""

    def __init__(self):
        """初始化PPT报告服务"""
        self.ppt_service = ppt_service
        self.data_service = data_acquisition_service
        self.constants = PPTConstants
        
        # 初始化OSS服务（延迟导入，避免循环依赖）
        try:
            from services.oss_service import oss_service
            self.oss_service = oss_service
        except Exception as e:
            logger.warning(f"OSS服务初始化失败，将使用本地存储: {e}")
            self.oss_service = None

        logger.info("PPT报告服务初始化完成")

    async def generate_member_report(
        self,
        query_params: QueryParams,
        output_filename: Optional[str] = None,
        task_uuid: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        生成会员数据报告PPT

        Args:
            query_params: 查询参数
            output_filename: 输出文件名（可选）
            task_uuid: 任务UUID（用于创建独立文件夹）

        Returns:
            Dict: 生成结果
        """
        try:
            logger.info(f"开始生成会员数据报告PPT - 查询参数: {query_params}")

            # 0. 生成自定义文件名（如果未提供）
            if not output_filename:
                output_filename = self._generate_custom_filename(query_params)
                logger.info(f"生成自定义文件名: {output_filename}")

            # 1. 获取所有数据（包括行业分析数据）
            logger.info("步骤1: 获取数据...")
            ppt_data = await self.data_service.get_all_ppt_data(query_params)

            # 2. 转换为PPT参数（包含AI分析）
            logger.info("步骤2: 转换数据为PPT参数...")
            ppt_params = await self._convert_to_ppt_params(ppt_data, query_params)
            
            # 添加查询参数到PPT参数中
            ppt_params["query_params"] = ppt_data.get("query_params", {})
            if task_uuid:
                ppt_params["query_params"]["task_uuid"] = task_uuid

            # 3. 验证参数完整性
            logger.info("步骤3: 验证参数完整性...")
            validation_result = self.constants.validate_required_params(ppt_params)

            if not validation_result["valid"]:
                logger.warning(f"PPT参数验证失败，缺少参数: {validation_result['missing_params']}")
                # 记录缺失的参数，但不使用虚拟数据填充
                # 只对关键参数进行最小化的默认值设置
                for missing_param in validation_result["missing_params"]:
                    if missing_param == "time_frame":
                        ppt_params[missing_param] = "数据查询期间"
                    elif missing_param == "report_date":
                        import datetime
                        ppt_params[missing_param] = datetime.datetime.now().strftime("%Y年%m月%d日")
                    else:
                        # 对于数据参数，设置为空字符串或0，不使用虚拟数据
                        if "count" in missing_param or "amount" in missing_param or "members" in missing_param:
                            ppt_params[missing_param] = "0"
                        else:
                            ppt_params[missing_param] = ""
                        logger.warning(f"参数 '{missing_param}' 缺失，设置为默认值")

            # 4. 生成PPT
            logger.info("步骤4: 生成PPT文件...")
            generation_result = self.ppt_service.generate_ppt_report(ppt_params, output_filename, task_uuid)

            if generation_result["success"]:
                logger.info(f"PPT报告生成成功: {generation_result['file_path']}")
                
                # 5. 生成Excel数据文件
                logger.info("步骤5: 生成数据Excel文件...")
                data_download_url = None
                
                try:
                    from .excel import ExcelGenerator
                    from pathlib import Path
                    import os
                    import datetime as dt
                    import shutil
                    
                    # CSV文件在图片生成器创建的temp目录中
                    bid = query_params.bid
                    date_str = dt.datetime.now().strftime("%Y%m%d")
                    csv_session_dir = Path(f"uploads/temp/{bid}_{date_str}")
                    
                    # 检查CSV目录是否存在
                    if not csv_session_dir.exists():
                        logger.warning(f"CSV数据目录不存在: {csv_session_dir}")
                        # 尝试查找最近的目录
                        temp_dir = Path("uploads/temp")
                        if temp_dir.exists():
                            bid_dirs = list(temp_dir.glob(f"{bid}_*"))
                            if bid_dirs:
                                # 使用最新的目录
                                csv_session_dir = sorted(bid_dirs, key=lambda x: x.stat().st_mtime)[-1]
                                logger.info(f"使用最新的CSV目录: {csv_session_dir}")
                    
                    # 从包含CSV文件的目录生成Excel
                    excel_generator = ExcelGenerator(csv_session_dir)
                    excel_filename = (output_filename or generation_result["file_name"]).replace('.pptx', '.xlsx')
                    
                    # 在CSV目录生成Excel
                    excel_path = await excel_generator.generate_excel_with_comparison(excel_filename)
                    
                    # 处理Excel文件
                    if excel_path and os.path.exists(excel_path):
                        # 获取任务文件夹
                        task_folder = generation_result.get("task_folder")
                        
                        if task_folder:
                            # 将Excel复制到任务文件夹
                            target_dir = Path(f"uploads/ppt-reports/{task_folder}")
                            target_dir.mkdir(parents=True, exist_ok=True)
                            target_excel_path = target_dir / excel_filename
                            
                            # 复制Excel文件到任务文件夹
                            shutil.copy2(excel_path, target_excel_path)
                            logger.info(f"Excel文件复制到任务文件夹: {target_excel_path}")
                            
                            # 生成下载URL
                            data_download_url = f"/api/ppt-report/download/ppt-reports/{task_folder}/{excel_filename}"
                            
                            # 尝试上传Excel到OSS（参考PPT的上传方式）
                            try:
                                if hasattr(self, 'oss_service') and self.oss_service and self.oss_service.oss_enabled and self.oss_service.bucket:
                                    logger.info(f"开始上传Excel文件到OSS: {target_excel_path}")
                                    
                                    # 直接上传已存在的文件到OSS，避免文件复制
                                    oss_object_key = f"ppt-reports/{task_folder}/{excel_filename}"
                                    result = self.oss_service.bucket.put_object_from_file(oss_object_key, str(target_excel_path))
                                    
                                    # 生成OSS访问URL
                                    oss_file_url = self.oss_service._generate_oss_url(oss_object_key)
                                    
                                    if oss_file_url:
                                        data_download_url = oss_file_url
                                        logger.info(f"Excel文件OSS上传成功: {oss_file_url}")
                                    else:
                                        logger.warning("OSS上传成功但未获取到文件URL，使用本地URL")
                                else:
                                    logger.info("OSS服务未启用或未配置，Excel文件仅保存在本地")
                            except Exception as oss_error:
                                logger.error(f"Excel OSS上传异常: {oss_error}", exc_info=True)
                        else:
                            # 备用方案：没有任务文件夹时的处理
                            logger.warning("未找到任务文件夹，尝试上传Excel到默认目录")
                            try:
                                # 直接导入oss_service作为备用方案
                                from services.oss_service import oss_service as backup_oss_service
                                
                                excel_upload_result = backup_oss_service.upload_file(
                                    file_path=excel_path,
                                    object_name=excel_filename,
                                    folder='ppt-reports-data'
                                )
                                
                                if excel_upload_result.get("success"):
                                    oss_file_url = excel_upload_result.get("file_url")
                                    if oss_file_url:
                                        data_download_url = oss_file_url
                                        logger.info(f"Excel文件备用OSS上传成功: {oss_file_url}")
                                    else:
                                        logger.warning("备用OSS上传成功但未获取到文件URL")
                                else:
                                    error_msg = excel_upload_result.get("error", "未知错误")
                                    logger.warning(f"Excel文件备用OSS上传失败: {error_msg}")
                                    data_download_url = None
                            except Exception as upload_error:
                                logger.error(f"Excel备用OSS上传异常: {upload_error}", exc_info=True)
                                data_download_url = None
                    else:
                        logger.warning("Excel文件生成失败或文件不存在")
                
                except Exception as excel_error:
                    logger.error(f"Excel生成失败（不影响PPT）: {excel_error}")
                    # Excel生成失败不影响PPT结果
                
                return {
                    "success": True,
                    "message": "会员数据报告生成成功",
                    "data": {
                        "file_path": generation_result["file_path"],
                        "file_name": generation_result["file_name"],
                        "file_size": generation_result["file_size"],
                        "file_url": generation_result.get("file_url"),  # 添加OSS文件URL
                        "download_url": generation_result.get("download_url"),  # 添加下载URL
                        "dataDownloadUrl": data_download_url,  # 添加Excel下载URL
                        "object_name": generation_result.get("object_name"),  # 添加对象名
                        "task_folder": generation_result.get("task_folder"),  # 添加任务文件夹
                        "upload_time": generation_result.get("upload_time"),  # 添加上传时间
                        "storage_type": generation_result.get("storage_type"),  # 添加存储类型
                        "query_params": ppt_data["query_params"],
                        "validation_result": validation_result,
                        "missing_params": generation_result.get("missing_params", []),
                        "extra_params": generation_result.get("extra_params", [])
                    }
                }
            else:
                error_msg = f"PPT生成失败: {generation_result.get('error', '未知错误')}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "message": error_msg,
                    "data": None
                }

        except Exception as e:
            error_msg = f"生成会员数据报告失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "message": error_msg,
                "data": None
            }

    async def _convert_to_ppt_params(self, ppt_data: Dict[str, Any], original_query_params=None) -> Dict[str, Any]:
        """
        将获取的数据转换为PPT参数

        Args:
            ppt_data: 从数据获取服务获得的数据

        Returns:
            Dict: PPT参数字典
        """
        try:
            # 获取时间范围
            time_frame = ppt_data.get("query_params", {}).get("time_frame", "2024年1月-12月")

            # 生成图片（在数据整合之前）
            logger.info("生成PPT所需图片...")
            if original_query_params:
                picture_paths = await pic_acquisition_service.get_picture_paths(original_query_params)
            else:
                # 如果没有原始参数，使用字典格式的参数
                query_params_dict = ppt_data.get("query_params", {})
                picture_paths = await pic_acquisition_service.get_picture_paths(query_params_dict)
            logger.info(f"图片生成完成，共 {len(picture_paths)} 张图片")

            # 使用constants类的方法整合数据（异步调用）
            ppt_params = await self.constants.build_complete_ppt_data(
                time_frame=time_frame,
                member_base_data=ppt_data.get("member_base"),
                member_consume_data=ppt_data.get("member_consume"),
                member_charge_data=ppt_data.get("member_charge"),
                coupon_data=ppt_data.get("coupon"),
                pinzhi_data=ppt_data.get("pinzhi"),
                industry_data=ppt_data.get("industry")
            )

            # 将图片路径添加到PPT参数中
            ppt_params.update(picture_paths)

            logger.info(f"转换后的PPT参数数量: {len(ppt_params)}")
            logger.debug(f"PPT参数详情: {ppt_params}")

            return ppt_params

        except Exception as e:
            logger.error(f"转换PPT参数失败: {str(e)}")
            # 返回最小化的基础数据，不使用虚拟数据
            import datetime
            return {
                "time_frame": "数据查询期间",
                "report_date": datetime.datetime.now().strftime("%Y年%m月%d日"),
                "member_data_analysis_report": "数据分析暂时不可用",
                "member_revenue_analysis_report": "收入分析暂时不可用"
            }

    async def validate_ppt_template(self) -> Dict[str, Any]:
        """
        验证PPT模板

        Returns:
            Dict: 验证结果
        """
        try:
            template_info = self.ppt_service.get_template_info()

            if not template_info["exists"]:
                return {
                    "valid": False,
                    "message": "PPT模板文件不存在",
                    "template_info": template_info
                }

            # 使用默认数据验证模板参数
            default_data = self.constants.get_default_data()
            validation_result = self.ppt_service.validate_template_params(default_data)

            return {
                "valid": validation_result["valid"],
                "message": "PPT模板验证完成",
                "template_info": template_info,
                "validation_result": validation_result
            }

        except Exception as e:
            logger.error(f"验证PPT模板失败: {str(e)}")
            return {
                "valid": False,
                "message": f"验证PPT模板失败: {str(e)}",
                "template_info": None
            }

    async def get_data_preview(self, query_params: QueryParams) -> Dict[str, Any]:
        """
        获取数据预览（不生成PPT，仅返回数据）

        Args:
            query_params: 查询参数

        Returns:
            Dict: 数据预览结果
        """
        try:
            logger.info(f"获取数据预览 - 查询参数: {query_params}")

            # 获取所有数据
            ppt_data = await self.data_service.get_all_ppt_data(query_params)

            # 转换为PPT参数
            ppt_params = await self._convert_to_ppt_params(ppt_data, query_params)

            # 验证参数
            validation_result = self.constants.validate_required_params(ppt_params)

            return {
                "success": True,
                "message": "数据预览获取成功",
                "data": {
                    "raw_data": ppt_data,
                    "ppt_params": ppt_params,
                    "validation_result": validation_result,
                    "param_count": len(ppt_params)
                }
            }

        except Exception as e:
            error_msg = f"获取数据预览失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "message": error_msg,
                "data": None
            }

    async def generate_with_custom_data(
        self,
        custom_data: Dict[str, Any],
        output_filename: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        使用自定义数据生成PPT

        Args:
            custom_data: 自定义PPT参数数据
            output_filename: 输出文件名（可选）

        Returns:
            Dict: 生成结果
        """
        try:
            logger.info("使用自定义数据生成PPT")

            # 验证参数完整性
            validation_result = self.constants.validate_required_params(custom_data)

            if not validation_result["valid"]:
                logger.warning(f"自定义数据验证失败，缺少参数: {validation_result['missing_params']}")
                # 使用默认数据补充缺失参数
                default_data = self.constants.get_default_data()
                for missing_param in validation_result["missing_params"]:
                    if missing_param in default_data:
                        custom_data[missing_param] = default_data[missing_param]
                        logger.info(f"使用默认值补充参数: {missing_param}")

            # 生成PPT
            generation_result = self.ppt_service.generate_ppt_report(custom_data, output_filename)

            if generation_result["success"]:
                logger.info(f"自定义数据PPT生成成功: {generation_result['file_path']}")
                return {
                    "success": True,
                    "message": "自定义数据PPT生成成功",
                    "data": {
                        "file_path": generation_result["file_path"],
                        "file_name": generation_result["file_name"],
                        "file_size": generation_result["file_size"],
                        "validation_result": validation_result,
                        "missing_params": generation_result.get("missing_params", []),
                        "extra_params": generation_result.get("extra_params", [])
                    }
                }
            else:
                error_msg = f"自定义数据PPT生成失败: {generation_result.get('error', '未知错误')}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "message": error_msg,
                    "data": None
                }

        except Exception as e:
            error_msg = f"使用自定义数据生成PPT失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "message": error_msg,
                "data": None
            }

    def _generate_custom_filename(self, query_params: QueryParams) -> str:
        """
        根据查询参数生成自定义文件名

        Args:
            query_params: 查询参数

        Returns:
            str: 自定义文件名
        """
        import datetime
        import re

        # 生成时间戳
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

        # 基础文件名
        base_name = "会员数据报告"

        # 添加品牌ID标识
        brand_part = ""
        if query_params.bid:
            # 清理bid中的特殊字符，确保文件名安全
            clean_bid = re.sub(r'[<>:"/\\|?*]', '', str(query_params.bid))
            brand_part = f"_品牌{clean_bid}"

        # 添加门店标识（如果有）
        store_part = ""
        if query_params.sid:
            clean_sid = re.sub(r'[<>:"/\\|?*]', '', str(query_params.sid))
            store_part = f"_门店{clean_sid}"

        # 组合完整文件名
        filename = f"{base_name}{brand_part}{store_part}_{timestamp}.pptx"

        logger.info(f"生成文件名: {filename}")
        return filename


# 创建全局服务实例
ppt_report_service = PPTReportService()