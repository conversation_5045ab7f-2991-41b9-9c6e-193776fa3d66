import request from './request'

export const bidAPI = {
  // 获取BID配置列表
  getBidConfigs(params) {
    return request({
      url: '/admin/bid-configs',
      method: 'get',
      params
    })
  },

  // 获取单个BID配置
  getBidConfig(bidId) {
    return request({
      url: `/admin/bid-configs/${bidId}`,
      method: 'get'
    })
  },

  // 创建BID配置
  createBidConfig(data) {
    return request({
      url: '/admin/bid-configs',
      method: 'post',
      data
    })
  },

  // 更新BID配置
  updateBidConfig(bidId, data) {
    return request({
      url: `/admin/bid-configs/${bidId}`,
      method: 'put',
      data
    })
  },

  // 删除BID配置
  deleteBidConfig(bidId) {
    return request({
      url: `/admin/bid-configs/${bidId}`,
      method: 'delete'
    })
  },

  // 更新BID状态
  updateBidStatus(bidId, status) {
    return request({
      url: `/admin/bid-configs/${bidId}/status`,
      method: 'patch',
      data: { status }
    })
  },

  // 获取BID使用统计
  getBidStatistics(bidId) {
    return request({
      url: `/admin/bid-configs/${bidId}/statistics`,
      method: 'get'
    })
  }
}