<template>
  <div class="not-found-container">
    <div class="not-found-content">
      <!-- 404图标 -->
      <div class="error-icon">
        <el-icon class="icon-404"><Warning /></el-icon>
      </div>
      
      <!-- 错误信息 -->
      <div class="error-info">
        <h1 class="error-title">404</h1>
        <h2 class="error-subtitle">页面未找到</h2>
        <p class="error-description">
          抱歉，您访问的页面不存在或已被移除。
        </p>
      </div>
      
      <!-- 操作按钮 -->
      <div class="error-actions">
        <el-button 
          type="primary" 
          size="large"
          @click="goHome"
          :icon="House"
        >
          返回首页
        </el-button>
        <el-button 
          size="large"
          @click="goBack"
          :icon="ArrowLeft"
        >
          返回上页
        </el-button>
      </div>
      
      <!-- 建议链接 -->
      <div class="suggestions">
        <h3 class="suggestions-title">您可能想要访问：</h3>
        <div class="suggestion-links">
          <router-link to="/inquiry/week" class="suggestion-link">
            <el-icon><Calendar /></el-icon>
            <span>周报查询</span>
          </router-link>
          <router-link to="/inquiry/month" class="suggestion-link disabled">
            <el-icon><Calendar /></el-icon>
            <span>月报查询</span>
          </router-link>
          <router-link to="/inquiry/quarter" class="suggestion-link disabled">
            <el-icon><Calendar /></el-icon>
            <span>季报查询</span>
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { Warning, House, ArrowLeft, Calendar } from '@element-plus/icons-vue'

const router = useRouter()

// 返回首页
const goHome = () => {
  router.push('/')
}

// 返回上一页
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}
</script>

<style scoped>
.not-found-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.not-found-content {
  text-align: center;
  max-width: 600px;
  width: 100%;
}

/* 错误图标 */
.error-icon {
  margin-bottom: 30px;
}

.icon-404 {
  font-size: 120px;
  color: #f56c6c;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* 错误信息 */
.error-info {
  margin-bottom: 40px;
}

.error-title {
  font-size: 72px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.error-subtitle {
  font-size: 28px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 15px;
}

.error-description {
  font-size: 16px;
  color: #909399;
  line-height: 1.6;
  margin: 0;
}

/* 操作按钮 */
.error-actions {
  margin-bottom: 50px;
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

/* 建议链接 */
.suggestions {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.suggestions-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
}

.suggestion-links {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.suggestion-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px;
  border-radius: 8px;
  background: #f8f9fa;
  color: #606266;
  text-decoration: none;
  transition: all 0.3s ease;
  min-width: 120px;
}

.suggestion-link:hover:not(.disabled) {
  background: #409eff;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.suggestion-link.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.suggestion-link .el-icon {
  font-size: 24px;
}

.suggestion-link span {
  font-size: 14px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .not-found-container {
    padding: 20px 10px;
  }
  
  .error-title {
    font-size: 48px;
  }
  
  .error-subtitle {
    font-size: 20px;
  }
  
  .icon-404 {
    font-size: 80px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .suggestion-links {
    flex-direction: column;
    align-items: center;
  }
  
  .suggestion-link {
    width: 100%;
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .suggestions {
    padding: 20px;
  }
  
  .error-title {
    font-size: 36px;
  }
  
  .error-subtitle {
    font-size: 18px;
  }
}
</style>
