from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import logging
import time
from contextlib import asynccontextmanager

from core.config import settings
from core.database import db
from core.exception_handlers import setup_exception_handlers
from utils.setup_logging import setup_logging
from api.query.router import router as query_router
from api.PPTreport.router import router as ppt_report_router, download_router as ppt_download_router
from api.PPTreport.router_custom import router as custom_report_router
from api.setting.router import router as setting_router
from api.PPTreport.task_management.database_init import init_task_database
from api.PPTreport.task_management.task_queue import init_task_queue, shutdown_task_queue
from api.PPTreport.task_management.task_scheduler import init_task_scheduler, shutdown_task_scheduler
from auth.database.init_auth_database import init_auth_database
from auth.routers.auth import router as auth_router
from auth.routers.admin import router as admin_router
from auth.middleware.audit_middleware import AuditMiddleware


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时
    setup_logging()
    logger = logging.getLogger(__name__)
    logger.info("应用启动中...")
    
    # 初始化数据库连接
    await db.connect()
    logger.info("数据库连接已建立")
    
    # 初始化认证数据库表
    try:
        await init_auth_database(db)
        logger.info("认证数据库初始化成功")
    except Exception as e:
        logger.error(f"认证数据库初始化失败: {e}")
        # 不阻止应用启动，仅记录错误
    
    # 初始化任务管理数据库表
    try:
        await init_task_database()
        logger.info("任务管理数据库初始化成功")
    except Exception as e:
        logger.error(f"任务管理数据库初始化失败: {e}")
        # 不阻止应用启动，仅记录错误
    
    # 启动任务队列
    try:
        await init_task_queue()
        logger.info("任务队列已启动")
    except Exception as e:
        logger.error(f"任务队列启动失败: {e}")
        # 不阻止应用启动，仅记录错误
    
    # 启动任务调度器
    try:
        await init_task_scheduler()
        logger.info("任务调度器已启动")
    except Exception as e:
        logger.error(f"任务调度器启动失败: {e}")
        # 不阻止应用启动，仅记录错误
    
    yield
    
    # 关闭时
    # 停止任务调度器
    try:
        await shutdown_task_scheduler()
        logger.info("任务调度器已停止")
    except Exception as e:
        logger.error(f"任务调度器停止失败: {e}")
    
    # 停止任务队列
    try:
        await shutdown_task_queue()
        logger.info("任务队列已停止")
    except Exception as e:
        logger.error(f"任务队列停止失败: {e}")
    
    await db.disconnect()
    logger.info("应用已关闭")


# 创建FastAPI应用实例
app = FastAPI(
    title="会员报表API",
    description="会员数据分析报表系统API",
    version="1.0.0",
    lifespan=lifespan
)

# 设置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.get_cors_origins() if settings.ENVIRONMENT != "development" else ["*"],  # 开发环境允许所有，生产环境使用配置
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加审计日志中间件（需要在CORS之后添加）
app.add_middleware(AuditMiddleware)

# 注册异常处理器
setup_exception_handlers(app)

# 注册路由
app.include_router(auth_router, prefix="/api", tags=["认证"])  # 认证路由优先
app.include_router(admin_router, prefix="/api", tags=["管理员"])  # 管理员路由
app.include_router(query_router, prefix="/api/query", tags=["查询"])
app.include_router(ppt_report_router, prefix="/api/ppt-report", tags=["PPT报告"])
app.include_router(custom_report_router, prefix="/api/ppt-report", tags=["自定义报告"])
app.include_router(ppt_download_router, prefix="/api/ppt-report", tags=["PPT下载"])  # 下载路由（支持token参数）
app.include_router(setting_router, prefix="/api/setting", tags=["用户设置"])

# 文件下载路由已经包含在ppt_download_router中，路径为 /api/ppt-report/download/{folder}/{filename}
# 支持通过URL参数传递token

@app.middleware("http")
async def log_requests(request: Request, call_next):
    """请求日志中间件"""
    start_time = time.time()
    logger = logging.getLogger("request")
    
    # 记录请求信息
    logger.info(f"收到请求: {request.method} {request.url}")
    logger.info(f"请求头: {dict(request.headers)}")
    
    # 处理请求
    response = await call_next(request)
    
    # 记录响应信息
    process_time = time.time() - start_time
    logger.info(f"请求处理完成: {request.method} {request.url} - 状态码: {response.status_code} - 耗时: {process_time:.3f}秒")
    
    return response

@app.get("/")
async def root():
    """根路径健康检查"""
    return {"message": "会员报表API服务运行正常", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "environment": settings.ENVIRONMENT}

@app.get("/test")
async def test_endpoint():
    """测试端点"""
    return {"message": "测试端点正常", "timestamp": time.time()}

@app.post("/test-post")
async def test_post_endpoint(request: Request):
    """测试POST端点"""
    try:
        body = await request.json()
        return {"message": "POST测试成功", "received_data": body}
    except Exception as e:
        return {"message": "POST测试失败", "error": str(e)}


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.API_HOST,
        port=settings.API_PORT,
        reload=settings.API_RELOAD,
        log_level=settings.LOG_LEVEL.lower()
    )
