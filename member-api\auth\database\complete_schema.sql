-- 会员报表系统数据库结构
-- 数据库：ai_yuce

-- 用户表
CREATE TABLE IF NOT EXISTS `member_report_users` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    `username` VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    `email` VARCHAR(100) UNIQUE COMMENT '邮箱',
    `password` VARCHAR(255) NOT NULL COMMENT '密码（加密）',
    `real_name` VARCHAR(100) COMMENT '真实姓名',
    `phone` VARCHAR(20) COMMENT '手机号',
    `department` VARCHAR(100) COMMENT '部门',
    `role` ENUM('super_admin', 'admin', 'user') NOT NULL DEFAULT 'user' COMMENT '角色：super_admin-超级管理员，admin-管理员，user-普通用户',
    `account_type` ENUM('regular_agent', 'direct_agent', 'aoqiwei_operation', 'merchant_super_admin', 'merchant_user') DEFAULT 'merchant_user' COMMENT '账户类别：regular_agent-常规代理商，direct_agent-直营代理商，aoqiwei_operation-奥琦玮直管运营，merchant_super_admin-商户超级管理员，merchant_user-商户普通号',
    `expire_time` DATETIME DEFAULT NULL COMMENT '账户到期时间',
    `status` ENUM('active', 'inactive', 'banned') NOT NULL DEFAULT 'active' COMMENT '状态：active-激活，inactive-禁用，banned-封禁',
    `quotas` INT DEFAULT 0 COMMENT 'PPT生成配额限制（0表示无限制）',
    `use_quotas` INT DEFAULT 0 COMMENT 'PPT已使用次数',
    `avatar_url` VARCHAR(500) COMMENT '头像URL',
    `last_login_time` DATETIME COMMENT '最后登录时间',
    `login_count` INT DEFAULT 0 COMMENT '登录次数',
    `failed_login_count` INT DEFAULT 0 COMMENT '失败登录次数',
    `last_failed_login_time` DATETIME COMMENT '最后失败登录时间',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` DATETIME DEFAULT NULL COMMENT '软删除时间',
    INDEX `idx_username` (`username`),
    INDEX `idx_email` (`email`),
    INDEX `idx_status` (`status`),
    INDEX `idx_role` (`role`),
    INDEX `idx_account_type` (`account_type`),
    INDEX `idx_expire_time` (`expire_time`),
    INDEX `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 会话表
CREATE TABLE IF NOT EXISTS `member_report_sessions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '会话ID',
    `user_id` INT NOT NULL COMMENT '用户ID',
    `token` VARCHAR(500) NOT NULL UNIQUE COMMENT '访问令牌',
    `refresh_token` VARCHAR(500) UNIQUE COMMENT '刷新令牌',
    `refresh_expires_at` DATETIME COMMENT '刷新令牌过期时间',
    `ip_address` VARCHAR(45) COMMENT 'IP地址',
    `user_agent` TEXT COMMENT '用户代理信息',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    `expires_at` DATETIME NOT NULL COMMENT '访问令牌过期时间',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (`user_id`) REFERENCES `member_report_users` (`id`) ON DELETE CASCADE,
    INDEX `idx_token` (`token`(100)),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会话表';

-- 审计日志表
CREATE TABLE IF NOT EXISTS `member_report_audit_logs` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    `user_id` INT COMMENT '用户ID',
    `username` VARCHAR(50) COMMENT '用户名',
    `action` VARCHAR(100) NOT NULL COMMENT '操作类型',
    `resource` VARCHAR(200) COMMENT '资源类型',
    `resource_id` VARCHAR(100) COMMENT '资源ID',
    `ip_address` VARCHAR(45) COMMENT 'IP地址',
    `user_agent` TEXT COMMENT '用户代理信息',
    `request_data` JSON COMMENT '请求数据',
    `response_data` JSON COMMENT '响应数据',
    `status` ENUM('success', 'failure') COMMENT '操作状态：success-成功，failure-失败',
    `error_message` TEXT COMMENT '错误信息',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_action` (`action`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审计日志表';

-- BID配置表（支持申请审批流程）
CREATE TABLE IF NOT EXISTS `member_report_bid_configs` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT 'BID配置ID',
    `bid` VARCHAR(50) NOT NULL COMMENT '业务ID',
    `bid_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'BID名称',
    `description` TEXT COMMENT '描述',
    `business_type` ENUM('fast_food', 'casual_dining', 'fine_dining') NOT NULL COMMENT '业态标签：fast_food-快餐，casual_dining-休闲餐，fine_dining-正餐',
    `owner_username` VARCHAR(50) COMMENT '所有者用户名',
    `status` ENUM('pending', 'approved', 'rejected', 'active', 'inactive') NOT NULL DEFAULT 'pending' COMMENT '状态：pending-待审批，approved-已批准，rejected-已拒绝，active-已激活，inactive-已停用',
    `apply_reason` TEXT COMMENT '申请理由',
    `reject_reason` TEXT COMMENT '拒绝理由',
    `approved_by` VARCHAR(50) COMMENT '审批人用户名',
    `approved_at` DATETIME COMMENT '审批时间',
    `sid_list` JSON COMMENT 'SID列表（JSON格式）',
    `contact_name` VARCHAR(100) COMMENT '联系人姓名',
    `contact_phone` VARCHAR(20) COMMENT '联系人电话',
    `contact_email` VARCHAR(100) COMMENT '联系人邮箱',
    `company_name` VARCHAR(200) COMMENT '公司名称',
    `industry` VARCHAR(100) COMMENT '行业',
    `remark` TEXT COMMENT '备注',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` DATETIME DEFAULT NULL COMMENT '软删除时间',
    FOREIGN KEY (`owner_username`) REFERENCES `member_report_users` (`username`) ON DELETE SET NULL ON UPDATE CASCADE,
    UNIQUE KEY `unique_bid_owner` (`bid`, `owner_username`),
    INDEX `idx_bid` (`bid`),
    INDEX `idx_status` (`status`),
    INDEX `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='BID配置表';

-- 用户BID权限关联表
CREATE TABLE IF NOT EXISTS `member_report_user_bid_permissions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '权限ID',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `bid` VARCHAR(50) NOT NULL COMMENT '业务ID',
    `permissions` JSON COMMENT '权限列表（JSON格式，如["read", "write", "delete"]）',
    `sid_filter` JSON COMMENT 'SID过滤列表（JSON格式，限制用户可访问的SID）',
    `granted_by` VARCHAR(50) COMMENT '授权人用户名',
    `grant_reason` TEXT COMMENT '授权原因',
    `expire_at` DATETIME COMMENT '权限过期时间',
    `status` ENUM('active', 'inactive') NOT NULL DEFAULT 'active' COMMENT '状态：active-激活，inactive-禁用',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (`username`) REFERENCES `member_report_users` (`username`) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (`bid`) REFERENCES `member_report_bid_configs` (`bid`) ON DELETE CASCADE ON UPDATE CASCADE,
    UNIQUE KEY `unique_user_bid` (`username`, `bid`),
    INDEX `idx_username` (`username`),
    INDEX `idx_bid` (`bid`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户BID权限关联表';


