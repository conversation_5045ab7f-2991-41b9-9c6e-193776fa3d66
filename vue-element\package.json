{"name": "member-report-frontend", "version": "1.0.0", "description": "会员报表系统前端", "type": "module", "scripts": {"dev": "node ./node_modules/vite/bin/vite.js --host", "dev:alt": "vite --host", "dev:fixed": "node ./node_modules/vite/bin/vite.js --host --port 5174", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "pinia": "^2.1.7", "dayjs": "^1.11.10", "echarts": "^5.4.3", "vue-echarts": "^6.6.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^5.0.8", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "@vue/eslint-config-prettier": "^9.0.0", "prettier": "^3.1.1", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0"}, "keywords": ["vue3", "element-plus", "member-report", "data-analysis"], "author": "Member Report Team", "license": "MIT"}