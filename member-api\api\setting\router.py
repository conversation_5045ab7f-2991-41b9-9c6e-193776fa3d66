# -*- coding: utf-8 -*-
"""
用户设置相关路由
包括BID申请管理
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Optional

from core.database import db
from auth.middleware.auth_middleware import get_current_user
from .bid_application import BidApplicationService

router = APIRouter()


@router.post("/bid-application/apply")
async def submit_bid_application(
    application_data: dict,
    current_user: dict = Depends(get_current_user)
):
    """提交BID申请"""
    service = BidApplicationService(db.task_management_pool)
    result = await service.submit_application(application_data, current_user)
    return {"code": 200, "data": result, "message": result.get('message')}


@router.get("/bid-application/list")
async def get_bid_applications(
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    current_user: dict = Depends(get_current_user)
):
    """获取我的BID申请列表"""
    service = BidApplicationService(db.task_management_pool)
    result = await service.get_user_applications(current_user, page, page_size)
    return {"code": 200, "data": result}


@router.delete("/bid-application/{bid}/cancel")
async def cancel_bid_application(
    bid: str,
    current_user: dict = Depends(get_current_user)
):
    """撤销BID申请"""
    service = BidApplicationService(db.task_management_pool)
    result = await service.cancel_application(bid, current_user)
    return {"code": 200, "data": result, "message": result.get('message')}


@router.put("/bid-application/{bid}/update")
async def update_bid_application(
    bid: str,
    update_data: dict,
    current_user: dict = Depends(get_current_user)
):
    """修改BID申请"""
    service = BidApplicationService(db.task_management_pool)
    result = await service.update_application(bid, update_data, current_user)
    return {"code": 200, "data": result, "message": result.get('message')}