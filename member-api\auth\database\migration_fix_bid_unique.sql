-- 修复BID重复提交问题的迁移脚本
-- 移除bid字段的唯一约束，添加bid和owner_username的复合唯一键

-- 1. 首先删除原有的唯一约束
ALTER TABLE `member_report_bid_configs` 
DROP INDEX `bid`;

-- 2. 添加复合唯一键，允许不同用户申请相同的BID
ALTER TABLE `member_report_bid_configs` 
ADD UNIQUE KEY `unique_bid_owner` (`bid`, `owner_username`);

-- 3. 保留bid的索引以提高查询性能
ALTER TABLE `member_report_bid_configs` 
ADD INDEX `idx_bid` (`bid`);

-- 注意：执行此脚本前，请确保数据库中没有重复的 (bid, owner_username) 组合
-- 如果有重复数据，需要先处理重复数据