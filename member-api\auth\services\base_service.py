"""
基础服务类
提供事务管理和错误处理
"""
import logging
import functools
from typing import Any, Callable
from contextlib import asynccontextmanager
import aiomysql

logger = logging.getLogger(__name__)


class BaseService:
    """基础服务类，提供数据库事务管理"""
    
    def __init__(self, db_pool):
        """
        初始化基础服务
        
        Args:
            db_pool: 数据库连接池
        """
        self.db_pool = db_pool
    
    @asynccontextmanager
    async def transaction(self):
        """
        数据库事务上下文管理器
        
        Usage:
            async with self.transaction() as (conn, cursor):
                await cursor.execute(sql, params)
                # 如果没有异常，会自动提交
                # 如果有异常，会自动回滚
        """
        conn = await self.db_pool.acquire()
        cursor = None
        
        try:
            cursor = await conn.cursor(aiomysql.DictCursor)
            yield conn, cursor
            await conn.commit()
            
        except Exception as e:
            await conn.rollback()
            logger.error(f"事务执行失败，已回滚: {e}")
            raise
            
        finally:
            if cursor:
                await cursor.close()
            self.db_pool.release(conn)
    
    @asynccontextmanager
    async def read_connection(self):
        """
        只读数据库连接（用于查询）
        
        Usage:
            async with self.read_connection() as cursor:
                await cursor.execute(sql, params)
                result = await cursor.fetchone()
        """
        conn = await self.db_pool.acquire()
        cursor = None
        
        try:
            cursor = await conn.cursor(aiomysql.DictCursor)
            yield cursor
            
        finally:
            if cursor:
                await cursor.close()
            self.db_pool.release(conn)
    
    def transactional(self, func: Callable) -> Callable:
        """
        事务装饰器
        
        Usage:
            @self.transactional
            async def create_user(self, data):
                # 方法内的所有数据库操作都会在一个事务中
        """
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            async with self.transaction() as (conn, cursor):
                # 将连接和游标注入到方法中
                kwargs['_conn'] = conn
                kwargs['_cursor'] = cursor
                return await func(*args, **kwargs)
        return wrapper
    
    async def execute_query(self, sql: str, params: tuple = None) -> Any:
        """
        执行查询语句
        
        Args:
            sql: SQL查询语句
            params: 查询参数
            
        Returns:
            查询结果
        """
        async with self.read_connection() as cursor:
            await cursor.execute(sql, params or ())
            return await cursor.fetchall()
    
    async def execute_one(self, sql: str, params: tuple = None) -> Any:
        """
        执行查询语句并返回单条结果
        
        Args:
            sql: SQL查询语句
            params: 查询参数
            
        Returns:
            单条查询结果
        """
        async with self.read_connection() as cursor:
            await cursor.execute(sql, params or ())
            return await cursor.fetchone()
    
    async def execute_update(self, sql: str, params: tuple = None) -> int:
        """
        执行更新语句
        
        Args:
            sql: SQL更新语句
            params: 更新参数
            
        Returns:
            影响的行数
        """
        async with self.transaction() as (conn, cursor):
            await cursor.execute(sql, params or ())
            return cursor.rowcount
    
    async def execute_insert(self, sql: str, params: tuple = None) -> int:
        """
        执行插入语句
        
        Args:
            sql: SQL插入语句
            params: 插入参数
            
        Returns:
            插入的记录ID
        """
        async with self.transaction() as (conn, cursor):
            await cursor.execute(sql, params or ())
            return cursor.lastrowid
    
    async def execute_delete(self, sql: str, params: tuple = None) -> int:
        """
        执行删除语句（软删除）
        
        Args:
            sql: SQL删除语句
            params: 删除参数
            
        Returns:
            影响的行数
        """
        async with self.transaction() as (conn, cursor):
            await cursor.execute(sql, params or ())
            return cursor.rowcount
    
    async def batch_insert(self, sql: str, params_list: list) -> int:
        """
        批量插入
        
        Args:
            sql: SQL插入语句
            params_list: 参数列表
            
        Returns:
            插入的记录数
        """
        if not params_list:
            return 0
            
        async with self.transaction() as (conn, cursor):
            await cursor.executemany(sql, params_list)
            return cursor.rowcount
    
    async def check_exists(self, table: str, condition: str, params: tuple = None) -> bool:
        """
        检查记录是否存在
        
        Args:
            table: 表名
            condition: WHERE条件
            params: 参数
            
        Returns:
            是否存在
        """
        sql = f"SELECT COUNT(*) as count FROM {table} WHERE {condition}"
        result = await self.execute_one(sql, params)
        return result['count'] > 0 if result else False