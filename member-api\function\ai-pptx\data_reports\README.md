# 会员数据报告生成器

这是一个专门用于生成会员数据报告的简化工具，可以直接替换PPT模板中的数据参数，无需依赖大语言模型。

## 📁 文件结构

```
data_reports/
├── __init__.py                    # 模块初始化文件
├── config.py                      # 会员数据配置文件 ⭐
├── simple_ppt_generator.py        # 简化的PPT生成器
├── generate_member_report.py      # 主执行脚本 ⭐
└── README.md                      # 使用说明
```

## 🚀 快速开始

### 1. 设置图片资源（首次使用）

```bash
# 创建示例图片（需要安装 Pillow: pip install Pillow）
python data_reports/setup_images.py
```

### 2. 修改数据配置

编辑 `config.py` 文件，修改以下数据：

```python
# 时间范围
TIME_FRAME = "2024年1月-12月"

# 会员数据
TOTAL_MEMBERS = 15680  # 会员总量
NEW_MEMBERS = 2340     # 新增会员

# 图片路径（可选，如需自定义图片）
COMPANY_LOGO = "./data_reports/images/your_logo.png"
```

### 3. 生成报告

在项目根目录下运行：

```bash
# 普通模式（使用配置文件中的数据）
python data_reports/generate_member_report.py

# 交互模式（临时修改数据）
python data_reports/generate_member_report.py --interactive
```

### 3. 查看结果

生成的报告将保存在 `ppt_template/` 目录下，文件名格式为：
`会员数据报告_YYYYMMDD_HHMMSS.pptx`

## 📊 支持的参数

模板中可以使用以下参数（用花括号包围）：

### 文本参数
- `{time_frame}` - 时间范围
- `{total_members}` - 会员总量
- `{new_members}` - 新增会员
- `{historical_members}` - 历史会员（自动计算）
- `{report_date}` - 报告生成日期

### 图片参数
- `{image_logo}` - 公司Logo
- `{image_chart}` - 数据图表
- `{image_trend}` - 会员趋势图

**图片参数使用方法：**
1. 在PPT模板中创建文本框
2. 在文本框中输入图片参数（如 `{image_logo}`）
3. 文本框的位置和大小将决定图片的插入位置和尺寸
4. 运行脚本后，文本框将被替换为对应的图片

## 🔧 配置说明

### 主要配置项

```python
# 模板文件路径
TEMPLATE_PATH = "./ppt_template/会员数据报告 -模板.pptx"

# 输出目录
OUTPUT_DIR = "./ppt_template/"

# 输出文件名前缀
OUTPUT_PREFIX = "会员数据报告"
```

### 数据配置

```python
# 会员数据
TOTAL_MEMBERS = 15680  # 会员总量
NEW_MEMBERS = 2340     # 新增会员
HISTORICAL_MEMBERS = TOTAL_MEMBERS - NEW_MEMBERS  # 历史会员（自动计算）
```

## 📝 使用示例

### 示例1：修改配置文件后生成

1. 编辑 `config.py`：
   ```python
   TOTAL_MEMBERS = 20000
   NEW_MEMBERS = 3000
   TIME_FRAME = "2024年第一季度"
   ```

2. 运行生成脚本：
   ```bash
   python data_reports/generate_member_report.py
   ```

### 示例2：交互模式临时修改

```bash
python data_reports/generate_member_report.py --interactive
```

然后按提示输入数据：
```
时间范围 (当前: 2024年1月-12月): 2024年第二季度
会员总量 (当前: 15680): 18000
新增会员 (当前: 2340): 2800
```

## 🛠️ 故障排除

### 常见问题

1. **模板文件不存在**
   - 检查 `TEMPLATE_PATH` 配置是否正确
   - 确保模板文件存在于指定路径

2. **参数未被替换**
   - 检查模板中的参数名是否与配置文件中的键名一致
   - 确保参数使用花括号格式：`{参数名}`

3. **生成的文件无法打开**
   - 检查模板文件是否损坏
   - 确保有足够的磁盘空间

### 日志文件

生成过程中的详细日志会保存在 `member_report_generation.log` 文件中。

## 🔍 参数验证

脚本会自动验证模板参数和配置数据的匹配情况：

- ✅ 显示模板中找到的所有参数
- ⚠️ 提示缺少的参数
- ℹ️ 显示额外的参数

## 📈 扩展功能

如需添加新的参数：

1. 在模板PPT中添加 `{新参数名}`
2. 在 `config.py` 的 `MEMBER_DATA` 字典中添加对应的键值对
3. 重新运行生成脚本

## 🤝 技术支持

如有问题，请检查：
1. Python环境是否正确安装
2. 依赖包是否完整（python-pptx）
3. 模板文件是否可正常打开
4. 日志文件中的错误信息
