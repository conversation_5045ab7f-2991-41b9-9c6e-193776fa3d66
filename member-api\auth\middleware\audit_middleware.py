"""
审计日志中间件
统一处理API请求的审计日志记录，确保每个用户请求只产生一条审计记录
"""
import json
import logging
import time
import asyncio
import hashlib
from typing import Callable, Optional, Dict, Any, Tuple
from datetime import datetime, timedelta
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

logger = logging.getLogger(__name__)


class AuditMiddleware(BaseHTTPMiddleware):
    """审计日志中间件"""
    
    # 需要记录审计日志的路径前缀
    AUDIT_PATHS = [
        "/api/query/",  # 查询相关接口
        "/api/auth/login",  # 登录
        "/api/auth/logout",  # 登出
        "/api/bid/",  # BID管理
        "/api/user/",  # 用户管理
        "/api/ppt/",  # PPT生成
    ]
    
    # 路径到资源类型的映射
    PATH_TO_RESOURCE = {
        "/week": "week_report",
        "/month": "month_report",
        "/quarter": "quarter_report",
        "/halfyear": "halfyear_report",
        "/all": "all_member_data",
        "/member-base": "member_data_query",  # 统一为会员数据查询
        "/member-consume": "member_data_query",  # 统一为会员数据查询
        "/member-charge": "member_data_query",  # 统一为会员数据查询
        "/coupon-trade": "member_data_query",  # 统一为会员数据查询
        "/pinzhi-cashier": "member_data_query",  # 统一为会员数据查询
        "/sid": "sid_query",
        "/sid/batch": "batch_sid_query",
    }
    
    # 路径到操作类型的映射
    PATH_TO_ACTION = {
        "/login": "login",
        "/logout": "logout",
        "/ppt/generate": "generate_ppt",
        "/ppt/export": "export_ppt",
    }
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.audit_service = None
        # 请求缓存，用于去重
        self.request_cache = {}
        # 缓存过期时间（秒）
        self.cache_ttl = 2.0
        # 启动定期清理任务
        asyncio.create_task(self._cleanup_cache())
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求并记录审计日志"""
        
        # 判断是否需要记录审计日志
        if not self._should_audit(request):
            return await call_next(request)
        
        # 延迟导入，避免循环依赖
        if self.audit_service is None:
            from auth.services.audit_service import AuditService
            from core.database import db
            self.audit_service = AuditService(db.task_management_pool)
        
        # 记录请求开始时间
        start_time = time.time()
        
        # 获取请求信息
        request_data = await self._get_request_data(request)
        
        # 执行请求
        response = await call_next(request)
        
        # 记录请求结束时间
        end_time = time.time()
        duration = int((end_time - start_time) * 1000)  # 毫秒
        
        # 异步记录审计日志（不阻塞响应）
        try:
            # 尝试从响应头中获取用户信息（如果认证成功，用户信息可能被设置）
            current_user = None
            
            # 如果是需要认证的接口，尝试解析token获取用户信息
            if request.headers.get("authorization"):
                try:
                    from auth.utils.jwt_handler import verify_token
                    auth_header = request.headers.get("authorization", "")
                    if auth_header.startswith("Bearer "):
                        token = auth_header.replace("Bearer ", "")
                        current_user = verify_token(token)
                except Exception:
                    pass  # 忽略token解析错误，可能是无效token
            
            # 如果成功获取到用户信息或是登录接口，记录审计日志
            if current_user or request.url.path.endswith("/login"):
                # 创建异步任务记录审计日志，不阻塞响应
                asyncio.create_task(self._log_audit(
                    request=request,
                    response=response,
                    current_user=current_user,
                    request_data=request_data,
                    duration=duration
                ))
        except Exception as e:
            logger.error(f"记录审计日志失败: {e}")
        
        return response
    
    def _generate_request_fingerprint(
        self,
        username: Optional[str],
        request_data: Optional[Dict[str, Any]],
        path: str
    ) -> str:
        """生成请求指纹，用于去重判断"""
        # 提取关键查询参数
        key_params = {}
        if request_data:
            # 只提取影响查询结果的关键参数
            for key in ['bid', 'sid', 'start_date', 'end_date', 'startDate', 'endDate']:
                if key in request_data:
                    key_params[key] = request_data[key]
        
        # 对于Tab查询接口，忽略具体的Tab路径差异
        normalized_path = path
        tab_paths = ['/member-base', '/member-consume', '/member-charge', '/pinzhi-cashier', '/coupon-trade']
        for tab_path in tab_paths:
            if tab_path in path:
                # 将所有Tab查询视为同一类请求
                normalized_path = '/api/query/data/[tab-query]'
                break
        
        # 生成指纹：用户名 + 标准化路径 + 关键参数
        fingerprint_data = {
            'username': username or 'anonymous',
            'path': normalized_path,
            'params': key_params
        }
        
        # 使用JSON序列化确保一致性
        fingerprint_str = json.dumps(fingerprint_data, sort_keys=True)
        # 生成MD5哈希作为指纹
        return hashlib.md5(fingerprint_str.encode()).hexdigest()
    
    def _should_skip_audit(
        self,
        username: Optional[str],
        request_data: Optional[Dict[str, Any]],
        path: str
    ) -> bool:
        """判断是否应该跳过审计记录（去重）"""
        # 生成请求指纹
        fingerprint = self._generate_request_fingerprint(username, request_data, path)
        
        current_time = time.time()
        
        # 检查缓存中是否存在相似请求
        if fingerprint in self.request_cache:
            cached_time = self.request_cache[fingerprint]
            # 如果在时间窗口内，跳过记录
            if current_time - cached_time < self.cache_ttl:
                logger.debug(f"跳过重复请求的审计记录: {path} (指纹: {fingerprint})")
                return True
        
        # 记录到缓存
        self.request_cache[fingerprint] = current_time
        return False
    
    async def _cleanup_cache(self):
        """定期清理过期的缓存项"""
        while True:
            try:
                await asyncio.sleep(10)  # 每10秒清理一次
                current_time = time.time()
                expired_keys = [
                    key for key, cached_time in self.request_cache.items()
                    if current_time - cached_time > self.cache_ttl
                ]
                for key in expired_keys:
                    del self.request_cache[key]
                
                if expired_keys:
                    logger.debug(f"清理了 {len(expired_keys)} 个过期的请求缓存项")
            except Exception as e:
                logger.error(f"清理缓存时出错: {e}")
    
    def _should_audit(self, request: Request) -> bool:
        """判断是否需要记录审计日志"""
        path = request.url.path
        
        # 检查是否匹配需要审计的路径
        for audit_path in self.AUDIT_PATHS:
            if path.startswith(audit_path):
                return True
        
        return False
    
    async def _get_request_data(self, request: Request) -> Optional[Dict[str, Any]]:
        """获取请求数据"""
        try:
            # 对于POST请求，尝试获取请求体
            if request.method in ["POST", "PUT", "PATCH"]:
                # 保存原始body以便后续使用
                body = await request.body()
                
                # 将body重新设置到request中，以便后续处理器可以读取
                async def receive():
                    return {"type": "http.request", "body": body}
                request._receive = receive
                
                # 尝试解析JSON
                if body:
                    try:
                        return json.loads(body)
                    except json.JSONDecodeError:
                        return {"raw_body": body.decode("utf-8", errors="ignore")[:1000]}  # 限制长度
            
            # 对于GET请求，获取查询参数
            elif request.method == "GET":
                params = dict(request.query_params)
                if params:
                    return params
            
        except Exception as e:
            logger.warning(f"获取请求数据失败: {e}")
        
        return None
    
    async def _log_audit(
        self,
        request: Request,
        response: Response,
        current_user: Optional[Dict[str, Any]],
        request_data: Optional[Dict[str, Any]],
        duration: int
    ):
        """记录审计日志"""
        try:
            # 获取路径信息
            path = request.url.path
            method = request.method
            
            # 获取用户名
            username = current_user.get("username") if current_user else None
            
            # 检查是否应该跳过此次审计记录（去重）
            if self._should_skip_audit(username, request_data, path):
                return
            
            # 确定操作类型和资源类型
            action = "query"  # 默认为查询
            resource = "unknown"
            resource_id = None
            
            # 从路径推断操作和资源类型
            for path_pattern, action_type in self.PATH_TO_ACTION.items():
                if path_pattern in path:
                    action = action_type
                    break
            
            for path_pattern, resource_type in self.PATH_TO_RESOURCE.items():
                if path_pattern in path:
                    resource = resource_type
                    break
            
            # 从请求数据中提取资源ID
            if request_data:
                resource_id = request_data.get("bid") or request_data.get("sid")
            
            # 获取响应状态
            status = "success" if response.status_code < 400 else "failure"
            error_message = None
            
            if status == "failure":
                error_message = f"HTTP {response.status_code}"
            
            # 增强请求数据，添加执行信息
            enhanced_request_data = {
                "method": method,
                "path": path,
                "duration_ms": duration,
                **(request_data or {})
            }
            
            # 对于Tab查询，记录具体的Tab类型
            tab_types = []
            if "/member-base" in path:
                tab_types.append("会员基础")
            if "/member-consume" in path:
                tab_types.append("会员消费")
            if "/member-charge" in path:
                tab_types.append("会员充值")
            if "/coupon-trade" in path:
                tab_types.append("券交易")
            if "/pinzhi-cashier" in path:
                tab_types.append("品智收银")
            
            if tab_types:
                enhanced_request_data["query_tabs"] = tab_types
                enhanced_request_data["query_type"] = "tab_query"
            
            # 记录审计日志
            await self.audit_service.log_action(
                action=action,
                user_id=current_user.get("user_id") if current_user else None,
                username=current_user.get("username") if current_user else None,
                resource=resource,
                resource_id=resource_id,
                request_data=enhanced_request_data,
                status=status,
                error_message=error_message,
                ip_address=request.client.host if request.client else None,
                user_agent=request.headers.get("user-agent")
            )
            
            logger.debug(f"审计日志已记录: {action} - {resource} - {status}")
            
        except Exception as e:
            logger.error(f"记录审计日志时发生错误: {e}")


async def get_audit_middleware(app: ASGIApp) -> AuditMiddleware:
    """获取审计中间件实例"""
    return AuditMiddleware(app)